#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内在动机探索策略示例脚本

展示如何使用内在动机探索策略，让AI在规划阶段探索能够带来更多信息的动作。
"""

import os
import sys
import argparse
import logging
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.intrinsic_motivation import (
    InformationGainMotivation,
    EntropyBasedMotivation,
    CompositeMotivation,
    create_intrinsic_motivation
)
from cardgame_ai.games.common.belief_state import BeliefState
try:
    from cardgame_ai.algorithms.belief_tracking.bayesian_belief import BayesianBeliefTracker
except ImportError:
    # 如果找不到，尝试从其他位置导入
    try:
        from cardgame_ai.games.common.belief_tracking import BayesianBeliefTracker
    except ImportError:
        # 创建一个简单的模拟实现
        class BayesianBeliefTracker:
            def __init__(self, player_id=None):
                self.player_id = player_id

            def get_belief_state(self):
                # 返回一个模拟的信念状态
                return MockBeliefState()

            def update(self, action, public_info=None):
                # 模拟更新
                pass

# 创建一个模拟的信念状态类
class MockBeliefState(BeliefState):
    def __init__(self):
        self.confidence = 0.7
        self.card_probabilities = {
            'A': 0.8, '2': 0.6, '3': 0.4, '4': 0.2, '5': 0.5,
            '6': 0.3, '7': 0.7, '8': 0.9, '9': 0.1, '10': 0.5,
            'J': 0.6, 'Q': 0.4, 'K': 0.2
        }

try:
    from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
    from cardgame_ai.games.doudizhu.state import DouDizhuState
    from cardgame_ai.algorithms.muzero import MuZero
    USE_REAL_ENV = True
except ImportError:
    USE_REAL_ENV = False
    # 创建模拟环境和模型
    class MockEnvironment:
        def __init__(self):
            self.observation_space = MockSpace()
            self.action_space = MockSpace(n=100)

        def reset(self):
            return MockState()

        def get_legal_actions(self, state):
            return [i for i in range(20) if i % 3 != 0]  # 简单地排除一些动作

        def step(self, action):
            next_state = MockState()
            reward = 0.0
            done = False
            info = {"public_info": {}}
            return next_state, reward, done, info

    class MockSpace:
        def __init__(self, n=64):
            self.n = n
            self.shape = (n,)

    class MockState:
        def __init__(self):
            self.current_player = "landlord"

        def get_player_id(self):
            return self.current_player

    class MockModel:
        def __init__(self, state_shape, action_shape, hidden_dim=128, state_dim=64, use_resnet=False):
            self.state_shape = state_shape
            self.action_shape = action_shape
            self.hidden_dim = hidden_dim
            self.state_dim = state_dim
            self.use_resnet = use_resnet

        def represent(self, state):
            return np.random.randn(1, self.state_dim)

        def predict(self, hidden_state):
            policy = np.random.rand(1, self.action_shape[0])
            policy = policy / np.sum(policy)
            value = np.random.randn(1)
            return policy, value

        def dynamics(self, hidden_state, action):
            next_hidden_state = hidden_state + 0.1 * np.random.randn(*hidden_state.shape)
            reward = np.random.randn(1)
            return next_hidden_state, reward

        def load(self, path):
            pass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='内在动机探索策略示例')

    parser.add_argument('--model_path', type=str, default=None,
                        help='MuZero模型路径')
    parser.add_argument('--num_simulations', type=int, default=50,
                        help='MCTS模拟次数')
    parser.add_argument('--motivation_type', type=str, default='information_gain',
                        choices=['information_gain', 'entropy', 'composite'],
                        help='内在动机类型')
    parser.add_argument('--motivation_weight', type=float, default=0.5,
                        help='内在动机权重')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)

    # 创建游戏环境和模型
    if USE_REAL_ENV:
        # 使用真实环境
        env = DouDizhuEnvironment()

        # 获取观察和动作空间
        observation_shape = env.observation_space.shape
        action_shape = (env.action_space.n,)

        # 创建MuZero模型
        model = MuZero(
            state_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=128,
            state_dim=64,
            use_resnet=False
        )

        # 如果有预训练模型，加载参数
        if args.model_path:
            model.load(args.model_path)
            logger.info(f"已加载预训练模型: {args.model_path}")
    else:
        # 使用模拟环境和模型
        logger.info("使用模拟环境和模型进行测试")
        env = MockEnvironment()

        # 获取观察和动作空间
        observation_shape = env.observation_space.shape
        action_shape = (env.action_space.n,)

        # 创建模拟模型
        model = MockModel(
            state_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=128,
            state_dim=64
        )

    # 创建标准MCTS和内在动机MCTS
    standard_mcts = MCTS(
        num_simulations=args.num_simulations,
        use_belief_state=True,
        use_information_value=False,
        use_intrinsic_motivation=False
    )

    # 创建内在动机
    if args.motivation_type == 'composite':
        # 创建组合内在动机
        info_gain = InformationGainMotivation(weight=0.7)
        entropy = EntropyBasedMotivation(weight=0.3)
        intrinsic_motivation = CompositeMotivation([info_gain, entropy])
    else:
        # 创建单一内在动机
        intrinsic_motivation = create_intrinsic_motivation(
            args.motivation_type,
            weight=args.motivation_weight
        )

    # 创建内在动机MCTS
    intrinsic_mcts = MCTS(
        num_simulations=args.num_simulations,
        use_belief_state=True,
        use_information_value=True,
        information_value_weight=0.3,
        use_intrinsic_motivation=True,
        intrinsic_motivation_type=args.motivation_type,
        intrinsic_motivation_weight=args.motivation_weight
    )

    # 手动设置内在动机实例
    intrinsic_mcts.intrinsic_motivation = intrinsic_motivation

    # 创建信念追踪器
    belief_trackers = {}
    for player_id in ["landlord", "peasant1", "peasant2"]:
        belief_trackers[player_id] = BayesianBeliefTracker(player_id=player_id)

    # 重置环境
    state = env.reset()

    # 获取当前玩家ID
    current_player_id = state.current_player

    # 获取合法动作掩码
    legal_actions = env.get_legal_actions(state)
    actions_mask = [i in legal_actions for i in range(env.action_space.n)]

    # 使用标准MCTS进行搜索
    logger.info("使用标准MCTS进行搜索...")
    try:
        # 尝试使用current_player_id参数
        standard_visit_counts, standard_pi, standard_explanation = standard_mcts.run(
            root_state=state,
            model=model,
            temperature=1.0,
            actions_mask=actions_mask,
            belief_trackers=belief_trackers,
            current_player_id=current_player_id,
            explain=True
        )
    except TypeError:
        # 如果失败，尝试不使用current_player_id参数
        standard_visit_counts, standard_pi, standard_explanation = standard_mcts.run(
            root_state=state,
            model=model,
            temperature=1.0,
            actions_mask=actions_mask,
            belief_trackers=belief_trackers,
            explain=True
        )

    # 获取标准MCTS的最佳动作
    standard_best_action = max(standard_visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"标准MCTS最佳动作: {standard_best_action}")

    # 使用内在动机MCTS进行搜索
    logger.info(f"使用内在动机MCTS ({args.motivation_type})进行搜索...")
    try:
        # 尝试使用current_player_id参数
        intrinsic_visit_counts, intrinsic_pi, intrinsic_explanation = intrinsic_mcts.run(
            root_state=state,
            model=model,
            temperature=1.0,
            actions_mask=actions_mask,
            belief_trackers=belief_trackers,
            current_player_id=current_player_id,
            explain=True
        )
    except TypeError:
        # 如果失败，尝试不使用current_player_id参数
        intrinsic_visit_counts, intrinsic_pi, intrinsic_explanation = intrinsic_mcts.run(
            root_state=state,
            model=model,
            temperature=1.0,
            actions_mask=actions_mask,
            belief_trackers=belief_trackers,
            explain=True
        )

    # 获取内在动机MCTS的最佳动作
    intrinsic_best_action = max(intrinsic_visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"内在动机MCTS最佳动作: {intrinsic_best_action}")

    # 分析内在动机的影响
    logger.info("\n内在动机影响分析:")

    # 比较标准MCTS和内在动机MCTS的结果
    if standard_best_action != intrinsic_best_action:
        logger.info(f"最佳动作不同: {standard_best_action} vs {intrinsic_best_action}")

        # 分析访问次数变化
        for action in set(list(standard_visit_counts.keys()) + list(intrinsic_visit_counts.keys())):
            standard_count = standard_visit_counts.get(action, 0)
            intrinsic_count = intrinsic_visit_counts.get(action, 0)

            if standard_count != intrinsic_count:
                count_change = intrinsic_count - standard_count
                logger.info(f"动作 {action} 访问次数变化: {count_change} ({standard_count} -> {intrinsic_count})")
    else:
        logger.info(f"最佳动作相同: {standard_best_action}")

        # 分析策略分布变化
        for action in set(list(standard_pi.keys()) + list(intrinsic_pi.keys())):
            standard_prob = standard_pi.get(action, 0.0)
            intrinsic_prob = intrinsic_pi.get(action, 0.0)

            if abs(standard_prob - intrinsic_prob) > 0.01:
                prob_change = intrinsic_prob - standard_prob
                logger.info(f"动作 {action} 概率变化: {prob_change:.4f} ({standard_prob:.4f} -> {intrinsic_prob:.4f})")

    # 分析内在动机奖励
    logger.info("\n内在动机奖励分析:")

    # 获取内在动机MCTS的顶级动作
    top_actions = intrinsic_explanation.get('top_actions', [])
    for action_info in top_actions:
        action = action_info.get('action')
        intrinsic_bonus = action_info.get('intrinsic_bonus', 0.0)
        info_value = action_info.get('info_value', 0.0)

        logger.info(f"动作 {action}:")
        logger.info(f"  内在奖励: {intrinsic_bonus:.4f}")
        logger.info(f"  信息价值: {info_value:.4f}")
        logger.info(f"  访问次数: {action_info.get('visit_count', 0)}")
        logger.info(f"  策略概率: {action_info.get('policy_prob', 0.0):.4f}")

    # 执行最佳动作
    logger.info(f"\n执行内在动机MCTS的最佳动作: {intrinsic_best_action}")
    try:
        # 尝试执行动作
        next_state, reward, done, info = env.step(intrinsic_best_action)

        # 更新信念追踪器
        for player_id, tracker in belief_trackers.items():
            try:
                tracker.update(intrinsic_best_action, info.get("public_info", {}))
            except Exception as e:
                logger.warning(f"更新信念追踪器失败: {e}")
    except Exception as e:
        logger.warning(f"执行动作失败: {e}")

    # 分析内在动机的影响
    logger.info("\n内在动机对MCTS的影响分析:")

    # 比较访问次数分布
    logger.info("访问次数分布变化:")
    for action in sorted(set(standard_visit_counts.keys()) | set(intrinsic_visit_counts.keys()))[:10]:  # 只显示前10个动作
        std_count = standard_visit_counts.get(action, 0)
        int_count = intrinsic_visit_counts.get(action, 0)
        diff = int_count - std_count
        logger.info(f"  动作 {action}: {std_count} -> {int_count} (差异: {diff:+d})")

    # 分析内在奖励
    if 'intrinsic_motivation_info' in intrinsic_explanation and 'action_bonuses' in intrinsic_explanation['intrinsic_motivation_info']:
        bonuses = intrinsic_explanation['intrinsic_motivation_info']['action_bonuses']
        logger.info("\n内在奖励分析:")
        for action, bonus in sorted(bonuses.items(), key=lambda x: x[1], reverse=True)[:5]:  # 只显示前5个最高奖励
            logger.info(f"  动作 {action}: 内在奖励 {bonus:.4f}")

    logger.info("\n示例完成!")


if __name__ == "__main__":
    main()
