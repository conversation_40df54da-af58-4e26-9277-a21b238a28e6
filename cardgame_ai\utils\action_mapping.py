"""
动作映射工具模块

解决EfficientZero算法中动作索引映射不一致的问题。
提供全局动作编码和动态映射功能。
"""
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import defaultdict

from cardgame_ai.games.doudizhu.action import BidAction, GrabAction, DouDizhuAction
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit

logger = logging.getLogger(__name__)


class ActionMapper:
    """
    动作映射器
    
    解决训练和推理阶段动作索引映射不一致的问题。
    提供全局动作编码和动态映射功能。
    """
    
    def __init__(self, game_type: str = "doudizhu"):
        """
        初始化动作映射器

        Args:
            game_type (str): 游戏类型，默认为"doudizhu"
        """
        self.game_type = game_type
        self.global_action_space = self._build_global_action_space()
        self.action_to_index = {action: idx for idx, action in self.global_action_space.items()}

        # 创建反向映射以提高查找效率
        self.signature_to_index = {}
        for idx, action_name in self.global_action_space.items():
            self.signature_to_index[action_name] = idx

        logger.info(f"ActionMapper初始化完成，全局动作空间大小: {len(self.global_action_space)}")
    
    def _build_global_action_space(self) -> Dict[int, str]:
        """
        构建全局动作空间映射
        
        Returns:
            Dict[int, str]: 索引到动作描述的映射
        """
        action_space = {}
        current_idx = 0
        
        # 1. 叫分动作 (0-3)
        for bid_action in BidAction:
            action_space[current_idx] = f"BID_{bid_action.name}"
            current_idx += 1
        
        # 2. 抢地主动作 (4-5)
        for grab_action in GrabAction:
            action_space[current_idx] = f"GRAB_{grab_action.name}"
            current_idx += 1
        
        # 3. 出牌动作
        # 3.1 PASS动作 (6)
        action_space[current_idx] = "CARD_PASS"
        current_idx += 1
        
        # 3.2 单牌 (7-19) - 3到大王
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE,
                    CardRank.TWO, CardRank.SMALL_JOKER, CardRank.BIG_JOKER]:
            action_space[current_idx] = f"SINGLE_{rank.name}"
            current_idx += 1
        
        # 3.3 对子 (20-31) - 3到2
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE, CardRank.TWO]:
            action_space[current_idx] = f"PAIR_{rank.name}"
            current_idx += 1
        
        # 3.4 三张 (32-43) - 3到2
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE, CardRank.TWO]:
            action_space[current_idx] = f"TRIO_{rank.name}"
            current_idx += 1
        
        # 3.5 炸弹 (44-55) - 3到2
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE, CardRank.TWO]:
            action_space[current_idx] = f"BOMB_{rank.name}"
            current_idx += 1
        
        # 3.6 火箭 (56)
        action_space[current_idx] = "ROCKET"
        current_idx += 1

        # 3.7 三带一 (57-68) - 3到2带任意单牌
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE, CardRank.TWO]:
            action_space[current_idx] = f"TRIO_WITH_SINGLE_{rank.name}"
            current_idx += 1

        # 3.8 三带二 (69-80) - 3到2带任意对子
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE, CardRank.TWO]:
            action_space[current_idx] = f"TRIO_WITH_PAIR_{rank.name}"
            current_idx += 1

        # 3.9 顺子 (81-100) - 长度5到12的顺子，起始点3到A
        for length in range(5, 13):  # 5到12张的顺子
            for start_rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                              CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                              CardRank.JACK, CardRank.QUEEN]:  # 顺子不能包含2和王，最大到A
                if start_rank.value + length - 1 <= CardRank.ACE.value:  # 确保不超过A
                    action_space[current_idx] = f"STRAIGHT_{length}_{start_rank.name}"
                    current_idx += 1

        # 3.10 连对 (101-120) - 长度3到8的连对
        for length in range(3, 9):  # 3到8对的连对
            for start_rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                              CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                              CardRank.JACK, CardRank.QUEEN]:  # 连对不能包含2和王
                if start_rank.value + length - 1 <= CardRank.ACE.value:  # 确保不超过A
                    action_space[current_idx] = f"STRAIGHT_PAIR_{length}_{start_rank.name}"
                    current_idx += 1

        # 3.11 飞机 (121-140) - 长度2到6的飞机
        for length in range(2, 7):  # 2到6个三张的飞机
            for start_rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                              CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                              CardRank.JACK, CardRank.QUEEN]:  # 飞机不能包含2和王
                if start_rank.value + length - 1 <= CardRank.ACE.value:  # 确保不超过A
                    action_space[current_idx] = f"AIRPLANE_{length}_{start_rank.name}"
                    current_idx += 1

        # 3.12 飞机带单牌 (141-160)
        for length in range(2, 7):  # 2到6个三张的飞机带单牌
            for start_rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                              CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                              CardRank.JACK, CardRank.QUEEN]:
                if start_rank.value + length - 1 <= CardRank.ACE.value:
                    action_space[current_idx] = f"AIRPLANE_WITH_SINGLE_{length}_{start_rank.name}"
                    current_idx += 1

        # 3.13 飞机带对子 (161-180)
        for length in range(2, 7):  # 2到6个三张的飞机带对子
            for start_rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                              CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                              CardRank.JACK, CardRank.QUEEN]:
                if start_rank.value + length - 1 <= CardRank.ACE.value:
                    action_space[current_idx] = f"AIRPLANE_WITH_PAIR_{length}_{start_rank.name}"
                    current_idx += 1

        # 3.14 四带二单 (181-192) - 3到2的四张带两张单牌
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE, CardRank.TWO]:
            action_space[current_idx] = f"FOUR_WITH_TWO_SINGLE_{rank.name}"
            current_idx += 1

        # 3.15 四带二对 (193-204) - 3到2的四张带两对
        for rank in [CardRank.THREE, CardRank.FOUR, CardRank.FIVE, CardRank.SIX,
                    CardRank.SEVEN, CardRank.EIGHT, CardRank.NINE, CardRank.TEN,
                    CardRank.JACK, CardRank.QUEEN, CardRank.KING, CardRank.ACE, CardRank.TWO]:
            action_space[current_idx] = f"FOUR_WITH_TWO_PAIR_{rank.name}"
            current_idx += 1

        return action_space
    
    def _get_action_signature(self, action: Any) -> str:
        """
        获取动作的签名字符串

        Args:
            action: 动作对象

        Returns:
            str: 动作签名
        """
        if isinstance(action, BidAction):
            return f"BID_{action.name}"
        elif isinstance(action, GrabAction):
            return f"GRAB_{action.name}"
        elif isinstance(action, CardGroup):
            if action.card_type == CardGroupType.PASS:
                return "CARD_PASS"
            elif action.card_type == CardGroupType.SINGLE:
                return f"SINGLE_{action.cards[0].rank.name}"
            elif action.card_type == CardGroupType.PAIR:
                return f"PAIR_{action.cards[0].rank.name}"
            elif action.card_type == CardGroupType.TRIO:
                return f"TRIO_{action.cards[0].rank.name}"
            elif action.card_type == CardGroupType.BOMB:
                return f"BOMB_{action.cards[0].rank.name}"
            elif action.card_type == CardGroupType.ROCKET:
                return "ROCKET"
            elif action.card_type == CardGroupType.TRIO_WITH_SINGLE:
                # 三带一：使用三张的点数
                main_rank = action.main_rank
                return f"TRIO_WITH_SINGLE_{main_rank.name}"
            elif action.card_type == CardGroupType.TRIO_WITH_PAIR:
                # 三带二：使用三张的点数
                main_rank = action.main_rank
                return f"TRIO_WITH_PAIR_{main_rank.name}"
            elif action.card_type == CardGroupType.STRAIGHT:
                # 顺子：长度_起始点数
                length = len(action.cards)
                start_rank = min(card.rank for card in action.cards)
                return f"STRAIGHT_{length}_{start_rank.name}"
            elif action.card_type == CardGroupType.STRAIGHT_PAIR:
                # 连对：长度_起始点数
                length = len(action.cards) // 2  # 对数
                start_rank = min(card.rank for card in action.cards)
                return f"STRAIGHT_PAIR_{length}_{start_rank.name}"
            elif action.card_type == CardGroupType.AIRPLANE:
                # 飞机：长度_起始点数
                length = len(action.cards) // 3  # 三张的个数
                start_rank = action.main_rank
                return f"AIRPLANE_{length}_{start_rank.name}"
            elif action.card_type == CardGroupType.AIRPLANE_WITH_SINGLE:
                # 飞机带单牌：长度_起始点数
                # 需要计算三张的个数
                from collections import Counter
                counter = Counter([card.rank for card in action.cards])
                trio_count = sum(1 for count in counter.values() if count == 3)
                start_rank = action.main_rank
                return f"AIRPLANE_WITH_SINGLE_{trio_count}_{start_rank.name}"
            elif action.card_type == CardGroupType.AIRPLANE_WITH_PAIR:
                # 飞机带对子：长度_起始点数
                from collections import Counter
                counter = Counter([card.rank for card in action.cards])
                trio_count = sum(1 for count in counter.values() if count == 3)
                start_rank = action.main_rank
                return f"AIRPLANE_WITH_PAIR_{trio_count}_{start_rank.name}"
            elif action.card_type == CardGroupType.FOUR_WITH_TWO_SINGLE:
                # 四带二单：使用四张的点数
                main_rank = action.main_rank
                return f"FOUR_WITH_TWO_SINGLE_{main_rank.name}"
            elif action.card_type == CardGroupType.FOUR_WITH_TWO_PAIR:
                # 四带二对：使用四张的点数
                main_rank = action.main_rank
                return f"FOUR_WITH_TWO_PAIR_{main_rank.name}"
            else:
                # 其他复杂牌型用通用签名
                return f"COMPLEX_{action.card_type.name}_{len(action.cards)}"
        else:
            return f"UNKNOWN_{type(action).__name__}"
    
    def map_to_global_index(self, action: Any) -> int:
        """
        将动作映射到全局索引

        Args:
            action: 动作对象

        Returns:
            int: 全局索引，如果无法映射则返回-1
        """
        signature = self._get_action_signature(action)

        # 首先尝试精确匹配
        if signature in self.signature_to_index:
            return self.signature_to_index[signature]

        # 如果找不到精确匹配，尝试模糊匹配
        if isinstance(action, CardGroup):
            fuzzy_match = self._fuzzy_match_card_group(action, signature)
            if fuzzy_match != -1:
                return fuzzy_match

        logger.warning(f"无法找到动作 {signature} 的全局索引映射")
        return -1

    def _fuzzy_match_card_group(self, action: CardGroup, signature: str) -> int:
        """
        对CardGroup进行模糊匹配

        Args:
            action: CardGroup动作
            signature: 动作签名

        Returns:
            int: 匹配的全局索引，如果无法匹配则返回-1
        """
        # 对于复杂牌型，尝试找到最相似的动作
        card_type = action.card_type

        # 如果是复杂牌型，尝试匹配基础牌型
        if card_type in [CardGroupType.TRIO_WITH_SINGLE, CardGroupType.TRIO_WITH_PAIR]:
            # 尝试匹配对应的三张
            trio_signature = f"TRIO_{action.main_rank.name}"
            if trio_signature in self.signature_to_index:
                logger.debug(f"模糊匹配: {signature} -> {trio_signature}")
                return self.signature_to_index[trio_signature]

        elif card_type in [CardGroupType.FOUR_WITH_TWO_SINGLE, CardGroupType.FOUR_WITH_TWO_PAIR]:
            # 尝试匹配对应的炸弹
            bomb_signature = f"BOMB_{action.main_rank.name}"
            if bomb_signature in self.signature_to_index:
                logger.debug(f"模糊匹配: {signature} -> {bomb_signature}")
                return self.signature_to_index[bomb_signature]

        elif card_type in [CardGroupType.STRAIGHT, CardGroupType.STRAIGHT_PAIR,
                          CardGroupType.AIRPLANE, CardGroupType.AIRPLANE_WITH_SINGLE,
                          CardGroupType.AIRPLANE_WITH_PAIR]:
            # 对于顺子类牌型，尝试匹配最小的单牌
            min_rank = min(card.rank for card in action.cards)
            single_signature = f"SINGLE_{min_rank.name}"
            if single_signature in self.signature_to_index:
                logger.debug(f"模糊匹配: {signature} -> {single_signature}")
                return self.signature_to_index[single_signature]

        return -1

    def map_from_global_index(self, index: int, legal_actions: List[Any]) -> Tuple[Any, bool]:
        """
        从全局索引映射到合法动作
        
        Args:
            index (int): 全局动作索引
            legal_actions (List[Any]): 当前合法动作列表
            
        Returns:
            Tuple[Any, bool]: (选择的动作, 是否成功映射)
        """
        if index < 0 or index >= len(self.global_action_space):
            logger.warning(f"动作索引 {index} 超出全局动作空间范围")
            return None, False
        
        target_signature = self.global_action_space[index]
        
        # 在合法动作中查找匹配的动作
        for action in legal_actions:
            if self._get_action_signature(action) == target_signature:
                logger.debug(f"成功映射: 索引 {index} -> {target_signature} -> {action}")
                return action, True
        
        # 如果没有找到精确匹配，尝试智能匹配
        best_match = self._find_intelligent_match(target_signature, legal_actions)
        if best_match is not None:
            logger.debug(f"智能映射成功: 索引 {index} -> {target_signature} -> {self._get_action_signature(best_match)}")
            return best_match, True

        logger.warning(f"无法映射动作索引 {index} ({target_signature}) 到合法动作列表")
        return None, False

    def _find_intelligent_match(self, target_signature: str, legal_actions: List[Any]) -> Any:
        """
        智能匹配算法，尝试找到最佳的替代动作

        Args:
            target_signature: 目标动作签名
            legal_actions: 合法动作列表

        Returns:
            Any: 最佳匹配的动作，如果没有找到则返回None
        """
        # 策略1: 动作类型不匹配时的跨阶段处理
        if target_signature.startswith("BID_") or target_signature.startswith("GRAB_"):
            # 如果目标是叫分/抢地主动作，但当前是出牌阶段，选择PASS
            for action in legal_actions:
                if isinstance(action, CardGroup) and action.card_type == CardGroupType.PASS:
                    return action

        # 策略2: 基础牌型的点数匹配
        if target_signature.startswith(("SINGLE_", "PAIR_", "TRIO_", "BOMB_")):
            target_parts = target_signature.split("_")
            if len(target_parts) >= 2:
                target_type = target_parts[0]
                target_rank = target_parts[1]

                # 寻找相同点数的动作
                for action in legal_actions:
                    action_sig = self._get_action_signature(action)
                    if action_sig.endswith(f"_{target_rank}"):
                        return action

                # 如果找不到相同点数，寻找相同类型的动作
                for action in legal_actions:
                    action_sig = self._get_action_signature(action)
                    if action_sig.startswith(f"{target_type}_"):
                        return action

        # 策略3: 复杂牌型的降级匹配
        if target_signature.startswith(("TRIO_WITH_", "STRAIGHT_", "AIRPLANE_", "FOUR_WITH_")):
            # 尝试匹配基础牌型
            if "TRIO_WITH_" in target_signature:
                # 三带一/三带二 -> 三张
                parts = target_signature.split("_")
                if len(parts) >= 3:
                    rank = parts[2]
                    trio_sig = f"TRIO_{rank}"
                    for action in legal_actions:
                        if self._get_action_signature(action) == trio_sig:
                            return action

            elif "FOUR_WITH_" in target_signature:
                # 四带二 -> 炸弹
                parts = target_signature.split("_")
                if len(parts) >= 3:
                    rank = parts[2]
                    bomb_sig = f"BOMB_{rank}"
                    for action in legal_actions:
                        if self._get_action_signature(action) == bomb_sig:
                            return action

            elif target_signature.startswith("STRAIGHT_") or target_signature.startswith("AIRPLANE_"):
                # 顺子/飞机 -> 单张（使用最小的牌）
                parts = target_signature.split("_")
                if len(parts) >= 3:
                    start_rank = parts[2]
                    single_sig = f"SINGLE_{start_rank}"
                    for action in legal_actions:
                        if self._get_action_signature(action) == single_sig:
                            return action

        # 策略4: 如果所有策略都失败，选择PASS（如果可用）
        for action in legal_actions:
            if isinstance(action, CardGroup) and action.card_type == CardGroupType.PASS:
                return action

        # 策略5: 最后的备选方案，选择第一个合法动作
        if legal_actions:
            return legal_actions[0]

        return None

    def get_action_distribution_info(self, legal_actions: List[Any]) -> Dict[str, int]:
        """
        获取合法动作的分布信息，用于调试
        
        Args:
            legal_actions (List[Any]): 合法动作列表
            
        Returns:
            Dict[str, int]: 动作类型分布
        """
        distribution = defaultdict(int)
        for action in legal_actions:
            signature = self._get_action_signature(action)
            action_type = signature.split("_")[0]
            distribution[action_type] += 1
        
        return dict(distribution)

    def validate_mapping_consistency(self, legal_actions: List[Any]) -> bool:
        """
        验证动作映射的一致性

        Args:
            legal_actions (List[Any]): 合法动作列表

        Returns:
            bool: 映射是否一致
        """
        inconsistent_count = 0
        total_count = len(legal_actions)

        for action in legal_actions:
            # 正向映射：动作 -> 索引
            global_idx = self.map_to_global_index(action)
            if global_idx == -1:
                inconsistent_count += 1
                logger.warning(f"动作无法映射到全局索引: {action}")
                continue

            # 反向映射：索引 -> 动作
            mapped_action, success = self.map_from_global_index(global_idx, legal_actions)
            if not success or mapped_action != action:
                inconsistent_count += 1
                logger.warning(f"反向映射不一致: {action} -> {global_idx} -> {mapped_action}")

        consistency_rate = (total_count - inconsistent_count) / total_count if total_count > 0 else 1.0
        logger.info(f"动作映射一致性: {consistency_rate:.2%} ({total_count - inconsistent_count}/{total_count})")

        return inconsistent_count == 0

    def get_mapping_statistics(self) -> Dict[str, Any]:
        """
        获取映射统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_global_actions": len(self.global_action_space),
            "bid_actions": 4,  # BidAction.PASS, BID_1, BID_2, BID_3
            "grab_actions": 2,  # GrabAction.PASS, GRAB
            "card_actions": len(self.global_action_space) - 6,  # 总数减去叫分和抢地主
            "game_type": self.game_type
        }
