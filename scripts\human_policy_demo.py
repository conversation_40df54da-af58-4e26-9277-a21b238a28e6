#!/usr/bin/env python
"""
人类策略网络演示脚本

展示如何使用训练好的人类策略网络在斗地主游戏中模拟人类玩家行为。
"""

import os
import sys
import argparse
import logging
import numpy as np
import time
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cardgame_ai.algorithms.opponent_modeling.human_policy import HumanPolicyAgent
from cardgame_ai.core.agent import RandomAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="人类策略网络演示脚本")
    
    # 模型相关参数
    parser.add_argument('--model_path', type=str, default='models/human_policy/human_policy_final.pt',
                        help="人类策略网络模型路径")
    parser.add_argument('--device', type=str, default=None,
                        help="计算设备 (cuda或cpu)")
    
    # 代理参数
    parser.add_argument('--exploration_rate', type=float, default=0.1,
                        help="探索率")
    parser.add_argument('--temperature', type=float, default=1.0,
                        help="温度参数")
    
    # 游戏参数
    parser.add_argument('--num_games', type=int, default=5,
                        help="游戏局数")
    parser.add_argument('--random_seed', type=int, default=None,
                        help="随机种子")
    parser.add_argument('--use_human_policy', type=int, default=1,
                        help="使用人类策略的玩家索引(0:地主, 1和2:农民), -1表示全部使用人类策略")
    
    # 显示参数
    parser.add_argument('--verbose', action='store_true',
                        help="是否显示详细游戏过程")
    parser.add_argument('--delay', type=float, default=0.5,
                        help="每步延迟时间(秒)，便于观察")
    
    return parser.parse_args()


def setup_agents(env, use_human_policy, model_path, device, exploration_rate, temperature, verbose):
    """
    设置代理
    
    Args:
        env: 游戏环境
        use_human_policy: 使用人类策略的玩家索引
        model_path: 模型路径
        device: 计算设备
        exploration_rate: 探索率
        temperature: 温度参数
        verbose: 是否显示详细信息
        
    Returns:
        代理列表
    """
    agents = []
    
    # 创建3个代理（地主和两个农民）
    for i in range(3):
        if use_human_policy == -1 or use_human_policy == i:
            # 使用人类策略网络代理
            agent = HumanPolicyAgent(
                model_path=model_path,
                device=device,
                exploration_rate=exploration_rate,
                temperature=temperature,
                verbose=verbose
            )
            agents.append(agent)
            logger.info(f"玩家{i} 使用人类策略网络代理")
        else:
            # 使用随机代理
            agent = RandomAgent()
            agents.append(agent)
            logger.info(f"玩家{i} 使用随机代理")
    
    return agents


def print_state(state, player_names):
    """打印游戏状态"""
    print("\n" + "="*50)
    print(f"当前阶段: {state.game_phase.name}")
    
    if state.landlord is not None:
        print(f"地主: {player_names[state.landlord]}")
    else:
        print("地主: 未确定")
    
    print(f"当前玩家: {player_names[state.current_player]}")
    
    for i, hand in enumerate(state.hands):
        print(f"{player_names[i]}的手牌({len(hand)}张): {' '.join(str(card) for card in sorted(hand))}")
    
    if state.last_move and state.last_move.cards:
        print(f"上一手牌: {state.last_move} (由{player_names[state.last_player]}出)")
    
    if state.num_passes > 0:
        print(f"连续不出次数: {state.num_passes}")
    
    print("="*50)


def simulate_game(env, agents, verbose=False, delay=0.5):
    """
    模拟一局游戏
    
    Args:
        env: 游戏环境
        agents: 代理列表
        verbose: 是否显示详细过程
        delay: 每步延迟时间
        
    Returns:
        游戏结果
    """
    # 重置环境
    observation = env.reset()
    done = False
    
    # 玩家名称
    player_names = ["地主", "农民1", "农民2"]
    
    # 记录每一步
    steps = 0
    
    if verbose:
        print("\n开始新游戏")
        print_state(env.state, player_names)
    
    # 游戏循环
    while not done:
        # 获取当前玩家
        current_player = env.state.current_player
        agent = agents[current_player]
        
        # 获取合法动作
        legal_actions = env.state.get_legal_actions()
        
        # 选择动作
        action = agent.act(observation, legal_actions)
        
        if verbose:
            player_name = player_names[current_player]
            if action.cards:
                print(f"\n{player_name} 出牌: {action}")
            else:
                print(f"\n{player_name} 不出")
        
        # 执行动作
        observation, reward, done, info = env.step(action)
        
        if verbose:
            print_state(env.state, player_names)
            time.sleep(delay)
        
        steps += 1
    
    # 游戏结束
    winner = "地主" if reward[env.state.landlord] > 0 else "农民"
    
    if verbose:
        print("\n游戏结束!")
        print(f"胜利方: {winner}")
        print(f"游戏步数: {steps}")
        print(f"奖励: {reward}")
    
    return {
        "winner": winner,
        "steps": steps,
        "reward": reward,
    }


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.random_seed is not None:
        random.seed(args.random_seed)
        np.random.seed(args.random_seed)
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 设置代理
    agents = setup_agents(
        env=env,
        use_human_policy=args.use_human_policy,
        model_path=args.model_path,
        device=args.device,
        exploration_rate=args.exploration_rate,
        temperature=args.temperature,
        verbose=args.verbose
    )
    
    # 游戏统计
    stats = {
        "landlord_wins": 0,
        "farmer_wins": 0,
        "total_steps": 0,
    }
    
    # 模拟多局游戏
    logger.info(f"开始模拟{args.num_games}局游戏")
    
    for game_id in range(args.num_games):
        logger.info(f"正在模拟第{game_id+1}局游戏...")
        
        result = simulate_game(
            env=env,
            agents=agents,
            verbose=args.verbose,
            delay=args.delay
        )
        
        # 更新统计信息
        if result["winner"] == "地主":
            stats["landlord_wins"] += 1
        else:
            stats["farmer_wins"] += 1
        
        stats["total_steps"] += result["steps"]
        
        logger.info(f"第{game_id+1}局游戏结束, 胜利方: {result['winner']}, 步数: {result['steps']}")
    
    # 打印统计信息
    print("\n游戏统计:")
    print(f"总局数: {args.num_games}")
    print(f"地主胜利: {stats['landlord_wins']} 局 ({stats['landlord_wins'] / args.num_games * 100:.1f}%)")
    print(f"农民胜利: {stats['farmer_wins']} 局 ({stats['farmer_wins'] / args.num_games * 100:.1f}%)")
    print(f"平均步数: {stats['total_steps'] / args.num_games:.1f}")
    
    logger.info("演示脚本执行完毕")


if __name__ == "__main__":
    main() 