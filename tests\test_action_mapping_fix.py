"""
动作映射修复测试模块

测试ActionMapper的功能和EfficientZero中动作选择的修复效果。
"""
import sys
import os
import logging
import unittest
from typing import List, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from cardgame_ai.utils.action_mapping import ActionMapper
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.games.doudizhu.card import Card, CardRank, CardSuit

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestActionMapping(unittest.TestCase):
    """动作映射测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.action_mapper = ActionMapper(game_type="doudizhu")
        self.env = DouDizhuEnvironment()
    
    def test_action_mapper_initialization(self):
        """测试ActionMapper初始化"""
        self.assertIsNotNone(self.action_mapper)
        self.assertGreater(len(self.action_mapper.global_action_space), 0)
        logger.info(f"全局动作空间大小: {len(self.action_mapper.global_action_space)}")
    
    def test_bid_action_mapping(self):
        """测试叫分动作映射"""
        bid_actions = [BidAction.PASS, BidAction.BID_1, BidAction.BID_2, BidAction.BID_3]
        
        for action in bid_actions:
            # 正向映射
            global_idx = self.action_mapper.map_to_global_index(action)
            self.assertNotEqual(global_idx, -1, f"叫分动作 {action} 无法映射到全局索引")
            
            # 反向映射
            mapped_action, success = self.action_mapper.map_from_global_index(global_idx, bid_actions)
            self.assertTrue(success, f"全局索引 {global_idx} 无法映射回叫分动作")
            self.assertEqual(mapped_action, action, f"映射不一致: {action} -> {global_idx} -> {mapped_action}")
    
    def test_grab_action_mapping(self):
        """测试抢地主动作映射"""
        grab_actions = [GrabAction.PASS, GrabAction.GRAB]
        
        for action in grab_actions:
            # 正向映射
            global_idx = self.action_mapper.map_to_global_index(action)
            self.assertNotEqual(global_idx, -1, f"抢地主动作 {action} 无法映射到全局索引")
            
            # 反向映射
            mapped_action, success = self.action_mapper.map_from_global_index(global_idx, grab_actions)
            self.assertTrue(success, f"全局索引 {global_idx} 无法映射回抢地主动作")
            self.assertEqual(mapped_action, action, f"映射不一致: {action} -> {global_idx} -> {mapped_action}")
    
    def test_card_group_mapping(self):
        """测试出牌动作映射"""
        # 创建一些基本的出牌动作
        test_actions = [
            CardGroup([]),  # PASS
            CardGroup([Card(CardRank.THREE, CardSuit.HEART)]),  # 单张3
            CardGroup([Card(CardRank.KING, CardSuit.HEART)]),  # 单张K
            CardGroup([Card(CardRank.THREE, CardSuit.HEART), Card(CardRank.THREE, CardSuit.SPADE)]),  # 对子3
        ]
        
        for action in test_actions:
            # 正向映射
            global_idx = self.action_mapper.map_to_global_index(action)
            if action.card_type in [CardGroupType.PASS, CardGroupType.SINGLE, CardGroupType.PAIR]:
                self.assertNotEqual(global_idx, -1, f"出牌动作 {action} 无法映射到全局索引")
                
                # 反向映射
                mapped_action, success = self.action_mapper.map_from_global_index(global_idx, test_actions)
                if success:
                    # 验证映射的动作类型是否一致
                    self.assertEqual(mapped_action.card_type, action.card_type, 
                                   f"映射的动作类型不一致: {action.card_type} -> {mapped_action.card_type}")
    
    def test_mapping_consistency(self):
        """测试映射一致性"""
        # 重置环境并获取合法动作
        state = self.env.reset()
        legal_actions = self.env.get_legal_actions(state)
        
        # 验证映射一致性
        is_consistent = self.action_mapper.validate_mapping_consistency(legal_actions)
        logger.info(f"映射一致性验证结果: {is_consistent}")
        
        # 获取分布信息
        distribution = self.action_mapper.get_action_distribution_info(legal_actions)
        logger.info(f"动作分布: {distribution}")
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试非法索引
        action, success = self.action_mapper.map_from_global_index(-1, [BidAction.PASS])
        self.assertFalse(success, "负索引应该映射失败")
        
        action, success = self.action_mapper.map_from_global_index(99999, [BidAction.PASS])
        self.assertFalse(success, "超大索引应该映射失败")
        
        # 测试空的合法动作列表
        action, success = self.action_mapper.map_from_global_index(0, [])
        self.assertFalse(success, "空的合法动作列表应该映射失败")


class TestGameIntegration(unittest.TestCase):
    """游戏集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.env = DouDizhuEnvironment()
        self.action_mapper = ActionMapper(game_type="doudizhu")
    
    def test_complete_game_flow(self):
        """测试完整游戏流程"""
        state = self.env.reset()
        step_count = 0
        max_steps = 100  # 防止无限循环
        
        while not state.is_terminal() and step_count < max_steps:
            legal_actions = self.env.get_legal_actions(state)
            self.assertGreater(len(legal_actions), 0, f"步骤 {step_count}: 没有合法动作")
            
            # 模拟模型输出一个随机的全局动作索引
            import random
            global_action_idx = random.randint(0, len(self.action_mapper.global_action_space) - 1)
            
            # 使用动作映射器选择动作
            action, is_valid = self.action_mapper.map_from_global_index(global_action_idx, legal_actions)
            
            if not is_valid:
                # 如果映射失败，随机选择一个合法动作
                action = random.choice(legal_actions)
            
            # 执行动作
            try:
                next_state, reward, done, info = self.env.step(action)
                state = next_state
                step_count += 1
                
                logger.debug(f"步骤 {step_count}: 动作 {action}, 奖励 {reward}")
                
            except Exception as e:
                self.fail(f"步骤 {step_count} 执行动作时出错: {e}")
        
        logger.info(f"游戏完成，总步数: {step_count}")
        self.assertLessEqual(step_count, max_steps, "游戏步数超过最大限制")
    
    def test_action_validation(self):
        """测试动作验证"""
        state = self.env.reset()
        legal_actions = self.env.get_legal_actions(state)
        
        # 测试所有合法动作都能正确执行
        for i, action in enumerate(legal_actions[:5]):  # 只测试前5个动作
            try:
                # 创建状态副本进行测试
                test_state = state
                next_state, reward, done, info = self.env.step(action)
                logger.debug(f"动作 {i}: {action} 执行成功")
            except Exception as e:
                self.fail(f"合法动作 {action} 执行失败: {e}")


def run_action_mapping_tests():
    """运行动作映射测试"""
    logger.info("开始运行动作映射修复测试...")

    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试用例 - 使用TestLoader替代makeSuite
    loader = unittest.TestLoader()
    test_suite.addTest(loader.loadTestsFromTestCase(TestActionMapping))
    test_suite.addTest(loader.loadTestsFromTestCase(TestGameIntegration))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        logger.info("✅ 所有测试通过！动作映射修复成功。")
        return True
    else:
        logger.error(f"❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        for failure in result.failures:
            logger.error(f"失败: {failure[0]} - {failure[1]}")
        for error in result.errors:
            logger.error(f"错误: {error[0]} - {error[1]}")
        return False


if __name__ == "__main__":
    success = run_action_mapping_tests()
    sys.exit(0 if success else 1)
