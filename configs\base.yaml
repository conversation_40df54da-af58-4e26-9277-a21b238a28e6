# ============================================================================
# 斗地主AI优化项目 - 基础配置文件
# ============================================================================
#
# 【文件用途】
# 此文件是整个斗地主AI训练系统的核心配置文件，定义了所有模块的基础参数。
# 所有其他配置文件都会继承这里的设置，是系统运行的基础。
#
# 【配置层次】
# base.yaml (基础配置) -> training/efficient_zero.yaml (算法配置) -> 运行时参数
#
# 【修改建议】
# - 性能优化主要修改: device、data、performance 部分
# - 调试问题主要修改: logging、debug 部分
# - 分布式训练修改: distributed 部分
# ============================================================================

defaults:
  - training: efficient_zero    # 默认使用EfficientZero算法配置
  - evaluation: benchmark       # 默认使用基准评估配置
  - _self_                     # 当前文件优先级最高

# ============================================================================
# 项目基本信息
# ============================================================================
project:
  name: "doudizhu_ai_optimization"                                    # 项目名称，用于日志和监控
  version: "1.0.0"                                                   # 版本号，用于模型版本管理
  description: "斗地主AI优化项目 - 超人类水平的多智能体协作AI系统"        # 项目描述
  author: "AI Development Team"                                       # 开发团队

# ============================================================================
# 设备配置 - 影响训练性能的关键配置
# ============================================================================
device:
  # 【设备类型】
  # - "cuda": 强制使用GPU (推荐，性能最佳)
  # - "cpu": 强制使用CPU (调试用，性能较差)
  # - "auto": 自动检测 (有GPU用GPU，无GPU用CPU)
  type: "cuda"

  # 【GPU设备列表】
  # 指定使用哪些GPU设备，支持多GPU训练
  # - [0]: 使用第一块GPU (单GPU训练，推荐)
  # - [0, 1]: 使用前两块GPU (双GPU训练)
  # - [0, 1, 2, 3]: 使用四块GPU (四GPU训练，需要大量显存)
  # 【性能影响】单GPU通常比多GPU更稳定，除非数据量特别大
  ids: [0]

  # 【混合精度训练】
  # 使用FP16代替FP32，可以显著提升训练速度并节省显存
  # - true: 启用混合精度 (推荐，速度提升30-50%)
  # - false: 使用标准FP32 (精度更高但速度慢)
  # 【代码位置】cardgame_ai/algorithms/efficient_zero.py:2388
  mixed_precision: true

  # 【确定性算法】
  # 控制是否使用确定性算法，影响训练的可重复性
  # - false: 使用非确定性算法 (推荐，性能更好)
  # - true: 使用确定性算法 (结果可重复但性能较差)
  deterministic: false

  # 【cuDNN基准测试】
  # 启用cuDNN的自动调优功能，寻找最优的卷积算法
  # - true: 启用基准测试 (推荐，首次运行慢但后续快)
  # - false: 禁用基准测试 (每次运行速度一致)
  # 【代码位置】torch.backends.cudnn.benchmark = True
  benchmark: true

# ============================================================================
# 日志配置 - 调试和监控的重要配置
# ============================================================================
logging:
  # 【日志级别】
  # - "DEBUG": 最详细的日志，包含所有调试信息 (调试时使用)
  # - "INFO": 一般信息日志 (推荐，正常训练使用)
  # - "WARNING": 警告信息 (生产环境推荐)
  # - "ERROR": 错误信息 (仅记录错误)
  # - "CRITICAL": 严重错误 (最少日志)
  level: "INFO"

  # 【日志目录】日志文件保存位置
  dir: "logs"

  # 【日志格式】
  # - "json": 结构化JSON格式 (推荐，便于分析)
  # - "text": 纯文本格式 (便于阅读)
  format: "json"

  # 【日志轮转】防止日志文件过大
  rotation: "1 day"      # 每天创建新日志文件
  retention: "30 days"   # 保留30天的日志

  # 【结构化日志】启用后可以更好地分析训练过程
  structured: true

  # 【组件日志级别】为不同模块设置不同的日志级别
  components:
    training: "INFO"      # 训练过程日志
    evaluation: "INFO"    # 评估过程日志
    algorithms: "DEBUG"   # 算法详细日志 (调试用)
    distributed: "INFO"   # 分布式训练日志
    monitoring: "INFO"    # 监控系统日志

# ============================================================================
# 数据配置 - 影响数据加载性能的关键配置
# ============================================================================
data:
  # 【数据目录结构】
  root_dir: "data"                              # 数据根目录
  cache_dir: "${data.root_dir}/cache"           # 缓存目录，存储预处理数据
  processed_dir: "${data.root_dir}/processed"   # 处理后数据目录
  raw_dir: "${data.root_dir}/raw"               # 原始数据目录

  # 【数据格式配置】
  # - "hdf5": HDF5格式 (推荐，读取速度快，压缩率高)
  # - "pickle": Python pickle格式 (兼容性好)
  # - "json": JSON格式 (可读性好但速度慢)
  format: "hdf5"
  compression: "gzip"    # 压缩算法，节省存储空间

  # 【数据加载性能配置】- 这是性能优化的重点！
  # 【num_workers】数据加载线程数
  # - 4: 默认值，适合大多数情况
  # - 8-12: 推荐值，可以显著提升数据加载速度
  # - 16+: 高端CPU可以使用，但要注意内存消耗
  # 【代码位置】cardgame_ai/utils/data_loader.py:143
  # 【性能影响】增加到8-12可以减少GPU等待时间
  num_workers: 12

  # 【pin_memory】内存锁定，加速GPU数据传输
  # - true: 启用内存锁定 (推荐，GPU训练必须启用)
  # - false: 禁用内存锁定 (CPU训练时使用)
  # 【代码位置】cardgame_ai/algorithms/compute_optimization.py:456
  pin_memory: true

  # 【prefetch_factor】预取因子，控制预加载的批次数量
  # - 2: 默认值，预加载2个批次
  # - 4-6: 推荐值，可以进一步减少等待时间
  # - 8+: 高内存系统可以使用，但要注意内存消耗
  # 【性能影响】增加到4-6可以让GPU更少等待数据
  prefetch_factor: 6

# 模型配置
model:
  save_dir: "models"
  checkpoint_dir: "${model.save_dir}/checkpoints"
  pretrained_dir: "${model.save_dir}/pretrained"
  export_dir: "${model.save_dir}/exports"
  
  # 检查点管理
  save_frequency: 1000  # 每N步保存一次
  keep_last: 5  # 保留最近N个检查点
  save_best: true  # 保存最佳性能模型
  
  # 模型版本控制
  versioning: true
  metadata: true  # 保存模型元数据

# 分布式配置
distributed:
  backend: "ray"  # ray, torch
  
  # Ray配置
  ray:
    address: null  # Ray集群地址，null表示本地模式
    runtime_env:
      pip: []
      env_vars: {}
    resources:
      num_cpus: null  # null表示使用所有可用CPU
      num_gpus: null  # null表示使用所有可用GPU
      memory: null
      object_store_memory: null
  
  # 通信配置
  communication:
    timeout: 300  # 通信超时时间(秒)
    retry_attempts: 3
    compression: true

# 监控配置
monitoring:
  enabled: true
  
  # TensorBoard
  tensorboard:
    enabled: true
    log_dir: "${logging.dir}/tensorboard"
    update_frequency: 100  # 每N步更新一次
  
  # Weights & Biases (可选)
  wandb:
    enabled: false
    project: "${project.name}"
    entity: null
    tags: []
    notes: ""
  
  # 系统监控
  system:
    enabled: true
    interval: 10  # 监控间隔(秒)
    metrics:
      - cpu_usage
      - memory_usage
      - gpu_usage
      - gpu_memory
      - disk_usage
      - network_io

# 实验配置
experiment:
  name: "default"
  tags: []
  notes: ""
  
  # 随机种子
  seed: 42
  deterministic: false
  
  # 实验跟踪
  tracking:
    enabled: true
    save_config: true
    save_code: true
    save_artifacts: true

# ============================================================================
# 性能配置 - 训练性能优化的核心配置
# ============================================================================
performance:
  # 【内存优化配置】
  memory:
    # 【梯度累积步数】当显存不足时使用
    # - 1: 不使用梯度累积 (推荐，显存充足时)
    # - 2-4: 显存不足时使用，等效于增大batch_size
    # 【代码位置】configs/training/efficient_zero.yaml:86
    gradient_accumulation: 1

    # 【最大内存使用率】控制系统内存使用上限
    # - 0.9: 使用90%的系统内存 (默认值)
    # - 0.95: 使用95%的系统内存 (推荐，更充分利用内存)
    # - 0.8: 使用80%的系统内存 (保守设置)
    max_memory_usage: 0.95

    # 【缓存清理频率】定期清理GPU缓存，防止内存泄漏
    # - 1000: 每1000步清理一次 (默认)
    # - 500: 更频繁清理 (内存紧张时)
    # - 2000: 较少清理 (内存充足时)
    clear_cache_frequency: 1000

  # 【计算优化配置】
  compute:
    # 【模型编译】PyTorch 2.0+的新特性，可以显著提升性能
    # - false: 禁用编译 (默认，兼容性好)
    # - true: 启用编译 (推荐，性能提升20-30%)
    # 【代码位置】cardgame_ai/integrated_system.py:1359
    compile_model: true

    # 【Flash Attention】高效的注意力机制实现
    # - false: 使用标准注意力 (默认)
    # - true: 使用Flash Attention (内存效率更高)
    use_flash_attention: false

    # 【梯度检查点】用内存换时间的技术
    # - false: 禁用梯度检查点 (推荐，显存充足时)
    # - true: 启用梯度检查点 (显存不足时使用)
    # 【代码位置】cardgame_ai/integrated_system.py:174
    gradient_checkpointing: false

  # 【数据加载优化配置】
  dataloader:
    # 【持久化工作进程】保持数据加载进程存活，避免重复创建
    # - true: 启用持久化 (推荐，减少进程创建开销)
    # - false: 禁用持久化 (每个epoch重新创建进程)
    # 【代码位置】configs/base.yaml:158
    persistent_workers: true

    # 【多进程上下文】
    # - "spawn": 创建新进程 (推荐，更稳定)
    # - "fork": 复制进程 (Linux下更快但可能不稳定)
    multiprocessing_context: "spawn"

# 安全配置
security:
  # 输入验证
  validation:
    strict_mode: true
    max_input_size: 1048576  # 1MB
  
  # 资源限制
  limits:
    max_memory_per_process: "8GB"
    max_gpu_memory_per_process: "16GB"
    max_training_time: "24h"
  
  # 审计日志
  audit:
    enabled: true
    log_level: "INFO"

# 调试配置
debug:
  enabled: false
  
  # 性能分析
  profiling:
    enabled: false
    output_dir: "${logging.dir}/profiling"
    profile_memory: true
    profile_gpu: true
  
  # 调试选项
  options:
    detect_anomaly: false  # PyTorch异常检测
    warn_on_missing_keys: true
    strict_loading: true

# 环境特定配置覆盖
env_overrides:
  development:
    logging.level: "DEBUG"
    debug.enabled: true
    monitoring.wandb.enabled: false
  
  staging:
    logging.level: "INFO"
    debug.enabled: false
    monitoring.wandb.enabled: true
  
  production:
    logging.level: "WARNING"
    debug.enabled: false
    monitoring.wandb.enabled: true
    security.validation.strict_mode: true

# Hydra配置
hydra:
  run:
    dir: "outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}"
  sweep:
    dir: "multirun/${now:%Y-%m-%d}/${now:%H-%M-%S}"
    subdir: "${hydra.job.num}"
  launcher:
    _target_: hydra._internal.BasicLauncher
  sweeper:
    _target_: hydra._internal.BasicSweeper
    max_jobs: null
  help:
    app_name: ${project.name}
    header: "斗地主AI优化项目配置"
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers: [console]
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers: [console, file]
    disable_existing_loggers: false
