"""
多智能体协作机制增强模块

实现斗地主游戏中农民协作机制的增强功能，
主要包括隐含通信机制、基于动作的信息传递和共享价值网络等功能。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union
from collections import defaultdict

from cardgame_ai.multi_agent.multi_agent_framework import FarmerCooperation, RoleManager
from cardgame_ai.games.doudizhu.card import Card
from cardgame_ai.games.doudizhu.card_group import CardGroup


class EnhancedFarmerCooperation(FarmerCooperation):
    """增强的农民协作机制
    
    扩展基础农民协作类，增强隐含通信机制和共享价值网络功能。
    """
    
    def __init__(self, role_manager: RoleManager, communication_threshold: float = 0.7):
        """初始化增强的农民协作机制
        
        Args:
            role_manager: 角色管理器
            communication_threshold: 通信阈值，控制隐含通信的触发条件
        """
        super().__init__(role_manager, communication_threshold)
        
        # 初始化共享价值网络
        self.shared_value_network = SharedValueNetwork()
        
        # 信号编码和解码器
        self.signal_encoder = SignalEncoder()
        self.signal_decoder = SignalDecoder()
        
        # 用于记录牌型信号的历史
        self.play_pattern_history = defaultdict(list)
        
        # 隐含通信状态
        self.communication_state = defaultdict(dict)
        
        # 特殊牌型与信号映射
        self.pattern_signal_mapping = {
            "straight": "has_straights",  # 顺子表示有更多顺子
            "pair_straight": "has_pair_straights",  # 连对表示有更多连对
            "trio": "has_trios",  # 三张表示有更多三张
            "bomb": "has_bombs",  # 炸弹表示有更多炸弹
            "single_big": "has_big_cards",  # 大牌表示有其他大牌
            "single_small": "lack_big_cards",  # 小牌表示缺少大牌
            "pass": "weak_hand"  # 不出表示手牌较弱
        }
    
    def _interpret_signals(self, teammate_ids: List[str], observations: Dict[str, np.ndarray]) -> Dict[str, Dict[str, Any]]:
        """增强的队友隐含信号解读
        
        分析队友最近的行动，解读其中包含的隐含信号
        
        Args:
            teammate_ids: 队友ID列表
            observations: 所有智能体的观察
            
        Returns:
            解读出的队友信号
        """
        signals = {}
        
        for teammate_id in teammate_ids:
            if teammate_id not in observations:
                continue
                
            # 提取队友的观察
            teammate_obs = observations[teammate_id]
            
            # 提取队友的出牌历史
            play_history = self._extract_play_history(teammate_id, teammate_obs)
            
            # 如果没有历史记录，跳过
            if not play_history:
                continue
            
            # 分析最近3次出牌
            recent_plays = play_history[-min(3, len(play_history)):]
            decoded_signals = {}
            
            for play in recent_plays:
                # 识别牌型
                play_pattern = self._identify_play_pattern(play)
                
                # 利用牌型解码信号
                if play_pattern in self.pattern_signal_mapping:
                    signal_key = self.pattern_signal_mapping[play_pattern]
                    decoded_signals[signal_key] = True
                
                # 增加特殊组合分析
                combination_signal = self._analyze_combination_signal(recent_plays)
                if combination_signal:
                    decoded_signals.update(combination_signal)
            
            # 使用深度信号解码器进一步分析
            deep_signals = self.signal_decoder.decode(recent_plays, play_history)
            if deep_signals:
                decoded_signals.update(deep_signals)
            
            # 更新本地通信状态
            self.communication_state[teammate_id] = decoded_signals
            
            # 记录解读出的信号
            signals[teammate_id] = decoded_signals
        
        return signals
    
    def _evaluate_actions_with_shared_network(
        self, 
        agent_id: str, 
        hand_cards: np.ndarray, 
        legal_actions: List[int],
        teammate_signals: Dict[str, Dict[str, Any]],
        landlord_pattern: Dict[str, Any]
    ) -> Dict[int, float]:
        """使用共享价值网络评估动作
        
        使用共享价值网络评估各个动作的价值，考虑队友信号和地主模式
        
        Args:
            agent_id: 智能体ID
            hand_cards: 手牌
            legal_actions: 合法动作列表
            teammate_signals: 队友信号
            landlord_pattern: 地主牌型模式
            
        Returns:
            动作价值字典，键为动作ID，值为价值评分
        """
        # 如果没有合法动作，返回空字典
        if not legal_actions:
            return {}
        
        # 准备网络输入
        network_inputs = []
        
        for action in legal_actions:
            # 编码动作
            action_encoding = self._encode_action(action)
            
            # 编码手牌
            hand_encoding = self._encode_hand(hand_cards)
            
            # 编码队友信号
            signal_encoding = self._encode_teammate_signals(teammate_signals)
            
            # 编码地主模式
            landlord_encoding = self._encode_landlord_pattern(landlord_pattern)
            
            # 合并所有特征
            combined_features = np.concatenate([
                action_encoding,
                hand_encoding,
                signal_encoding,
                landlord_encoding
            ])
            
            network_inputs.append(combined_features)
        
        # 转换为批量输入
        if network_inputs:
            batch_input = np.stack(network_inputs)
            
            # 使用共享价值网络评估
            action_values = self.shared_value_network.evaluate(batch_input)
            
            # 创建动作-价值映射
            action_value_map = {
                action: value 
                for action, value in zip(legal_actions, action_values)
            }
            
            return action_value_map
        
        return {}
    
    def _determine_best_cooperative_action(
        self,
        agent_id: str,
        hand_cards: np.ndarray,
        teammate_cards: Dict[str, np.ndarray],
        landlord_pattern: Dict[str, Any],
        legal_actions: List[int],
        action_values: Dict[int, float] = None,
        teammate_signals: Dict[str, Dict[str, Any]] = None
    ) -> Optional[int]:
        """确定最佳协作动作
        
        基于队友信号、估计的队友手牌和共享价值网络，确定最佳协作动作
        
        Args:
            agent_id: 智能体ID
            hand_cards: 手牌
            teammate_cards: 估计的队友手牌
            landlord_pattern: 地主牌型模式
            legal_actions: 合法动作列表
            action_values: 动作价值映射
            teammate_signals: 队友信号
            
        Returns:
            最佳协作动作
        """
        if not legal_actions:
            return None
        
        # 如果没有动作价值映射，使用启发式评估
        if not action_values:
            action_values = self._heuristic_action_evaluation(
                agent_id, hand_cards, legal_actions, teammate_signals, landlord_pattern
            )
        
        # 协作因子计算
        cooperation_factors = {}
        
        for action in legal_actions:
            # 基础价值
            base_value = action_values.get(action, 0.0)
            
            # 计算协作因子
            coop_factor = self._calculate_cooperation_factor(
                action, hand_cards, teammate_cards, teammate_signals, landlord_pattern
            )
            
            # 组合最终价值
            cooperation_factors[action] = base_value * coop_factor
        
        # 选择最佳协作动作
        if cooperation_factors:
            best_action = max(cooperation_factors.items(), key=lambda x: x[1])[0]
            
            # 检查是否超过随机阈值，以增加探索
            if np.random.random() < 0.1:  # 10%的随机探索率
                return np.random.choice(legal_actions)
            
            return best_action
        
        # 如果无法确定协作动作，随机选择一个合法动作
        return np.random.choice(legal_actions)
    
    def _calculate_cooperation_factor(
        self,
        action: int,
        hand_cards: np.ndarray,
        teammate_cards: Dict[str, np.ndarray],
        teammate_signals: Dict[str, Dict[str, Any]],
        landlord_pattern: Dict[str, Any]
    ) -> float:
        """计算协作因子
        
        基于队友信号和手牌状态计算动作的协作因子
        
        Args:
            action: 动作ID
            hand_cards: 手牌
            teammate_cards: 队友手牌估计
            teammate_signals: 队友信号
            landlord_pattern: 地主牌型
            
        Returns:
            协作因子
        """
        base_factor = 1.0
        
        # 解码动作对应的牌型
        action_cards = self._decode_action_to_cards(action)
        if not action_cards:
            return base_factor
        
        # 1. 考虑队友信号
        for teammate_id, signals in teammate_signals.items():
            # 如果队友信号表明有大牌，我们应该保留自己的大牌
            if signals.get("has_big_cards", False) and self._contains_big_cards(action_cards):
                base_factor *= 0.8  # 降低出大牌的意愿
            
            # 如果队友信号表明有炸弹，我们可以更自由地出其他牌
            if signals.get("has_bombs", False) and self._is_bomb(action_cards):
                base_factor *= 0.9  # 降低出炸弹的意愿
            
            # 如果队友信号表明牌很弱，我们应该尽可能接管控制权
            if signals.get("weak_hand", False):
                base_factor *= 1.2  # 增加主动出牌的意愿
        
        # 2. 考虑队友估计的手牌
        for teammate_id, cards in teammate_cards.items():
            # 如果我们的动作会帮助队友出牌，增加协作因子
            if self._helps_teammate(action_cards, cards):
                base_factor *= 1.3
        
        # 3. 考虑地主牌型
        if landlord_pattern:
            # 如果地主似乎有强力牌型，更倾向于保存强力牌
            if landlord_pattern.get("has_strong_cards", False):
                if self._is_strong_play(action_cards):
                    base_factor *= 0.9  # 降低出强力牌的意愿
            
            # 如果地主似乎缺少某种牌型，更倾向于出这种牌型
            weak_patterns = landlord_pattern.get("weak_patterns", [])
            if weak_patterns and self._matches_pattern(action_cards, weak_patterns):
                base_factor *= 1.2  # 增加针对地主弱点出牌的意愿
        
        return base_factor
    
    def _helps_teammate(self, action_cards: List[Card], teammate_cards: np.ndarray) -> bool:
        """判断动作是否有助于队友
        
        Args:
            action_cards: 动作对应的卡牌
            teammate_cards: 队友手牌
            
        Returns:
            是否有助于队友
        """
        # 实现具体的判断逻辑
        # 简单示例：如果动作是出小牌，且队友有大牌，则有助于队友
        if self._is_small_play(action_cards) and self._contains_big_cards_array(teammate_cards):
            return True
        return False
    
    def _is_small_play(self, cards: List[Card]) -> bool:
        """判断是否是小牌
        
        Args:
            cards: 卡牌列表
            
        Returns:
            是否是小牌
        """
        # 简单实现：所有牌都小于10
        return all(card.rank < 10 for card in cards)
    
    def _contains_big_cards_array(self, cards_array: np.ndarray) -> bool:
        """判断数组表示的手牌中是否包含大牌
        
        Args:
            cards_array: 手牌数组
            
        Returns:
            是否包含大牌
        """
        # 假设数组中索引12-14表示2、大小王
        big_card_indices = [12, 13, 14]
        return any(cards_array[i] > 0 for i in big_card_indices)
    
    def _contains_big_cards(self, cards: List[Card]) -> bool:
        """判断卡牌列表中是否包含大牌
        
        Args:
            cards: 卡牌列表
            
        Returns:
            是否包含大牌
        """
        # 简单实现：包含2或大小王
        big_ranks = [15, 16, 17]  # 2、小王、大王
        return any(card.rank in big_ranks for card in cards)
    
    def _is_bomb(self, cards: List[Card]) -> bool:
        """判断是否是炸弹
        
        Args:
            cards: 卡牌列表
            
        Returns:
            是否是炸弹
        """
        # 简单实现：四张同样的牌或王炸
        if len(cards) == 4 and len(set(card.rank for card in cards)) == 1:
            return True
        if len(cards) == 2 and all(card.rank >= 16 for card in cards):
            return True
        return False
    
    def _is_strong_play(self, cards: List[Card]) -> bool:
        """判断是否是强力牌型
        
        Args:
            cards: 卡牌列表
            
        Returns:
            是否是强力牌型
        """
        # 实现强力牌型判断逻辑
        # 简单示例：炸弹或包含多张大牌
        if self._is_bomb(cards):
            return True
        big_card_count = sum(1 for card in cards if self._is_big_card(card))
        return big_card_count >= 2
    
    def _is_big_card(self, card: Card) -> bool:
        """判断是否是大牌
        
        Args:
            card: 卡牌
            
        Returns:
            是否是大牌
        """
        return card.rank >= 11  # A及以上
    
    def _matches_pattern(self, cards: List[Card], patterns: List[str]) -> bool:
        """判断卡牌是否匹配指定牌型
        
        Args:
            cards: 卡牌列表
            patterns: 牌型列表
            
        Returns:
            是否匹配
        """
        # 根据CardGroup实现牌型判断
        card_group = CardGroup(cards)
        pattern = card_group.get_type()
        return pattern in patterns
    
    def _identify_play_pattern(self, play: Any) -> str:
        """识别出牌牌型
        
        Args:
            play: 出牌记录
            
        Returns:
            牌型
        """
        # 假设play是一个包含牌的对象
        if not play or not hasattr(play, 'cards'):
            return "pass"
        
        cards = play.cards
        if not cards:
            return "pass"
        
        # 创建CardGroup并获取类型
        card_group = CardGroup(cards)
        return card_group.get_type()
    
    def _analyze_combination_signal(self, plays: List[Any]) -> Dict[str, Any]:
        """分析组合信号
        
        分析最近几次出牌的组合，推断更复杂的信号
        
        Args:
            plays: 最近几次出牌
            
        Returns:
            组合信号
        """
        signals = {}
        
        # 组合信号示例：连续出单牌可能表示顺子
        if len(plays) >= 3:
            singles_count = sum(1 for play in plays if self._identify_play_pattern(play) == "single")
            if singles_count >= 3:
                signals["potential_straight"] = True
        
        # 更多复杂组合分析...
        
        return signals
    
    def _encode_action(self, action: int) -> np.ndarray:
        """编码动作
        
        Args:
            action: 动作ID
            
        Returns:
            动作编码
        """
        # 简单实现：使用独热编码
        # 假设动作空间大小为100
        encoding = np.zeros(100)
        encoding[action] = 1
        return encoding
    
    def _encode_hand(self, hand_cards: np.ndarray) -> np.ndarray:
        """编码手牌
        
        Args:
            hand_cards: 手牌数组
            
        Returns:
            手牌编码
        """
        # 假设手牌已经是编码形式
        return hand_cards
    
    def _encode_teammate_signals(self, teammate_signals: Dict[str, Dict[str, Any]]) -> np.ndarray:
        """编码队友信号
        
        Args:
            teammate_signals: 队友信号
            
        Returns:
            信号编码
        """
        # 定义信号特征
        signal_features = [
            "has_big_cards",
            "has_bombs",
            "has_straights",
            "has_pair_straights",
            "has_trios",
            "weak_hand",
            "lack_big_cards"
        ]
        
        # 初始化编码
        encoding = np.zeros(len(signal_features))
        
        # 遍历所有队友信号
        for teammate_id, signals in teammate_signals.items():
            for i, feature in enumerate(signal_features):
                if signals.get(feature, False):
                    encoding[i] = 1
        
        return encoding
    
    def _encode_landlord_pattern(self, landlord_pattern: Dict[str, Any]) -> np.ndarray:
        """编码地主牌型模式
        
        Args:
            landlord_pattern: 地主牌型模式
            
        Returns:
            编码
        """
        # 定义地主模式特征
        pattern_features = [
            "has_strong_cards",
            "has_bombs",
            "has_big_singles",
            "has_straights",
            "aggressive_play"
        ]
        
        # 初始化编码
        encoding = np.zeros(len(pattern_features))
        
        # 编码地主模式
        for i, feature in enumerate(pattern_features):
            if landlord_pattern.get(feature, False):
                encoding[i] = 1
        
        # 编码弱点牌型
        weak_patterns = landlord_pattern.get("weak_patterns", [])
        weak_encoding = np.zeros(10)  # 假设有10种可能的弱点牌型
        for pattern in weak_patterns:
            if pattern == "single":
                weak_encoding[0] = 1
            elif pattern == "pair":
                weak_encoding[1] = 1
            elif pattern == "trio":
                weak_encoding[2] = 1
            # ... 其他牌型编码
        
        # 合并编码
        return np.concatenate([encoding, weak_encoding])
    
    def _decode_action_to_cards(self, action: int) -> List[Card]:
        """将动作ID解码为卡牌列表
        
        Args:
            action: 动作ID
            
        Returns:
            卡牌列表
        """
        # 这里需要根据实际的动作空间实现解码逻辑
        # 简单示例，实际实现需要根据游戏环境的动作编码来做
        return []


class SharedValueNetwork:
    """共享价值网络
    
    用于评估农民智能体的动作价值，支持农民协作决策
    """
    
    def __init__(self, feature_dim: int = 200, hidden_dim: int = 128):
        """初始化共享价值网络
        
        Args:
            feature_dim: 输入特征维度
            hidden_dim: 隐藏层维度
        """
        self.network = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
        # 优化器
        self.optimizer = torch.optim.Adam(self.network.parameters(), lr=0.001)
        
        # 经验缓冲区
        self.experience_buffer = []
        
        # 训练计数
        self.train_count = 0
    
    def evaluate(self, features: np.ndarray) -> np.ndarray:
        """评估动作价值
        
        Args:
            features: 特征向量
            
        Returns:
            动作价值
        """
        # 转换为张量
        with torch.no_grad():
            features_tensor = torch.FloatTensor(features)
            values = self.network(features_tensor)
            return values.numpy().flatten()
    
    def update(self, features: np.ndarray, targets: np.ndarray) -> float:
        """更新网络参数
        
        Args:
            features: 特征向量
            targets: 目标价值
            
        Returns:
            训练损失
        """
        # 转换为张量
        features_tensor = torch.FloatTensor(features)
        targets_tensor = torch.FloatTensor(targets).unsqueeze(1)
        
        # 前向传播
        values = self.network(features_tensor)
        
        # 计算损失
        loss = F.mse_loss(values, targets_tensor)
        
        # 反向传播和优化
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        # 更新训练计数
        self.train_count += 1
        
        return loss.item()
    
    def add_experience(self, features: np.ndarray, reward: float) -> None:
        """添加经验到缓冲区
        
        Args:
            features: 特征向量
            reward: 奖励
        """
        self.experience_buffer.append((features, reward))
        
        # 限制缓冲区大小
        max_buffer_size = 10000
        if len(self.experience_buffer) > max_buffer_size:
            self.experience_buffer = self.experience_buffer[-max_buffer_size:]
    
    def train_from_buffer(self, batch_size: int = 32) -> Optional[float]:
        """从经验缓冲区训练
        
        Args:
            batch_size: 批量大小
            
        Returns:
            训练损失，如果没有足够数据则返回None
        """
        if len(self.experience_buffer) < batch_size:
            return None
        
        # 随机采样
        indices = np.random.choice(len(self.experience_buffer), batch_size, replace=False)
        samples = [self.experience_buffer[i] for i in indices]
        
        # 准备批量数据
        features = np.stack([s[0] for s in samples])
        rewards = np.array([s[1] for s in samples])
        
        # 更新网络
        return self.update(features, rewards)


class SignalEncoder:
    """信号编码器
    
    用于将意图编码为隐含信号
    """
    
    def __init__(self):
        """初始化信号编码器"""
        # 策略信号映射
        self.strategy_signals = {
            "aggressive": [("single_big", "bomb")],
            "defensive": [("pass", "trio")],
            "cooperate": [("pair", "single")],
            "need_help": [("single_small", "pass")]
        }
    
    def encode(self, intention: str, available_actions: List[int]) -> Optional[int]:
        """编码意图为动作信号
        
        Args:
            intention: 意图
            available_actions: 可用动作列表
            
        Returns:
            编码后的动作ID，如果无法编码则返回None
        """
        if intention not in self.strategy_signals:
            return None
        
        signal_patterns = self.strategy_signals[intention]
        
        # 遍历可用动作，查找与信号模式匹配的动作
        for action in available_actions:
            action_pattern = self._get_action_pattern(action)
            for pattern_pair in signal_patterns:
                if action_pattern in pattern_pair:
                    return action
        
        return None
    
    def _get_action_pattern(self, action: int) -> str:
        """获取动作的牌型
        
        Args:
            action: 动作ID
            
        Returns:
            牌型
        """
        # 这里需要根据实际的动作空间实现解码逻辑
        # 简单示例，实际实现需要根据游戏环境的动作编码来做
        return "unknown"


class SignalDecoder:
    """信号解码器
    
    用于解码隐含信号为意图
    """
    
    def __init__(self):
        """初始化信号解码器"""
        # 牌型序列与意图的映射
        self.pattern_intentions = {
            ("single_big", "bomb"): "aggressive",
            ("pass", "trio"): "defensive",
            ("pair", "single"): "cooperate",
            ("single_small", "pass"): "need_help"
        }
    
    def decode(self, recent_plays: List[Any], play_history: List[Any]) -> Dict[str, Any]:
        """解码牌型序列为意图
        
        Args:
            recent_plays: 最近的出牌
            play_history: 完整出牌历史
            
        Returns:
            解码的意图
        """
        if not recent_plays:
            return {}
        
        # 获取最近出牌的牌型序列
        pattern_sequence = []
        for play in recent_plays:
            pattern = self._get_play_pattern(play)
            if pattern:
                pattern_sequence.append(pattern)
        
        # 寻找匹配的意图
        decoded_intentions = {}
        
        # 检查连续的两个牌型
        if len(pattern_sequence) >= 2:
            for i in range(len(pattern_sequence) - 1):
                pattern_pair = (pattern_sequence[i], pattern_sequence[i+1])
                if pattern_pair in self.pattern_intentions:
                    intent = self.pattern_intentions[pattern_pair]
                    decoded_intentions[intent] = True
        
        # 分析单个牌型
        for pattern in pattern_sequence:
            if pattern == "bomb":
                decoded_intentions["has_more_bombs"] = True
            elif pattern == "single_big":
                decoded_intentions["has_big_cards"] = True
            elif pattern == "pass":
                decoded_intentions["weak_hand"] = True
        
        # 深度分析历史模式
        context_intentions = self._analyze_context(play_history)
        decoded_intentions.update(context_intentions)
        
        return decoded_intentions
    
    def _get_play_pattern(self, play: Any) -> Optional[str]:
        """获取出牌的牌型
        
        Args:
            play: 出牌对象
            
        Returns:
            牌型，如果无法识别则返回None
        """
        # 这里需要根据实际的出牌对象实现牌型识别
        # 简单示例，实际实现需要根据游戏环境来做
        if not play or not hasattr(play, 'cards'):
            return None
        
        cards = play.cards
        if not cards:
            return "pass"
        
        # 这里需要实现牌型识别逻辑
        return "unknown"
    
    def _analyze_context(self, play_history: List[Any]) -> Dict[str, Any]:
        """分析历史出牌的上下文
        
        Args:
            play_history: 出牌历史
            
        Returns:
            上下文意图
        """
        # 简单示例，实际实现需要根据游戏规则来做更复杂的分析
        context_intentions = {}
        
        # 分析出牌频率和模式
        if len(play_history) > 5:
            pass_count = sum(1 for play in play_history[-5:] if self._get_play_pattern(play) == "pass")
            if pass_count >= 3:
                context_intentions["reluctant_to_play"] = True
        
        # 更多上下文分析...
        
        return context_intentions 