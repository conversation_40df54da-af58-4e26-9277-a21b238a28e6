# ============================================================================
# EfficientZero算法训练配置文件
# ============================================================================
#
# 【文件用途】
# 此文件专门配置EfficientZero算法的训练参数，是训练性能优化的核心文件。
# 包含MCTS搜索、神经网络架构、训练超参数等所有关键配置。
#
# 【优化重点】
# - 性能优化: batch_size, num_simulations, model架构
# - 收敛优化: learning_rate, lr_scheduler, optimizer
# - 内存优化: gradient配置, 模型大小
#
# 【与base.yaml关系】
# 此文件继承base.yaml的基础配置，专注于算法特定参数
# ============================================================================

# @package training
defaults:
  - base_training

# ============================================================================
# 算法基本配置
# ============================================================================
algorithm:
  name: "efficient_zero"    # 算法名称，对应cardgame_ai/algorithms/efficient_zero.py
  type: "model_based"       # 基于模型的强化学习算法

  # ============================================================================
  # EfficientZero特定参数 - 影响AI决策质量和训练速度
  # ============================================================================
  efficient_zero:
    # 【MCTS搜索配置】- 直接影响AI决策质量和推理速度
    # 【num_simulations】每次决策的MCTS模拟次数
    # - 50: 快速模式 (推理速度快，决策质量一般)
    # - 100: 平衡模式 (推荐，性能和质量平衡)
    # - 200: 高质量模式 (决策质量高，推理速度慢)
    # 【代码位置】cardgame_ai/algorithms/mcts.py:424
    # 【性能影响】每增加50次模拟，推理时间约增加50%
    num_simulations: 100

    # 【max_simulations】动态调整时的最大模拟次数
    # 在关键时刻可以增加到此数值以提高决策质量
    # 【代码位置】cardgame_ai/algorithms/mcts.py:428
    max_simulations: 200

    # 【搜索配置】- 控制探索与利用的平衡
    # 【c_puct】UCB公式中的探索常数，控制探索程度
    # - 1.0: 较少探索 (更倾向于选择已知好的动作)
    # - 1.25: 平衡探索 (推荐值)
    # - 1.5+: 更多探索 (更愿意尝试新动作)
    # 【代码位置】cardgame_ai/config/mcts_config.py:14
    c_puct: 1.25

    # 【dirichlet_alpha】Dirichlet噪声参数，增加根节点探索
    # - 0.3: 适合斗地主的噪声强度 (推荐)
    # - 0.1: 较少噪声 (更确定性的决策)
    # - 0.5+: 较多噪声 (更随机的探索)
    dirichlet_alpha: 0.3

    # 【exploration_fraction】探索噪声的混合比例
    # - 0.25: 25%噪声 + 75%策略网络输出 (推荐)
    exploration_fraction: 0.25

    # 【动态预算分配】- EfficientZero的创新特性
    # 根据游戏阶段动态分配计算资源
    dynamic_budget: true
    budget_allocation:
      early_game: 0.4    # 前期40%计算资源 (叫地主阶段)
      mid_game: 0.4      # 中期40%计算资源 (主要出牌阶段)
      late_game: 0.2     # 后期20%计算资源 (收尾阶段)

    # 【价值网络配置】- 影响价值估计的准确性
    value_network:
      # 【type】价值网络类型
      # - "scalar": 标量价值 (简单，速度快)
      # - "categorical": 分类价值 (中等复杂度)
      # - "distributional": 分布式价值 (推荐，最准确)
      # 【代码位置】cardgame_ai/algorithms/efficient_zero.py:366
      type: "distributional"

      # 【support_size】分布式价值网络的支持大小
      # - 601: 支持-300到+300的价值范围，精度0.1
      # 更大的支持大小提供更精确的价值估计但增加计算量
      support_size: 601
      value_min: -300    # 最小价值 (大败)
      value_max: 300     # 最大价值 (大胜)
    
    # ========================================================================
    # 【神经网络模型架构】- 直接影响模型容量和训练速度
    # ========================================================================
    model:
      # 【表示网络】将观察转换为隐藏状态
      representation:
        # 【网络类型】
        # - "resnet": ResNet架构 (推荐，性能好)
        # - "mlp": 多层感知机 (简单，速度快)
        type: "resnet"

        # 【ResNet块数量】影响模型容量和计算复杂度
        # - 4: 轻量级 (速度快，容量小)
        # - 6: 平衡型 (推荐，性能和速度平衡)
        # - 8+: 重量级 (容量大，速度慢)
        # 【代码位置】cardgame_ai/algorithms/efficient_zero/model.py:42
        # 【性能影响】每增加2个块，训练时间约增加30%
        blocks: 6

        # 【通道数】网络宽度，影响模型表达能力
        # - 128: 轻量级 (速度快，表达能力有限)
        # - 256: 标准型 (推荐，平衡性能)
        # - 512: 重量级 (表达能力强，显存需求大)
        # 【性能影响】从256增加到512，显存需求约增加4倍
        channels: 256

        downsample: false    # 是否下采样，斗地主不需要

      # 【动态网络】预测状态转移和奖励
      dynamics:
        type: "resnet"       # 与表示网络保持一致
        blocks: 6            # 通常与表示网络相同
        channels: 256        # 通常与表示网络相同
        reward_head: true    # 启用奖励预测头

      # 【预测网络】预测策略和价值
      prediction:
        type: "resnet"       # 与其他网络保持一致
        blocks: 6            # 通常与其他网络相同
        channels: 256        # 通常与其他网络相同
        policy_head: true    # 启用策略预测头
        value_head: true     # 启用价值预测头

# ============================================================================
# 训练超参数 - 影响训练收敛速度和最终性能
# ============================================================================
training:
  # 【基础训练参数】
  # 【epochs】训练轮数，影响训练时间和收敛程度
  # - 500: 快速训练 (可能欠拟合)
  # - 1000: 标准训练 (推荐)
  # - 2000+: 长期训练 (可能过拟合)
  epochs: 1000

  # 【batch_size】批处理大小，影响训练稳定性和速度
  # - 128: 小批次 (收敛稳定，显存需求小)
  # - 256: 中批次 (推荐，平衡性能)
  # - 512: 大批次 (训练快，需要大显存)
  # - 768+: 超大批次 (需要52GB显存，您的RTX3080可以尝试)
  # 【代码位置】scripts/optimized_training.py:101
  # 【性能影响】从256增加到512，训练速度提升约30%
  batch_size: 512

  # 【learning_rate】学习率，影响收敛速度和稳定性
  # - 0.0001: 保守学习率 (收敛慢但稳定)
  # - 0.0005: 平衡学习率 (推荐)
  # - 0.001: 激进学习率 (收敛快但可能不稳定)
  learning_rate: 0.0005

  # 【weight_decay】权重衰减，防止过拟合
  # - 1e-5: 轻微正则化
  # - 1e-4: 标准正则化 (推荐)
  # - 1e-3: 强正则化
  weight_decay: 1e-4

  # 【学习率调度】控制学习率变化策略
  lr_scheduler:
    # 【type】调度器类型
    # - "cosine": 余弦退火 (推荐，平滑下降)
    # - "step": 阶梯下降 (简单有效)
    # - "exponential": 指数下降 (快速下降)
    type: "cosine"
    warmup_epochs: 10    # 预热轮数，逐渐增加学习率
    min_lr: 1e-6         # 最小学习率

  # 【优化器配置】
  optimizer:
    # 【type】优化器类型
    # - "adamw": AdamW优化器 (推荐，收敛快且稳定)
    # - "adam": Adam优化器 (经典选择)
    # - "sgd": SGD优化器 (简单但需要调参)
    type: "adamw"
    betas: [0.9, 0.999]  # Adam的动量参数
    eps: 1e-8            # 数值稳定性参数
    amsgrad: false       # 是否使用AMSGrad变体

  # 【梯度配置】
  gradient:
    # 【clip_norm】梯度裁剪，防止梯度爆炸
    # - 5.0: 较严格的裁剪
    # - 10.0: 标准裁剪 (推荐)
    # - 20.0: 较宽松的裁剪
    # 【代码位置】cardgame_ai/algorithms/efficient_zero.py:1230
    clip_norm: 10.0

    # 【accumulation_steps】梯度累积步数
    # - 1: 不使用梯度累积 (推荐，显存充足时)
    # - 2-4: 显存不足时使用，等效于增大batch_size
    accumulation_steps: 1

  # 【正则化配置】
  regularization:
    dropout: 0.0           # Dropout比率，0表示不使用
    label_smoothing: 0.0   # 标签平滑，0表示不使用
    mixup_alpha: 0.0       # Mixup数据增强，0表示不使用

# 自对弈配置
self_play:
  # 游戏配置
  num_games: 1000  # 每轮自对弈游戏数
  max_moves: 1000  # 最大步数
  
  # 数据收集
  data_collection:
    buffer_size: 100000  # 经验回放缓冲区大小
    min_buffer_size: 10000  # 开始训练的最小缓冲区大小
    sample_ratio: 0.1  # 采样比例
  
  # 对手配置
  opponents:
    self_ratio: 0.8  # 自对弈比例
    historical_ratio: 0.2  # 历史模型比例
    random_ratio: 0.0  # 随机对手比例

# 多智能体协作配置
multi_agent:
  # 农民协作
  farmer_cooperation:
    enabled: true
    cooperation_weight: 0.8  # 从0.7提升
    team_reward_weight: 0.9  # 从0.8提升
    
    # 协作机制
    mechanisms:
      - "shared_value"      # 共享价值函数
      - "communication"     # 通信机制
      - "role_specialization"  # 角色专门化
    
    # 通信配置
    communication:
      enabled: true
      message_dim: 64
      max_messages: 3
      attention_heads: 4
  
  # 角色管理
  role_management:
    dynamic_roles: true
    role_switch_threshold: 0.1
    specialization_bonus: 0.1

# 奖励机制配置
reward:
  # 基础奖励
  terminal_reward: 2.0  # 胜负奖励
  
  # 过程奖励
  process_rewards:
    # 炸弹奖励机制
    bomb_reward: 0.1  # 炸弹奖励
    rocket_reward: 0.15  # 火箭奖励
    
    # 合作奖励
    cooperation_reward: 0.5  # 农民合作奖励
    
    # 进度奖励
    progress_reward: 0.01  # 游戏进度奖励
  
  # 奖励塑形
  reward_shaping:
    enabled: true
    potential_based: true  # 基于势能的奖励塑形
    discount_factor: 0.99

# 评估配置
evaluation:
  # 评估频率
  eval_frequency: 100  # 每N个训练步骤评估一次
  eval_episodes: 100   # 每次评估的游戏局数
  
  # 评估对手
  opponents:
    - type: "random"
      weight: 0.1
    - type: "rule_based"
      weight: 0.3
    - type: "historical"
      weight: 0.6
  
  # 评估指标
  metrics:
    - "win_rate"
    - "average_score"
    - "game_length"
    - "cooperation_efficiency"

# 分布式训练配置
distributed:
  # 并行配置
  data_parallel: true
  model_parallel: false
  
  # 工作节点配置
  num_workers: 4  # 训练工作节点数
  num_actors: 16  # 自对弈actor数量
  
  # 同步配置
  sync_frequency: 10  # 模型同步频率
  async_update: false  # 是否异步更新

# 检查点配置
checkpoint:
  save_frequency: 1000  # 保存频率
  keep_last: 5         # 保留最近N个检查点
  save_best: true      # 保存最佳模型
  
  # 检查点内容
  save_optimizer: true
  save_scheduler: true
  save_random_state: true

# 早停配置
early_stopping:
  enabled: true
  patience: 50  # 容忍轮数
  min_delta: 0.001  # 最小改进
  monitor: "eval_win_rate"  # 监控指标
  mode: "max"  # max或min

# 实验特定配置
experiment:
  # 实验标识
  name: "efficient_zero_optimization"
  tags: ["efficient_zero", "multi_agent", "cooperation"]
  
  # 超参数搜索
  hyperparameter_search:
    enabled: false
    method: "random"  # random, grid, bayesian
    num_trials: 50
    
    # 搜索空间
    search_space:
      learning_rate: [1e-5, 1e-3]
      batch_size: [128, 256, 512]
      num_simulations: [50, 100, 200]
      cooperation_weight: [0.5, 0.9]

# 调试和监控
debug:
  # 训练监控
  log_frequency: 100  # 日志记录频率
  plot_frequency: 1000  # 绘图频率
  
  # 性能分析
  profile_training: false
  profile_inference: false
  
  # 梯度监控
  monitor_gradients: false
  gradient_histogram: false

# ============================================================================
# 资源管理配置 - 硬件资源使用的关键配置
# ============================================================================
resources:
  # 【GPU配置】- 影响显存使用和训练稳定性
  gpu:
    # 【memory_fraction】GPU显存使用比例
    # - 0.8: 保守使用80%显存 (稳定但浪费)
    # - 0.9: 标准使用90%显存 (推荐)
    # - 0.95: 激进使用95%显存 (最大化利用，您的52GB显存可以尝试)
    # 【代码位置】configs/training/efficient_zero.yaml:254
    memory_fraction: 0.95

    # 【allow_growth】是否允许显存动态增长
    # - true: 动态分配显存 (推荐，避免显存浪费)
    # - false: 预分配所有显存 (可能更稳定但浪费显存)
    allow_growth: true

  # 【CPU配置】- 影响数据处理和并行计算性能
  cpu:
    # 【num_threads】CPU线程数，影响数据预处理和MCTS并行计算
    # - 8: 默认值，适合大多数CPU
    # - 12-16: 推荐值，充分利用现代多核CPU
    # - 24+: 高端CPU可以使用更多线程
    # 【代码位置】cardgame_ai/integrated_system.py:397
    # 【性能影响】增加线程数可以提升数据加载和MCTS搜索速度
    num_threads: 16

  # 【内存配置】- 控制系统内存使用
  memory:
    # 【max_usage】最大内存使用量
    # - "16GB": 保守设置
    # - "32GB": 标准设置 (推荐)
    # - "64GB": 大内存系统可以使用
    max_usage: "32GB"

    # 【swap_usage】是否使用交换空间
    # - false: 不使用交换空间 (推荐，避免性能下降)
    # - true: 允许使用交换空间 (内存不足时的备选)
    swap_usage: false
