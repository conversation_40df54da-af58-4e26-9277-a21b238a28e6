"""
优化的集成AI系统模块

提供优化的集成AI系统，整合多种算法和决策系统，提供统一的接口，
提高系统性能和可扩展性。
"""

import os
import time
import logging
import threading
from typing import Dict, Any, List, Optional, Callable, Tuple, Union

from cardgame_ai.core.base import State, Action
from cardgame_ai.core.component_manager import ComponentManager
from cardgame_ai.core.optimized_hybrid_system import OptimizedHybridDecisionSystem

# 设置日志
logger = logging.getLogger(__name__)


class OptimizedIntegratedAISystem:
    """
    优化的集成AI系统类

    整合多种算法和决策系统，提供统一的接口，提高系统性能和可扩展性。
    """

    def __init__(
        self,
        decision_system: Optional[OptimizedHybridDecisionSystem] = None,
        component_types: Optional[List[str]] = None,
        use_cache: bool = True,
        use_resource_allocation: bool = True,
        use_similarity_cache: bool = True
    ):
        """
        初始化优化的集成AI系统

        Args:
            decision_system: 决策系统，如果为None则创建新的
            component_types: 组件类型列表，如果为None则使用默认值
            use_cache: 是否使用缓存
            use_resource_allocation: 是否使用资源分配
            use_similarity_cache: 是否使用相似度缓存
        """
        # 默认组件类型
        if component_types is None:
            component_types = [
                "mcts",
                "efficient_zero",
                "rule_based",
                "random"
            ]

        # 保存参数
        self.component_types = component_types
        self.use_cache = use_cache

        # 创建或使用决策系统
        if decision_system is None:
            self.decision_system = OptimizedHybridDecisionSystem(
                component_types=component_types,
                cache_capacity=10000 if use_cache else 0,
                use_resource_allocation=use_resource_allocation,
                use_similarity_cache=use_similarity_cache and use_cache
            )
        else:
            self.decision_system = decision_system

        # 获取组件管理器
        self.component_manager = ComponentManager.get_instance()

        # 锁
        self.lock = threading.RLock()

        # 性能统计
        self.stats = {
            "total_decisions": 0,
            "total_decision_time": 0.0,
            "avg_decision_time": 0.0
        }

        logger.info(f"优化的集成AI系统已初始化，组件类型: {component_types}")

    def decide(self, state: State) -> Action:
        """
        做出决策

        Args:
            state: 当前状态

        Returns:
            Action: 选择的动作
        """
        start_time = time.time()

        try:
            # 使用决策系统做出决策
            action = self.decision_system.decide(state)

            # 更新统计信息
            decision_time = time.time() - start_time
            self._update_stats(decision_time)

            return action
        except Exception as e:
            logger.error(f"决策出错: {e}")
            # 出错时使用备用策略
            return self._fallback_decision(state)

    def _fallback_decision(self, state: State) -> Action:
        """
        备用决策策略

        当主要决策系统出错时使用

        Args:
            state: 当前状态

        Returns:
            Action: 选择的动作
        """
        try:
            # 尝试使用规则基础组件
            rule_based = self.component_manager.get_component("rule_based")
            return rule_based.decide(state)
        except Exception:
            try:
                # 尝试使用随机组件
                random_agent = self.component_manager.get_component("random")
                return random_agent.decide(state)
            except Exception:
                # 最后尝试获取合法动作
                if hasattr(state, "get_legal_actions"):
                    actions = state.get_legal_actions()
                    if actions:
                        import random
                        return random.choice(actions)

                # 无法做出决策
                logger.error("无法做出决策，返回None")
                return None

    def _update_stats(self, decision_time: float) -> None:
        """
        更新统计信息

        Args:
            decision_time: 决策时间
        """
        with self.lock:
            # 更新总决策次数
            self.stats["total_decisions"] += 1

            # 更新总决策时间
            self.stats["total_decision_time"] += decision_time

            # 更新平均决策时间
            total_decisions = self.stats["total_decisions"]
            if total_decisions > 0:
                self.stats["avg_decision_time"] = (
                    self.stats["total_decision_time"] / total_decisions
                )

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self.lock:
            # 复制统计信息
            stats_copy = self.stats.copy()

            # 添加决策系统统计
            stats_copy["decision_system_stats"] = self.decision_system.get_stats()

            return stats_copy

    def clear_cache(self) -> None:
        """清除缓存"""
        self.decision_system.clear_cache()

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self.lock:
            self.stats = {
                "total_decisions": 0,
                "total_decision_time": 0.0,
                "avg_decision_time": 0.0
            }
            self.decision_system.reset_stats()
            logger.info("已重置统计信息")
