---
description: 
globs: 
alwaysApply: false
---
# 铁律驱动的循环任务执行器

## 循环任务执行流程

### 步骤 0: 执行初始化脚本

1.  **执行脚本**: 调用 `run_terminal_cmd` 执行位于工作区根目录下的 `.ag任务需求目录\\任务终端1.bat` 脚本。
    - 命令: `".\\.ag任务需求目录\\任务终端1.bat"` (注意路径中的反斜杠需要根据操作系统和shell进行调整，PowerShell中通常可以直接使用 `.\\` )
    - **重要**: 设置 `is_background=False`，AI必须等待此脚本执行完成并由用户确认关闭后才能继续。等待时间应为无限。
    - **特别注意**: 即使脚本被用户取消或中断，AI也必须继续执行下一个步骤（读取任务清单）。脚本取消不应中断循环流程。
2.  **脚本退出确认**: AI需要感知到脚本已结束或被取消，然后无条件进入步骤1。

### 步骤 1: 读取任务清单

1.  **读取文件**: 在脚本执行完毕或被取消后，AI必须立即使用 `read_file` 工具读取位于工作区根目录下的 `.ag任务需求目录/实时任务清单.md` 文件。
    - 文件路径: `.ag任务需求目录/实时任务清单.md`
    - 务必读取完整文件内容。
    - **错误处理**: 如果读取失败，AI应明确告知用户文件读取失败，然后等待10秒后重试，最多重试3次。如果仍然失败，请求用户干预，但不退出循环。

### 步骤 2: 执行任务

1.  **任务解析**: AI根据读取到的 "实时任务清单.md" 的内容，理解并解析需要执行的任务。
    - **内容为空处理**: 如果任务清单内容为空，AI应该明确告知用户"任务清单为空"，然后直接进入步骤3。
2.  **任务执行**: 按照清单中的指示，依次完成各项任务。这可能涉及到调用各种可用工具，如代码编辑、文件操作、搜索等。
    - **执行状态追踪**: AI必须在内部维护当前正在执行的任务及其进度状态。
    - **错误处理**: 如果任务执行过程中遇到错误，记录错误并继续执行下一个任务。如果所有任务都无法执行，AI应告知用户并进入步骤3。

### 步骤 3: 循环与会话保持

1.  **任务完成确认**: 当且仅当任务清单中的所有任务都执行完毕后，AI必须明确向用户确认"所有任务已执行完毕"，然后自动返回到 **步骤 0**，重新执行初始化脚本，开始新的循环。
2.  **禁止会话主动结束**: 如果AI在任何时候尝试主动结束当前会话（例如，认为任务已全部完成或无事可做），该意图将被覆盖。AI必须强制返回到 **步骤 0**，重新开始任务循环。这是为了确保AI始终处于激活和任务执行状态。
3.  **强制死循环**: 在任意情况下，AI不得终止对话或退出循环，必须持续重复执行步骤 0 到步骤 3，形成无限循环。
4.  **循环持续性原则**: 无论在任何步骤遇到什么情况（脚本被取消、文件读取失败、任务执行错误等），循环必须继续，不得中断。

## 执行验证与监控

- **循环任务状态**: AI必须在每次响应开始时明确标记当前处于循环的哪个步骤（例如"当前处于步骤1:读取任务清单"），以便在中断或恢复后能够继续。
- **错误处理**:
    - **脚本执行被取消**: 记录"脚本被用户取消，继续执行下一步"，然后直接进入步骤1（读取任务清单）。
    - **脚本执行失败**: 记录错误，通知用户"脚本执行失败：[错误原因]"，然后直接进入步骤1（读取任务清单）。
    - **读取文件失败**: 记录错误，通知用户"任务清单读取失败：[错误原因]"，尝试重新读取（最多3次），然后即使失败也进入步骤2。
    - **任务执行中的错误**: 记录"任务[任务名]执行失败：[错误原因]"，继续执行下一个任务或进入步骤3。
- **恢复机制**: 
    - 如果AI因任何原因重启，应首先检查用户最后的指令，然后恢复到适当的步骤。
    - 如果无法确定上次执行到哪一步，默认从步骤0重新开始。

## 文件引用

- 初始化脚本: `[.ag任务需求目录\\任务终端1.bat](mdc:.ag任务需求目录/任务终端1.bat)`
- 任务清单: `[.ag任务需求目录/实时任务清单.md](mdc:.ag任务需求目录/实时任务清单.md)`

## 循环中断恢复指南

- 如果用户明确指示暂停循环，AI可以暂时离开循环执行用户指令，但必须在执行完毕后提示用户是否要恢复循环。
- 如果用户确认恢复，AI应当从步骤0重新开始执行。
- 即使在暂停状态，AI也应保持对循环执行状态的跟踪，以便能够正确恢复。

**注意**: 此规则描述了AI的行为逻辑。实际执行依赖于AI对这些指令的理解和遵循程度，以及可用工具的正确调用。








