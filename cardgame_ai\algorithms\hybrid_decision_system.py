"""
混合决策系统模块

实现结合神经网络、搜索和规则的混合决策系统，通过元控制器动态选择最合适的决策方法。
支持关键决策点检测和动态计算预算分配。
"""

import os
import time
import logging
import random
import inspect
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, List, Tuple, Optional, Union, Type, Callable
from abc import ABC, abstractmethod

from cardgame_ai.core.agent import Agent
from cardgame_ai.core.base import State, Action, Experience
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.symbolic_reasoning import SymbolicReasoningComponent
from cardgame_ai.algorithms.opponent_modeling.deviation_detector import DeviationDetector, DeviationToExploitMapper
from cardgame_ai.algorithms.gto_approximation import GTOPolicy
from cardgame_ai.algorithms.hrl.high_level_policy import HighLevelPolicy
from cardgame_ai.algorithms.hrl.low_level_policy import LowLevelPolicy
from cardgame_ai.algorithms.hrl.hierarchical_controller import HierarchicalController
from cardgame_ai.algorithms.belief_tracking.deep_belief import DeepBeliefTracker
from cardgame_ai.algorithms.belief_tracking.joint_belief import JointBeliefTracker
from cardgame_ai.games.common.belief_state import BeliefState
from cardgame_ai.algorithms.dynamic_budget_allocator import DynamicBudgetAllocator
from cardgame_ai.algorithms.endgame_modules import (
    handle_king_bomb_endgame, is_king_bomb_scenario,
    handle_single_card_control, is_single_card_control_scenario,
    is_endgame, get_endgame_type, EndgameType
)
from cardgame_ai.algorithms.endgame_specialist import EndgameSpecialist
from cardgame_ai.games.doudizhu.state import DouDizhuState
from cardgame_ai.games.doudizhu.card_group import CardGroup, CardGroupType
from cardgame_ai.algorithms.human_ai_collaboration import TrustEstimator


class DecisionComponent(ABC):
    """
    决策组件基类

    所有决策组件的抽象基类，定义了统一的接口。
    """

    def __init__(self, name: str):
        """
        初始化决策组件

        Args:
            name: 组件名称
        """
        self.name = name
        self.stats = {
            "calls": 0,
            "time_spent": 0.0,
            "success_rate": 0.0
        }

    @abstractmethod
    def decide(self, state: State, legal_actions: List[Action]) -> Action:
        """
        做出决策

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            选择的动作
        """
        pass

    def update_stats(self, time_spent: float, success: bool = True):
        """
        更新统计信息

        Args:
            time_spent: 决策耗时
            success: 是否成功
        """
        self.stats["calls"] += 1
        self.stats["time_spent"] += time_spent

        # 更新成功率
        if "successes" not in self.stats:
            self.stats["successes"] = 0

        if success:
            self.stats["successes"] += 1

        self.stats["success_rate"] = self.stats["successes"] / self.stats["calls"]

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息字典
        """
        stats = self.stats.copy()

        # 添加ACT相关统计信息（仅当组件定义了 use_act 属性且为 True 时）
        if getattr(self, 'use_act', False):
            stats["act_enabled"] = True
            stats["act_min_simulations"] = getattr(self, 'act_min_simulations', 0)
            stats["act_confidence_threshold"] = getattr(self, 'act_confidence_threshold', 0)
            stats["act_visit_threshold"] = getattr(self, 'act_visit_threshold', 0)
         
            # 如果有提前终止的情况，计算平均节省的计算资源
            if "act_early_stops" in stats:
                early_stops = stats["act_early_stops"]
                total_decisions = stats["calls"]
                stats["act_early_stop_ratio"] = early_stops / total_decisions if total_decisions > 0 else 0
        else:
            stats["act_enabled"] = False

        return stats


class NeuralNetworkComponent(DecisionComponent):
    """
    神经网络决策组件

    封装EfficientZero算法，提供基于神经网络的决策能力。
    """

    def __init__(self, model: EfficientZero, temperature: float = 1.0):
        """
        初始化神经网络组件

        Args:
            model: EfficientZero模型
            temperature: 温度参数，控制探索程度
        """
        super().__init__("neural_network")
        self.model = model
        self.temperature = temperature

        # 添加特定统计信息
        self.stats.update({
            "avg_value": 0.0,
            "avg_policy_entropy": 0.0
        })

    def _mask_illegal_actions(self, policy_logits: torch.Tensor, legal_actions: List[int]) -> torch.Tensor:
        """
        掩盖非法动作，并将logits转换为概率分布
        
        Args:
            policy_logits: 策略网络输出的logits
            legal_actions: 合法动作列表
            
        Returns:
            掩盖非法动作后的概率分布
        """
        # 创建掩码
        mask = torch.zeros_like(policy_logits)
        for action in legal_actions:
            if action < len(mask):
                mask[action] = 1.0
        
        # 应用掩码，将非法动作设为负无穷
        masked_logits = policy_logits * mask - 1e9 * (1 - mask)
        
        # 将logits转换为概率
        policy = F.softmax(masked_logits, dim=-1)
        
        return policy

    def decide(
        self, 
        state: State, 
        legal_actions: List[Action], 
        belief_state: Optional[BeliefState] = None,
        deviation_mapper: Optional["DeviationToExploitMapper"] = None
    ) -> Action:
        """
        使用神经网络做出决策

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            belief_state: 信念状态，包含对手手牌的概率分布
            deviation_mapper: 偏离信号剥削映射器，用于调整策略网络输出

        Returns:
            选择的动作
        """
        start_time = time.time()

        try:
            # 使用模型预测策略和价值
            policy_logits, value = self.model.predict(state)
            
            # 如果有偏离剥削映射器，调整策略logits
            if deviation_mapper is not None:
                # 记录原始logits
                original_logits = policy_logits.clone()
                
                # 应用偏离剥削映射
                policy_logits = deviation_mapper.adjust_policy_logits(
                    policy_logits, 
                    state, 
                    legal_actions
                )
                
                # 记录调整情况
                logging.debug("应用偏离剥削映射器调整策略网络输出")

            # 限制为合法动作
            policy = self._mask_illegal_actions(policy_logits, legal_actions)
            
            # 应用温度
            if self.temperature != 1.0:
                policy = torch.pow(policy, 1.0 / self.temperature)
                policy = policy / policy.sum()

            # 计算策略熵
            entropy = -torch.sum(policy * torch.log(policy + 1e-10))

            # 更新统计信息
            self.stats["avg_value"] = (
                (self.stats["avg_value"] * (self.stats["calls"] - 1) + value.item()) / 
                self.stats["calls"]
            )
            self.stats["avg_policy_entropy"] = (
                (self.stats["avg_policy_entropy"] * (self.stats["calls"] - 1) + entropy.item()) / 
                self.stats["calls"]
            )

            # 从策略中采样动作
            action_probs = policy.detach().cpu().numpy()
            action_idx = np.random.choice(len(action_probs), p=action_probs)
            
            # 如果采样到的动作不在合法动作中，选择概率最高的合法动作
            if action_idx not in legal_actions:
                legal_probs = [(i, action_probs[i]) for i in legal_actions]
                action_idx = max(legal_probs, key=lambda x: x[1])[0]

            # 记录决策信息
            logging.debug(f"神经网络预测值: {value.item():.4f}, 策略熵: {entropy.item():.4f}")

            # 更新统计信息
            time_spent = time.time() - start_time
            self.update_stats(time_spent)

            return action_idx
        except Exception as e:
            logging.error(f"神经网络决策错误: {e}")
            time_spent = time.time() - start_time
            self.update_stats(time_spent, success=False)

            # 出错时返回随机动作
            return random.choice(legal_actions) if legal_actions else None


class SearchComponent(DecisionComponent):
    """
    搜索决策组件

    封装MCTS搜索算法，提供基于搜索的决策能力。
    支持动态调整模拟次数，根据关键决策点检测结果增加计算资源。
    """

    def __init__(
        self,
        model: Any,
        num_simulations: int = 50,
        discount: float = 0.997,
        critical_multiplier: int = 10,
        use_act: bool = True,
        act_min_simulations: int = 10,
        act_confidence_threshold: float = 0.95,
        act_visit_threshold: int = 20,
        max_time_ms: Optional[int] = None
    ):
        """
        初始化搜索组件

        Args:
            model: 用于搜索的模型，需要实现represent、predict和dynamics方法
            num_simulations: 基础模拟次数
            discount: 折扣因子
            critical_multiplier: 关键决策点时的模拟次数倍数
            use_act: 是否使用自适应计算时间
            act_min_simulations: ACT最小模拟次数
            act_confidence_threshold: ACT置信度阈值
            act_visit_threshold: ACT访问次数阈值
            max_time_ms: 最大搜索时间（毫秒）
        """
        super().__init__("search")
        self.model = model
        self.base_num_simulations = num_simulations
        self.critical_multiplier = critical_multiplier
        self.current_num_simulations = num_simulations
        self.use_act = use_act
        self.act_min_simulations = act_min_simulations
        self.act_confidence_threshold = act_confidence_threshold
        self.act_visit_threshold = act_visit_threshold
        self.max_time_ms = max_time_ms

        # 创建MCTS搜索器
        self.mcts = MCTS(
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=0.25,
            exploration_fraction=0.25,
            pb_c_base=19652,
            pb_c_init=1.25,
            root_exploration_noise=True,
            use_act=use_act,
            act_min_simulations=act_min_simulations,
            act_confidence_threshold=act_confidence_threshold,
            act_visit_threshold=act_visit_threshold
        )

        # 添加特定统计信息
        self.stats.update({
            "avg_search_depth": 0.0,
            "avg_value_improvement": 0.0,
            "critical_decisions": 0,
            "avg_simulations": num_simulations
        })

    def set_simulation_count(self, count: int):
        """
        设置模拟次数

        Args:
            count: 新的模拟次数
        """
        self.current_num_simulations = count
        self.mcts.num_simulations = count

    def decide(
        self, 
        state: State, 
        legal_actions: List[Action], 
        is_critical: bool = False, 
        belief_state: Optional[BeliefState] = None,
        deviation_mapper: Optional["DeviationToExploitMapper"] = None
    ) -> Action:
        """
        使用MCTS搜索做出决策

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            is_critical: 是否为关键决策点
            belief_state: 信念状态，包含对手手牌的概率分布
            deviation_mapper: 偏离信号剥削映射器，用于调整策略网络输出

        Returns:
            选择的动作
        """
        start_time = time.time()

        try:
            # 如果是关键决策点，增加模拟次数
            if is_critical:
                self.current_num_simulations = int(self.base_num_simulations * self.critical_multiplier)
                logging.info(f"检测到关键决策点，增加MCTS模拟次数至 {self.current_num_simulations}")
            else:
                self.current_num_simulations = self.base_num_simulations

            # 设置模拟次数
            self.mcts.num_simulations = self.current_num_simulations

            # 如果有信念状态，更新根节点先验
            if belief_state is not None:
                belief_tensor = belief_state.get_tensor()
                root_prior_fn = lambda: {"belief": belief_tensor}
                self.mcts.set_root_prior_fn(root_prior_fn)
                logging.debug(f"搜索组件使用信念状态，置信度: {belief_state.confidence:.4f}")

            # 如果启用了偏离信号剥削映射器，创建适配器函数
            if deviation_mapper is not None and hasattr(self.model, 'get_policy_value'):
                original_predict_fn = self.model.predict
                
                # 创建包装函数，在原始predict输出后应用偏离信号剥削映射
                def predict_with_exploit(state, *args, **kwargs):
                    # 调用原始predict函数
                    policy_logits, value = original_predict_fn(state, *args, **kwargs)
                    
                    # 应用偏离信号剥削映射
                    adjusted_logits = deviation_mapper.adjust_policy_logits(
                        policy_logits.clone(), 
                        state,
                        legal_actions
                    )
                    
                    return adjusted_logits, value
                
                # 临时替换predict函数
                self.model.predict = predict_with_exploit
                
                logging.debug(f"搜索组件使用偏离剥削映射器调整策略网络输出")
            
            try:
                # 执行MCTS搜索
                action, stats = self.mcts.run(self.model, state, legal_actions)
                
                # 更新统计信息
                if "avg_simulations" not in self.stats:
                    self.stats["avg_simulations"] = 0
                    self.stats["total_simulations"] = 0
                    self.stats["count"] = 0
                
                self.stats["total_simulations"] += stats.get("num_simulations", self.current_num_simulations)
                self.stats["count"] += 1
                self.stats["avg_simulations"] = self.stats["total_simulations"] / self.stats["count"]
                
                # 记录ACT相关统计信息
                if "act_enabled" in stats:
                    if "act_early_stops" not in self.stats:
                        self.stats["act_early_stops"] = 0
                    
                    if stats.get("act_stopped_early", False):
                        self.stats["act_early_stops"] += 1
                
                # 记录树统计信息
                if "max_depth" in stats:
                    if "max_tree_depth" not in self.stats:
                        self.stats["max_tree_depth"] = 0
                    self.stats["max_tree_depth"] = max(self.stats["max_tree_depth"], stats["max_depth"])
            finally:
                # 如果替换了predict函数，恢复原始函数
                if deviation_mapper is not None and hasattr(self.model, 'get_policy_value'):
                    self.model.predict = original_predict_fn

            # 更新统计信息
            time_spent = time.time() - start_time
            self.update_stats(time_spent)

            return action
        except Exception as e:
            logging.error(f"搜索决策错误: {e}")
            time_spent = time.time() - start_time
            self.update_stats(time_spent, success=False)

            # 出错时返回随机动作
            return random.choice(legal_actions) if legal_actions else None


class RuleComponent(DecisionComponent):
    """
    规则决策组件

    封装规则智能体，提供基于规则的决策能力。
    """

    def __init__(self, rule_agent: RuleBasedAgent):
        """
        初始化规则组件

        Args:
            rule_agent: 规则智能体
        """
        super().__init__("rule")
        self.rule_agent = rule_agent

    def decide(
        self, 
        state: State, 
        legal_actions: List[Action], 
        belief_state: Optional[BeliefState] = None,
        deviation_mapper: Optional["DeviationToExploitMapper"] = None
    ) -> Action:
        """
        使用规则做出决策

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            belief_state: 信念状态，包含对手手牌的概率分布
            deviation_mapper: 偏离信号剥削映射器（规则组件不使用此参数）

        Returns:
            选择的动作
        """
        start_time = time.time()

        try:
            # 使用规则智能体做出决策
            action = self.rule_agent.act(state, legal_actions)

            # 更新统计信息
            time_spent = time.time() - start_time
            self.update_stats(time_spent)

            return action
        except Exception as e:
            logging.error(f"规则决策错误: {e}")
            time_spent = time.time() - start_time
            self.update_stats(time_spent, success=False)

            # 出错时返回随机动作
            return random.choice(legal_actions) if legal_actions else None


class HRLComponent(DecisionComponent):
    """
    层次化强化学习决策组件

    封装高层和低层策略，提供层次化决策能力。
    可以根据状态复杂度动态选择决策模式。
    """

    def __init__(
        self,
        high_level_policy: HighLevelPolicy,
        low_level_policy: LowLevelPolicy,
        complexity_threshold: float = 0.6,
        confidence_threshold: float = 0.8,
        dynamic_scheduling: bool = True,
        use_history: bool = True,
        history_window: int = 5
    ):
        """
        初始化HRL组件

        Args:
            high_level_policy: 高层策略
            low_level_policy: 低层策略
            complexity_threshold: 复杂度阈值，高于此值使用高层策略
            confidence_threshold: 置信度阈值，高于此值直接使用低层策略
            dynamic_scheduling: 是否使用动态调度
            use_history: 是否使用历史信息
            history_window: 历史窗口大小
        """
        super().__init__("hrl")

        # 创建层次控制器
        self.controller = HierarchicalController(
            high_level_policy=high_level_policy,
            low_level_policy=low_level_policy,
            complexity_threshold=complexity_threshold,
            confidence_threshold=confidence_threshold,
            dynamic_scheduling=dynamic_scheduling,
            use_history=use_history,
            history_window=history_window
        )

        # 添加特定统计信息
        self.stats.update({
            "high_level_calls": 0,
            "low_level_calls": 0,
            "direct_calls": 0,
            "avg_complexity": 0.0,
            "avg_confidence": 0.0
        })

    def decide(self, state: State, legal_actions: List[Action], belief_state: Optional[BeliefState] = None, deviation_mapper: Optional["DeviationToExploitMapper"] = None) -> Action:
        """
        使用层次化强化学习做出决策

        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            belief_state: 信念状态，包含对手手牌的概率分布
            deviation_mapper: 偏离信号剥削映射器，用于调整策略网络输出

        Returns:
            选择的动作
        """
        start_time = time.time()

        try:
            # 如果有偏离剥削映射器，设置临时包装器
            original_low_level_network = None
            
            if deviation_mapper is not None and hasattr(self.controller, 'low_level_policy'):
                low_level_policy = self.controller.low_level_policy
                
                if hasattr(low_level_policy, 'network'):
                    # 保存原始网络引用
                    original_low_level_network = low_level_policy.network
                    
                    # 创建包装网络类
                    class WrappedNetwork(nn.Module):
                        def __init__(self, original_network, deviation_mapper, state, legal_actions):
                            super().__init__()
                            self.original_network = original_network
                            self.deviation_mapper = deviation_mapper
                            self.state = state
                            self.legal_actions = legal_actions
                        
                        def forward(self, x):
                            # 调用原始网络
                            logits = self.original_network(x)
                            
                            # 应用偏离剥削映射
                            adjusted_logits = self.deviation_mapper.adjust_policy_logits(
                                logits.clone(),
                                self.state, 
                                self.legal_actions
                            )
                            
                            return adjusted_logits
                    
                    # 替换为包装网络
                    low_level_policy.network = WrappedNetwork(
                        original_low_level_network,
                        deviation_mapper,
                        state,
                        legal_actions
                    )
                    
                    logging.debug("HRL组件应用了偏离剥削映射器")
            
            # 使用层次控制器做出决策
            # 如果有信念状态，将其传递给层次控制器
            if belief_state is not None and hasattr(self.controller, 'decide_with_belief'):
                action, decision_info = self.controller.decide_with_belief(state, legal_actions, belief_state)
                # 记录使用信念状态的情况
                if "belief_usage" not in self.stats:
                    self.stats["belief_usage"] = 0
                self.stats["belief_usage"] += 1
            else:
                action, decision_info = self.controller.decide(state, legal_actions)
            
            # 恢复原始网络
            if original_low_level_network is not None and hasattr(self.controller, 'low_level_policy'):
                self.controller.low_level_policy.network = original_low_level_network

            # 更新统计信息
            mode = decision_info.get("mode", "unknown")
            if mode == "high_level":
                self.stats["high_level_calls"] += 1
            elif mode == "low_level":
                self.stats["low_level_calls"] += 1
            elif mode == "direct":
                self.stats["direct_calls"] += 1

            # 更新复杂度和置信度统计
            complexity = decision_info.get("complexity", 0.0)
            confidence = decision_info.get("confidence", 0.0)

            total_calls = self.stats["calls"] + 1  # 包括当前调用
            self.stats["avg_complexity"] = (
                (self.stats["avg_complexity"] * (total_calls - 1) + complexity) /
                total_calls
            )
            self.stats["avg_confidence"] = (
                (self.stats["avg_confidence"] * (total_calls - 1) + confidence) /
                total_calls
            )

            # 记录决策模式
            logging.info(f"HRL决策模式: {mode}, 复杂度: {complexity:.2f}, 置信度: {confidence:.2f}")

            # 更新统计信息
            time_spent = time.time() - start_time
            self.update_stats(time_spent)

            return action
        except Exception as e:
            logging.error(f"HRL决策错误: {e}")
            time_spent = time.time() - start_time
            self.update_stats(time_spent, success=False)

            # 出错时返回随机动作
            return random.choice(legal_actions) if legal_actions else None

    def get_exploitation_action(self, state: State, legal_actions: List[Action]) -> Optional[Action]:
        """
        获取剥削动作

        检测对手是否偏离GTO，并生成针对性剥削动作。

        Args:
            state: 当前状态
            legal_actions: 合法动作列表

        Returns:
            剥削动作，如果不应剥削则返回None
        """
        if not self.enable_exploitation or self.deviation_detector is None or self.deviation_exploit_mapper is None:
            return None

        # 如果没有足够的对手历史记录，无法进行剥削
        if len(self.opponent_state_history) < 3:
            return None

        try:
            # 获取最近的几个状态和动作来分析模式
            recent_states = self.opponent_state_history[-3:]
            recent_actions = self.opponent_action_history[-3:]

            # 使用偏离剥削映射器，评估是否有可剥削的模式
            is_exploitable, exploitation_info = self.deviation_exploit_mapper.detect_exploitable_pattern(
                recent_states, 
                recent_actions
            )

            if is_exploitable:
                # 检测到可剥削模式
                pattern_type = exploitation_info.get("pattern_type", "unknown")
                confidence = exploitation_info.get("confidence", 0.0)
                
                # 记录剥削检测
                if "exploitation_patterns" not in self.stats:
                    self.stats["exploitation_patterns"] = {}
                
                if pattern_type not in self.stats["exploitation_patterns"]:
                    self.stats["exploitation_patterns"][pattern_type] = 0
                self.stats["exploitation_patterns"][pattern_type] += 1
                
                # 如果置信度足够高，生成剥削动作
                if confidence >= self.deviation_exploit_mapper.min_confidence:
                    # 使用神经网络组件生成基于剥削的动作
                    neural_component = self.components.get("neural_network")
                    if neural_component is not None:
                        # 使用偏离剥削映射器调整策略网络输出
                        exploitation_action = neural_component.decide(
                            state, 
                            legal_actions, 
                            deviation_mapper=self.deviation_exploit_mapper
                        )
                        
                        # 记录剥削动作
                        self.stats["exploitation_actions"] = self.stats.get("exploitation_actions", 0) + 1
                        
                        logging.info(f"使用剥削策略，模式类型: {pattern_type}, 置信度: {confidence:.4f}")
                        return exploitation_action
        
        except Exception as e:
            logging.error(f"剥削动作生成错误: {e}")
        
        return None

    def process_human_action(self, state: State, human_action: Action, legal_actions: List[Action]) -> Action:
        """
        处理人类动作，根据信任度估计决定是否采纳人类动作或使用AI建议动作

        Args:
            state: 当前游戏状态
            human_action: 人类玩家的动作
            legal_actions: 合法动作列表

        Returns:
            Action: 最终决策动作
        """
        # 如果没有信任度估计器，直接采纳人类动作
        if self.trust_estimator is None:
            return human_action

        # 获取AI建议动作
        ai_action = self.act(state, legal_actions)

        # 更新信任度
        if human_action is not None and ai_action is not None:
            self.trust_estimator.update_trust_level(human_action, ai_action, state)

        # 获取介入程度
        intervention_level = self.trust_estimator.get_ai_intervention_level()

        # 记录决策信息
        if "human_ai_decisions" not in self.stats:
            self.stats["human_ai_decisions"] = []

        self.stats["human_ai_decisions"].append({
            "trust_level": self.trust_estimator.get_trust_level(),
            "intervention_level": intervention_level,
            "human_action": str(human_action),
            "ai_action": str(ai_action)
        })

        # 根据介入程度决定最终动作
        # 这里使用0.7作为介入阈值，可以根据需要调整
        if intervention_level > 0.7:
            # AI介入
            logging.info(f"AI介入 (介入程度: {intervention_level:.2f})")
            return ai_action
        else:
            # 采纳人类决策
            logging.info(f"采纳人类决策 (介入程度: {intervention_level:.2f})")
            return human_action

    def update_belief_tracker(self, state: State, action: Action, player_id: int):
        """
        更新信念追踪器

        Args:
            state: 当前游戏状态
            action: 执行的动作
            player_id: 执行动作的玩家ID
        """
        if self.belief_tracker is None:
            return

        try:
            # 更新信念追踪器
            self.belief_tracker.update(player_id, action, state)

            # 记录更新次数
            if "belief_tracker_updates" not in self.stats:
                self.stats["belief_tracker_updates"] = 0
            self.stats["belief_tracker_updates"] += 1

            logging.debug(f"更新信念追踪器：玩家ID={player_id}")
        except Exception as e:
            logging.error(f"更新信念追踪器错误: {e}")

    def update_reward(self, reward: float):
        """
        更新奖励

        Args:
            reward: 奖励值
        """
        # 记录奖励
        self.stats["rewards"].append(reward)

        # 获取最后使用的组件
        if self.meta_controller.selection_history:
            last_component = self.meta_controller.selection_history[-1]
            # 更新元控制器的组件权重
            self.meta_controller.update_weights(last_component, reward)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息字典
        """
        stats = self.stats.copy()

        # 添加元控制器的统计信息
        stats["meta_controller"] = self.meta_controller.stats

        # 添加各组件的统计信息（如果组件实现get_stats则调用，否则返回空字典）
        stats["components"] = {}
        for name, component in self.components.items():
            stats["components"][name] = getattr(component, 'get_stats', lambda: {})()

        # 添加关键决策点检测统计信息
        if self.key_moment_detector is not None:
            total_decisions = self.stats["decisions"]
            critical_moments = self.stats["critical_moments"]

            # 计算关键决策点比例
            critical_ratio = critical_moments / total_decisions if total_decisions > 0 else 0

            # 计算平均关键程度评分
            avg_criticality = (
                sum(self.stats["critical_moment_scores"]) / len(self.stats["critical_moment_scores"])
                if self.stats["critical_moment_scores"] else 0
            )

            # 添加关键决策点统计
            stats["key_moment_detection"] = {
                "critical_moments": critical_moments,
                "critical_ratio": critical_ratio,
                "avg_criticality": avg_criticality,
                "critical_threshold": self.critical_threshold
            }

            # 如果搜索组件存在，添加动态计算预算统计
            if "search" in self.components:
                search_stats = self.components["search"].get_stats()
                stats["key_moment_detection"].update({
                    "base_simulations": self.components["search"].base_num_simulations,
                    "critical_multiplier": self.components["search"].critical_multiplier,
                    "avg_simulations": search_stats.get("avg_simulations", 0)
                })

            # 添加动态预算分配统计信息
            if self.dynamic_budget_allocator is not None:
                # 获取动态预算分配器的统计信息
                budget_stats = self.dynamic_budget_allocator.get_stats()

                # 添加动态预算分配统计
                stats["dynamic_budget"] = {
                    "enabled": True,
                    "base_budget": self.dynamic_budget_allocator.base_budget,
                    "max_budget": self.dynamic_budget_allocator.max_budget,
                    "amplification_factor": self.dynamic_budget_allocator.amplification_factor,
                    "adaptive_scaling": self.dynamic_budget_allocator.adaptive_scaling,
                    "total_allocations": budget_stats.get("total_allocations", 0),
                    "critical_allocations": budget_stats.get("critical_allocations", 0),
                    "critical_ratio": budget_stats.get("critical_ratio", 0.0),
                    "avg_budget": budget_stats.get("avg_budget", 0.0),
                    "max_budget_used": budget_stats.get("max_budget_used", 0),
                    "avg_criticality_score": budget_stats.get("avg_criticality_score", 0.0)
                }

                # 添加系统使用动态预算的统计
                if "budget_allocations" in self.stats:
                    stats["dynamic_budget"].update({
                        "system_allocations": self.stats.get("budget_allocations", 0),
                        "system_critical_allocations": self.stats.get("critical_budget_allocations", 0),
                        "system_avg_budget": self.stats.get("avg_budget", 0.0),
                        "system_total_budget": self.stats.get("total_budget_used", 0)
                    })

                # 添加动态预算使用情况
                if "dynamic_budget_usage" in self.stats:
                    stats["dynamic_budget"]["usage_count"] = self.stats["dynamic_budget_usage"]

                # 添加ACT相关统计信息
                if search_stats.get("act_enabled", False):
                    if "act_info" not in stats:
                        stats["act_info"] = {}
                    stats["act_info"].update({
                        "enabled": True,
                        "min_simulations": search_stats.get("act_min_simulations", 0),
                        "confidence_threshold": search_stats.get("act_confidence_threshold", 0),
                        "visit_threshold": search_stats.get("act_visit_threshold", 0),
                        "early_stops": search_stats.get("act_early_stops", 0),
                        "early_stop_ratio": search_stats.get("act_early_stop_ratio", 0),
                        "avg_saved_ratio": search_stats.get("act_avg_saved_ratio", 0)
                    })

        # 添加残局特化处理模块统计信息
        if "endgame_decisions" in self.stats:
            total_decisions = self.stats["decisions"]
            endgame_decisions = self.stats["endgame_decisions"]

            # 计算残局决策比例
            endgame_ratio = endgame_decisions / total_decisions if total_decisions > 0 else 0

            # 添加残局统计
            stats["endgame_handling"] = {
                "endgame_decisions": endgame_decisions,
                "endgame_ratio": endgame_ratio,
                "endgame_types": self.stats.get("endgame_types", {})
            }

            # 如果有残局特化处理组件，添加其统计信息
            if "endgame_specialist" in self.components:
                endgame_specialist_stats = self.components["endgame_specialist"].get_stats()
                stats["endgame_handling"]["specialist"] = {
                    "calls": endgame_specialist_stats.get("calls", 0),
                    "success_rate": endgame_specialist_stats.get("success_rate", 0.0),
                    "endgame_types_used": endgame_specialist_stats.get("endgame_types_used", {}),
                    "success_rate_by_type": endgame_specialist_stats.get("success_rate_by_type", {})
                }

        # 添加符号推理组件统计信息
        if self.symbolic_component is not None:
            # 获取符号推理组件的统计信息
            symbolic_stats = self.symbolic_component.get_stats()

            # 计算符号推理决策比例
            total_decisions = self.stats["decisions"]
            symbolic_decisions = self.stats.get("symbolic_decisions", 0)
            symbolic_ratio = symbolic_decisions / total_decisions if total_decisions > 0 else 0

            # 添加符号推理统计
            stats["symbolic_reasoning"] = {
                "calls": symbolic_stats.get("calls", 0),
                "successful_solves": symbolic_stats.get("successful_solves", 0),
                "symbolic_decisions": symbolic_decisions,
                "symbolic_ratio": symbolic_ratio,
                "solve_types": symbolic_stats.get("solve_types", {})
            }

        # 添加偏离检测统计信息
        if self.deviation_detector is not None:
            # 获取偏离检测器的统计信息
            deviation_stats = self.deviation_detector.get_stats()

            # 计算偏离检测比例
            total_decisions = self.stats["decisions"]
            deviation_detections = self.stats.get("deviation_detections", 0)
            exploitation_actions = self.stats.get("exploitation_actions", 0)

            deviation_ratio = deviation_detections / max(1, len(self.opponent_action_history))
            exploitation_ratio = exploitation_actions / total_decisions if total_decisions > 0 else 0

            # 添加偏离检测统计
            stats["deviation_detection"] = {
                "enabled": True,
                "deviation_threshold": self.deviation_threshold,
                "enable_exploitation": self.enable_exploitation,
                "deviation_detections": deviation_detections,
                "exploitation_actions": exploitation_actions,
                "deviation_ratio": deviation_ratio,
                "exploitation_ratio": exploitation_ratio,
                "opponent_actions_recorded": len(self.opponent_action_history),
                "deviation_pattern": deviation_stats.get("pattern", {})
            }

            # 添加详细的偏离统计
            if "avg_deviation_score" in deviation_stats:
                stats["deviation_detection"].update({
                    "avg_deviation_score": deviation_stats["avg_deviation_score"],
                    "max_deviation_score": deviation_stats["max_deviation_score"],
                    "total_checks": deviation_stats["total_checks"]
                })

        # 添加HRL统计信息
        if self.enable_hrl and "hrl" in self.components:
            # 获取HRL组件的统计信息
            hrl_stats = self.components["hrl"].get_stats()

            # 计算HRL决策比例
            total_decisions = self.stats["decisions"]
            hrl_decisions = self.stats["component_usage"].get("hrl", 0)
            hrl_ratio = hrl_decisions / total_decisions if total_decisions > 0 else 0

            # 计算各种调用的比例
            high_level_calls = self.stats.get("hrl_high_level_calls", 0)
            low_level_calls = self.stats.get("hrl_low_level_calls", 0)
            direct_calls = self.stats.get("hrl_direct_calls", 0)

            high_level_ratio = high_level_calls / max(1, hrl_decisions)
            low_level_ratio = low_level_calls / max(1, hrl_decisions)
            direct_ratio = direct_calls / max(1, hrl_decisions)

            # 添加HRL统计
            stats["hrl"] = {
                "enabled": True,
                "hrl_decisions": hrl_decisions,
                "hrl_ratio": hrl_ratio,
                "high_level_calls": high_level_calls,
                "low_level_calls": low_level_calls,
                "direct_calls": direct_calls,
                "high_level_ratio": high_level_ratio,
                "low_level_ratio": low_level_ratio,
                "direct_ratio": direct_ratio,
                "complexity_threshold": self.complexity_threshold,
                "confidence_threshold": self.confidence_threshold,
                "dynamic_scheduling": self.dynamic_scheduling,
                "use_history": self.use_history,
                "history_window": self.history_window,
                "controller_stats": hrl_stats.get("controller", {})
            }

            # 添加详细的HRL统计
            if "avg_complexity" in hrl_stats:
                stats["hrl"].update({
                    "avg_complexity": hrl_stats["avg_complexity"],
                    "avg_confidence": hrl_stats["avg_confidence"]
                })

        # 添加信念追踪器统计信息
        usage = self.stats.get('belief_tracker_usage', 0)
        decisions = self.stats.get('decisions', 0)
        ratio = usage / decisions if decisions > 0 else 0
        confidences = self.stats.get('belief_confidence', [])
        raw_conf = sum(confidences) / len(confidences) if confidences else 0.0
        # 四舍五入平均置信度，避免浮点误差
        avg_conf = round(raw_conf, 4)
        stats['belief_tracking'] = {
            'enabled': True if self.belief_tracker is not None else False,
            'usage_count': usage,
            'usage_ratio': ratio,
            'avg_confidence': avg_conf
        }

        return stats

    # 添加抽象方法实现，避免类仍为抽象
    def train(self, experience) -> Dict[str, Any]:
        """
        混合决策系统不进行模型训练，直接返回空字典
        """
        return {}

    def save(self, path: str) -> None:
        """
        混合决策系统无需保存功能
        """
        pass

    def load(self, path: str) -> None:
        """
        混合决策系统无需加载功能
        """
        pass