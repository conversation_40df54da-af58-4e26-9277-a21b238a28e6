"""
MuZero算法模块

实现MuZero算法，一种先进的模型驱动强化学习算法，
能够在不知道环境动力学的情况下学习规划。
"""
import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import deque
import random
import logging

from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.core.algorithm import ModelBasedAlgorithm
from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.replay_buffer import PrioritizedReplayBuffer
from cardgame_ai.algorithms.scheduler import StepLRScheduler


class RepresentationNetwork(nn.Module):
    """
    表示网络

    将观察转换为隐藏状态，作为模型的初始状态输入。
    """

    def __init__(self, input_dim: int, hidden_dim: int, state_dim: int, use_resnet: bool = True):
        """
        初始化表示网络

        Args:
            input_dim (int): 输入维度（观察空间）
            hidden_dim (int): 隐藏层维度
            state_dim (int): 状态维度（输出）
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
        """
        super(RepresentationNetwork, self).__init__()

        if use_resnet:
            # 使用残差网络架构
            self.initial = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU()
            )

            # 残差块
            self.residual_blocks = nn.ModuleList([
                ResidualBlock(hidden_dim) for _ in range(2)
            ])

            # 输出层
            self.output = nn.Linear(hidden_dim, state_dim)
        else:
            # 使用标准MLP架构
            self.model = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, state_dim)
            )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入观察

        Returns:
            torch.Tensor: 隐藏状态
        """
        if hasattr(self, 'initial'):  # 使用残差网络
            x = self.initial(x)
            for block in self.residual_blocks:
                x = block(x)
            return self.output(x)
        else:  # 使用标准MLP
            return self.model(x)


class DynamicsNetwork(nn.Module):
    """
    动态网络

    预测给定当前隐藏状态和动作下的下一个隐藏状态和奖励。
    """

    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int, use_resnet: bool = True):
        """
        初始化动态网络

        Args:
            state_dim (int): 状态维度（输入和输出）
            action_dim (int): 动作维度
            hidden_dim (int): 隐藏层维度
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
        """
        super(DynamicsNetwork, self).__init__()

        # 动作编码
        self.action_encoder = nn.Embedding(action_dim, hidden_dim // 4)

        if use_resnet:
            # 使用残差网络架构
            self.initial = nn.Sequential(
                nn.Linear(state_dim + hidden_dim // 4, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU()
            )

            # 残差块
            self.residual_blocks = nn.ModuleList([
                ResidualBlock(hidden_dim) for _ in range(2)
            ])

            # 状态输出
            self.state_output = nn.Linear(hidden_dim, state_dim)

            # 奖励输出
            self.reward_output = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, 1)
            )
        else:
            # 状态转移网络
            self.state_network = nn.Sequential(
                nn.Linear(state_dim + hidden_dim // 4, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, state_dim)
            )

            # 奖励预测网络
            self.reward_network = nn.Sequential(
                nn.Linear(state_dim + hidden_dim // 4, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, 1)
            )

    def forward(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播

        Args:
            state (torch.Tensor): 当前隐藏状态
            action (torch.Tensor): 执行的动作

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 下一个隐藏状态和奖励
        """
        action_embedding = self.action_encoder(action)
        x = torch.cat([state, action_embedding], dim=1)

        if hasattr(self, 'initial'):  # 使用残差网络
            x = self.initial(x)
            for block in self.residual_blocks:
                x = block(x)
            next_state = self.state_output(x)
            reward = self.reward_output(x)
        else:  # 使用标准MLP
            next_state = self.state_network(x)
            reward = self.reward_network(x)

        return next_state, reward.squeeze(-1)


class PredictionNetwork(nn.Module):
    """
    预测网络

    从隐藏状态预测策略（动作概率）和状态价值。
    """

    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int, use_resnet: bool = True):
        """
        初始化预测网络

        Args:
            state_dim (int): 状态维度（输入）
            action_dim (int): 动作维度（输出）
            hidden_dim (int): 隐藏层维度
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
        """
        super(PredictionNetwork, self).__init__()

        if use_resnet:
            # 使用残差网络架构
            self.initial = nn.Sequential(
                nn.Linear(state_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU()
            )

            # 残差块
            self.residual_blocks = nn.ModuleList([
                ResidualBlock(hidden_dim) for _ in range(2)
            ])

            # 策略头
            self.policy_head = nn.Linear(hidden_dim, action_dim)

            # 价值头
            self.value_head = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, 1)
            )
        else:
            # 共享网络
            self.shared_network = nn.Sequential(
                nn.Linear(state_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU()
            )

            # 策略头
            self.policy_head = nn.Linear(hidden_dim, action_dim)

            # 价值头
            self.value_head = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, 1)
            )

    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播

        Args:
            state (torch.Tensor): 隐藏状态

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        if hasattr(self, 'initial'):  # 使用残差网络
            x = self.initial(state)
            for block in self.residual_blocks:
                x = block(x)
            policy_logits = self.policy_head(x)
            value = self.value_head(x)
        else:  # 使用标准MLP
            x = self.shared_network(state)
            policy_logits = self.policy_head(x)
            value = self.value_head(x)

        return policy_logits, value.squeeze(-1)


class ResidualBlock(nn.Module):
    """
    残差块

    实现具有批归一化的残差连接。
    """

    def __init__(self, hidden_dim: int):
        """
        初始化残差块

        Args:
            hidden_dim (int): 隐藏层维度
        """
        super(ResidualBlock, self).__init__()

        self.block = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim)
        )

        self.relu = nn.ReLU()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入特征

        Returns:
            torch.Tensor: 残差连接后的特征
        """
        identity = x
        out = self.block(x)
        out += identity
        out = self.relu(out)
        return out


class MuZeroModel:
    """
    MuZero完整模型

    组合表示网络、动态网络和预测网络，提供MuZero所需的全部功能。
    """

    def __init__(
        self,
        observation_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        device: str = None
    ):
        """
        初始化MuZero模型

        Args:
            observation_shape (Tuple[int, ...]): 观察空间形状
            action_shape (Tuple[int, ...]): 动作空间形状
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            state_dim (int, optional): 隐藏状态维度. Defaults to 64.
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
            device (str, optional): 计算设备. Defaults to None.
        """
        # 计算输入和输出维度
        self.observation_dim = np.prod(observation_shape)
        self.action_dim = np.prod(action_shape)
        self.state_dim = state_dim

        # 设置设备
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建网络
        self.representation_network = RepresentationNetwork(
            self.observation_dim, hidden_dim, state_dim, use_resnet
        ).to(self.device)

        self.dynamics_network = DynamicsNetwork(
            state_dim, self.action_dim, hidden_dim, use_resnet
        ).to(self.device)

        self.prediction_network = PredictionNetwork(
            state_dim, self.action_dim, hidden_dim, use_resnet
        ).to(self.device)

    def represent(self, observation: np.ndarray) -> torch.Tensor:
        """
        将观察转换为隐藏状态

        Args:
            observation (np.ndarray): 观察

        Returns:
            torch.Tensor: 隐藏状态
        """
        # 保存当前训练状态
        training = self.representation_network.training

        # 切换到评估模式
        self.representation_network.eval()

        try:
            # 处理输入
            if isinstance(observation, np.ndarray):
                observation = torch.FloatTensor(observation).to(self.device)

            # 确保形状正确
            if len(observation.shape) == 1:
                observation = observation.unsqueeze(0)

            # 重塑观察
            if observation.shape[1:]:
                observation = observation.reshape(observation.shape[0], -1)

            # 转换为隐藏状态
            with torch.no_grad():
                hidden_state = self.representation_network(observation)

            return hidden_state
        finally:
            # 恢复原始训练状态
            self.representation_network.train(training)

    def dynamics(self, hidden_state: torch.Tensor, action: Union[int, torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测下一个隐藏状态和奖励

        Args:
            hidden_state (torch.Tensor): 当前隐藏状态
            action (Union[int, torch.Tensor]): 执行的动作

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 下一个隐藏状态和奖励
        """
        # 保存当前训练状态
        training = self.dynamics_network.training

        # 切换到评估模式
        self.dynamics_network.eval()

        try:
            # 处理动作输入
            if isinstance(action, int):
                action = torch.LongTensor([action]).to(self.device)
            elif isinstance(action, np.ndarray):
                action = torch.LongTensor(action).to(self.device)
            elif hasattr(action, 'value'):  # 处理枚举类型，如BidAction
                action = torch.LongTensor([action.value]).to(self.device)
            elif hasattr(action, 'to_index'):  # 处理具有to_index方法的对象
                action = torch.LongTensor([action.to_index()]).to(self.device)
            elif hasattr(action, 'card_type'):  # 处理CardGroup类型
                # 将CardGroup转换为整数索引
                # 这里使用简化的方法，实际应用中可能需要更复杂的映射
                if action.card_type.value < 100:  # 假设牌型枚举值小于100
                    action = torch.LongTensor([action.card_type.value]).to(self.device)
                else:
                    # 如果牌型枚举值过大，使用一个默认值
                    action = torch.LongTensor([0]).to(self.device)
            else:
                # 尝试将动作转换为整数
                try:
                    action = torch.LongTensor([int(action)]).to(self.device)
                except (TypeError, ValueError):
                    # 如果无法转换，使用默认值
                    action = torch.LongTensor([0]).to(self.device)

            # 确保形状正确
            if len(action.shape) == 0:
                action = action.unsqueeze(0)

            # 预测
            with torch.no_grad():
                next_hidden_state, reward = self.dynamics_network(hidden_state, action)

            return next_hidden_state, reward
        finally:
            # 恢复原始训练状态
            self.dynamics_network.train(training)

    def predict(self, hidden_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测策略和价值

        Args:
            hidden_state (torch.Tensor): 隐藏状态

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        # 保存当前训练状态
        training = self.prediction_network.training

        # 切换到评估模式
        self.prediction_network.eval()

        try:
            with torch.no_grad():
                policy_logits, value = self.prediction_network(hidden_state)

            return policy_logits, value
        finally:
            # 恢复原始训练状态
            self.prediction_network.train(training)

    def parameters(self):
        """
        获取模型的所有参数

        Returns:
            迭代器: 包含模型所有参数的迭代器
        """
        # 返回所有网络的参数
        for param in self.representation_network.parameters():
            yield param
        for param in self.dynamics_network.parameters():
            yield param
        for param in self.prediction_network.parameters():
            yield param


class MuZero(ModelBasedAlgorithm):
    """
    MuZero算法

    实现MuZero算法，一种先进的模型驱动强化学习算法，
    能够在不知道环境动力学的情况下学习规划。
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        num_simulations: int = 50,
        discount: float = 0.997,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: int = 19652,
        pb_c_init: float = 1.25,
        replay_buffer_size: int = 100000,
        batch_size: int = 128,
        num_unroll_steps: int = 5,
        td_steps: int = 10,
        value_loss_weight: float = 1.0,
        policy_loss_weight: float = 1.0,
        consistency_loss_weight: float = 1.0,
        learning_rate: float = 0.001,
        weight_decay: float = 1e-4,
        lr_scheduler: str = 'step',
        device: str = None
    ):
        """
        初始化MuZero算法

        Args:
            state_shape (Tuple[int, ...]): 状态形状
            action_shape (Tuple[int, ...]): 动作形状
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            state_dim (int, optional): 隐藏状态维度. Defaults to 64.
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
            num_simulations (int, optional): 每次行动前的模拟次数. Defaults to 50.
            discount (float, optional): 折扣因子. Defaults to 0.997.
            dirichlet_alpha (float, optional): Dirichlet噪声的参数. Defaults to 0.25.
            exploration_fraction (float, optional): 根节点探索噪声的比例. Defaults to 0.25.
            pb_c_base (int, optional): PUCT公式的基础常数. Defaults to 19652.
            pb_c_init (float, optional): PUCT公式的初始常数. Defaults to 1.25.
            replay_buffer_size (int, optional): 经验回放缓冲区大小. Defaults to 100000.
            batch_size (int, optional): 批次大小. Defaults to 128.
            num_unroll_steps (int, optional): 展开步数. Defaults to 5.
            td_steps (int, optional): 时序差分步数. Defaults to 10.
            value_loss_weight (float, optional): 价值损失权重. Defaults to 1.0.
            policy_loss_weight (float, optional): 策略损失权重. Defaults to 1.0.
            consistency_loss_weight (float, optional): 一致性损失权重. Defaults to 1.0.
            learning_rate (float, optional): 学习率. Defaults to 0.001.
            weight_decay (float, optional): 权重衰减. Defaults to 1e-4.
            lr_scheduler (str, optional): 学习率调度器类型. Defaults to 'step'.
            device (str, optional): 计算设备. Defaults to None.
        """
        super().__init__(state_shape, action_shape, discount)

        # 设置设备
        self.device = device if device is not None else ('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建MuZero模型
        self.model = MuZeroModel(
            state_shape,
            action_shape,
            hidden_dim,
            state_dim,
            use_resnet,
            self.device
        )

        # 创建目标模型（用于EfficientZero的一致性损失）
        self.target_model = MuZeroModel(
            state_shape,
            action_shape,
            hidden_dim,
            state_dim,
            use_resnet,
            self.device
        )
        self._update_target_network(tau=1.0)  # 完全复制参数

        # 创建MCTS搜索器
        self.mcts = MCTS(
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init
        )

        # 创建优化器
        all_params = list(self.model.representation_network.parameters()) + \
                     list(self.model.dynamics_network.parameters()) + \
                     list(self.model.prediction_network.parameters())
        self.optimizer = optim.Adam(all_params, lr=learning_rate, weight_decay=weight_decay)

        # 创建学习率调度器
        if lr_scheduler == 'step':
            self.scheduler = StepLRScheduler(self.optimizer, step_size=10000, gamma=0.1)
        else:
            self.scheduler = None

        # 创建经验回放缓冲区
        self.replay_buffer = PrioritizedReplayBuffer(replay_buffer_size)

        # 设置超参数
        self.batch_size = batch_size
        self.num_unroll_steps = num_unroll_steps
        self.td_steps = td_steps
        self.value_loss_weight = value_loss_weight
        self.policy_loss_weight = policy_loss_weight
        self.consistency_loss_weight = consistency_loss_weight

        # 训练计数器
        self.train_steps = 0

        # 设置日志记录器
        self.logger = logging.getLogger(self.__class__.__name__)

    def predict_next_state(self, state: Union[State, np.ndarray], action: Action) -> Tuple[Union[State, np.ndarray], float]:
        """
        预测执行动作后的下一个状态和奖励

        Args:
            state (Union[State, np.ndarray]): 当前状态
            action (Action): 执行的动作

        Returns:
            Tuple[Union[State, np.ndarray], float]: 预测的下一个状态和奖励
        """
        # 保存当前训练状态
        representation_training = self.model.representation_network.training
        dynamics_training = self.model.dynamics_network.training

        # 切换到评估模式
        self.model.representation_network.eval()
        self.model.dynamics_network.eval()

        try:
            # 获取观察
            if isinstance(state, State):
                observation = state.get_observation()
            else:
                observation = state

            # 转换为隐藏状态
            hidden_state = self.model.represent(observation)

            # 预测下一个隐藏状态和奖励
            next_hidden_state, reward = self.model.dynamics(hidden_state, action)

            # 将隐藏状态转换为可观察状态
            # 注意：这只是一个近似，因为MuZero在隐藏状态空间中运行
            # 实际应用中，应该使用环境模型或直接使用隐藏状态
            if isinstance(state, State):
                # 对于测试，直接返回原始状态
                # 在实际应用中，应该使用环境模型来生成下一个状态
                return state, reward.item()
            else:
                # 返回隐藏状态作为下一个状态的近似
                return next_hidden_state.cpu().numpy(), reward.item()
        finally:
            # 恢复原始训练状态
            self.model.representation_network.train(representation_training)
            self.model.dynamics_network.train(dynamics_training)

    def predict(self, state: Union[State, np.ndarray]) -> Tuple[List[float], float]:
        """
        预测状态的动作概率分布和价值

        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察

        Returns:
            Tuple[List[float], float]: 动作概率分布、状态价值
        """
        # 保存当前训练状态
        representation_training = self.model.representation_network.training
        dynamics_training = self.model.dynamics_network.training
        prediction_training = self.model.prediction_network.training

        # 切换到评估模式
        self.model.representation_network.eval()
        self.model.dynamics_network.eval()
        self.model.prediction_network.eval()

        try:
            # 获取状态
            if isinstance(state, State):
                observation = state.get_observation()
                legal_actions = state.get_legal_actions()
            else:
                observation = state
                legal_actions = None

            # 创建动作掩码
            if legal_actions is not None:
                action_mask = np.zeros(self.action_shape[0], dtype=bool)
                for action in legal_actions:
                    if isinstance(action, int):
                        action_mask[action] = True
                    else:
                        # 处理非整数类型的动作
                        if hasattr(action, 'value'):  # 处理枚举类型，如BidAction
                            action_idx = action.value
                        elif hasattr(action, 'to_index'):  # 处理具有to_index方法的对象
                            action_idx = action.to_index()
                        elif hasattr(action, 'card_type'):  # 处理CardGroup类型
                            # 将CardGroup转换为整数索引
                            if hasattr(action.card_type, 'value'):
                                action_idx = action.card_type.value
                            else:
                                action_idx = 0
                        else:
                            # 尝试将动作转换为整数
                            try:
                                action_idx = int(action)
                            except (TypeError, ValueError):
                                # 如果无法转换，使用默认值
                                action_idx = 0
                        action_mask[action_idx] = True
            else:
                action_mask = None

            # 使用MCTS进行预测
            visit_counts, action_probs = self.mcts.run(
                observation,
                self.model,
                temperature=1.0,
                actions_mask=action_mask
            )

            # 转换为列表格式
            action_list = [0.0] * self.action_shape[0]
            for action, prob in action_probs.items():
                action_list[action] = prob

            # 获取价值估计
            hidden_state = self.model.represent(observation)
            _, value = self.model.predict(hidden_state)

            return action_list, value.item()
        finally:
            # 恢复原始训练状态
            self.model.representation_network.train(representation_training)
            self.model.dynamics_network.train(dynamics_training)
            self.model.prediction_network.train(prediction_training)

    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用经验数据更新模型

        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次

        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        # 存储经验
        if isinstance(experience, Experience):
            self.replay_buffer.add(experience)
            return {}  # 单个经验不会触发立即更新

        # 如果是批量经验，存储所有经验
        if isinstance(experience, Batch):
            for exp in experience:
                self.replay_buffer.add(exp)

        # 从经验回放缓冲区中采样
        if len(self.replay_buffer) < self.batch_size:
            return {}  # 缓冲区样本不足，不更新

        # 处理不同类型的回放缓冲区
        sample_result = self.replay_buffer.sample(self.batch_size)
        if isinstance(sample_result, tuple) and len(sample_result) == 3:
            # PrioritizedReplayBuffer 返回 (batch, indices, weights)
            batch, indices, weights = sample_result
        else:
            # 普通 ReplayBuffer 只返回 batch
            batch = sample_result
            indices = None
            weights = None

        # 准备训练数据
        states = []
        actions = []
        rewards = []
        next_states = []
        dones = []

        for exp in batch:
            states.append(exp.state.get_observation() if isinstance(exp.state, State) else exp.state)
            actions.append(exp.action)
            rewards.append(exp.reward)
            next_states.append(exp.next_state.get_observation() if isinstance(exp.next_state, State) else exp.next_state)
            dones.append(exp.done)

        # 将数据转换为PyTorch张量
        states = torch.FloatTensor(np.array(states)).to(self.device)

        # 处理动作
        action_indices = []
        for action in actions:
            if isinstance(action, int):
                action_indices.append(action)
            else:
                # 处理非整数类型的动作
                if hasattr(action, 'value'):  # 处理枚举类型，如BidAction
                    action_idx = action.value
                elif hasattr(action, 'to_index'):  # 处理具有to_index方法的对象
                    action_idx = action.to_index()
                elif hasattr(action, 'card_type'):  # 处理CardGroup类型
                    # 将CardGroup转换为整数索引
                    if hasattr(action.card_type, 'value'):
                        action_idx = action.card_type.value
                    else:
                        action_idx = 0
                else:
                    # 尝试将动作转换为整数
                    try:
                        action_idx = int(action)
                    except (TypeError, ValueError):
                        # 如果无法转换，使用默认值
                        action_idx = 0
                action_indices.append(action_idx)

        actions = torch.LongTensor(np.array(action_indices)).to(self.device)
        rewards = torch.FloatTensor(np.array(rewards)).to(self.device)
        next_states = torch.FloatTensor(np.array(next_states)).to(self.device)
        dones = torch.FloatTensor(np.array(dones)).to(self.device)

        # 处理权重（如果存在）
        if weights is not None:
            weights = torch.FloatTensor(weights).to(self.device)
        else:
            # 如果没有权重，创建均匀权重
            weights = torch.ones(len(batch)).to(self.device)

        # 调整输入形状
        if len(states.shape) > 2:
            states = states.reshape(states.shape[0], -1)
        if len(next_states.shape) > 2:
            next_states = next_states.reshape(next_states.shape[0], -1)

        # 训练MuZero模型
        self.model.representation_network.train()
        self.model.dynamics_network.train()
        self.model.prediction_network.train()

        # 获取初始隐藏状态
        hidden_states = self.model.representation_network(states)

        # 预测策略和价值
        policy_logits, values = self.model.prediction_network(hidden_states)

        # 计算目标值 (n步回报)
        target_values = self._compute_target_values(rewards, dones, next_states)

        # 计算策略目标 (MCTS访问计数)
        target_policies = self._compute_target_policies(states)

        # 初始化损失
        value_loss = 0.0
        policy_loss = 0.0
        reward_loss = 0.0
        consistency_loss = 0.0

        # 初始化损失缩放因子
        gradient_scale = 1.0 / self.num_unroll_steps

        # 展开训练 (通过动态模型预测未来状态和奖励)
        current_states = hidden_states
        for step in range(self.num_unroll_steps):
            # 如果是第一步，使用真实动作；否则使用目标策略中概率最高的动作
            if step == 0:
                # 使用真实轨迹中的动作
                current_actions = actions
            else:
                # 使用目标策略中概率最高的动作
                current_actions = torch.argmax(target_policies[:, step - 1], dim=1)

            # 使用动态网络预测下一个状态和奖励
            next_states_pred, rewards_pred = self.model.dynamics_network(current_states, current_actions)

            # 使用预测网络预测策略和价值
            policy_logits_pred, values_pred = self.model.prediction_network(next_states_pred)

            # 计算奖励损失 (MSE)
            if step == 0:
                # 第一步使用真实奖励
                reward_loss += F.mse_loss(rewards_pred, rewards, reduction='none').mean() * gradient_scale

            # 计算策略损失 (交叉熵)
            policy_loss += F.cross_entropy(
                policy_logits_pred,
                torch.argmax(target_policies[:, step], dim=1),
                reduction='none'
            ).mean() * gradient_scale

            # 计算价值损失 (MSE)
            value_loss += F.mse_loss(values_pred, target_values[:, step], reduction='none').mean() * gradient_scale

            # 计算一致性损失 (EfficientZero改进)
            if self.consistency_loss_weight > 0:
                with torch.no_grad():
                    target_next_states, _ = self.target_model.dynamics_network(current_states, current_actions)

                consistency_loss += F.mse_loss(
                    next_states_pred,
                    target_next_states,
                    reduction='none'
                ).mean() * gradient_scale

            # 更新当前状态为预测的下一个状态
            current_states = next_states_pred

        # 计算总损失
        total_loss = (
            self.value_loss_weight * value_loss +
            self.policy_loss_weight * policy_loss +
            reward_loss +
            self.consistency_loss_weight * consistency_loss
        )

        # 梯度更新
        self.optimizer.zero_grad()
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.model.representation_network.parameters(), 10)
        torch.nn.utils.clip_grad_norm_(self.model.dynamics_network.parameters(), 10)
        torch.nn.utils.clip_grad_norm_(self.model.prediction_network.parameters(), 10)
        self.optimizer.step()

        # 更新目标网络
        self._update_target_network()

        # 更新学习率
        if self.scheduler is not None:
            self.scheduler.step()

        # 更新优先级（仅对优先级回放缓冲区）
        if indices is not None and hasattr(self.replay_buffer, 'update_priorities'):
            td_errors = torch.abs(values - target_values[:, 0]).detach().cpu().numpy()
            self.replay_buffer.update_priorities(indices, td_errors)

        # 更新训练步数
        self.train_steps += 1

        # 返回损失指标
        return {
            'value_loss': value_loss.item(),
            'policy_loss': policy_loss.item(),
            'reward_loss': reward_loss.item(),
            'consistency_loss': consistency_loss.item(),
            'total_loss': total_loss.item()
        }

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path (str): 保存路径
        """
        # 创建目录
        os.makedirs(path, exist_ok=True)

        # 保存模型参数
        torch.save(self.model.representation_network.state_dict(), os.path.join(path, 'representation_network.pt'))
        torch.save(self.model.dynamics_network.state_dict(), os.path.join(path, 'dynamics_network.pt'))
        torch.save(self.model.prediction_network.state_dict(), os.path.join(path, 'prediction_network.pt'))

        # 保存优化器和训练状态
        scheduler_state = None
        if self.scheduler is not None:
            if hasattr(self.scheduler, 'state_dict'):
                scheduler_state = self.scheduler.state_dict()
            else:
                # 如果调度器没有state_dict方法，保存基本属性
                scheduler_state = {
                    'step_size': getattr(self.scheduler, 'step_size', None),
                    'gamma': getattr(self.scheduler, 'gamma', None),
                    'last_epoch': getattr(self.scheduler, 'last_epoch', -1)
                }

        torch.save({
            'optimizer': self.optimizer.state_dict(),
            'train_steps': self.train_steps,
            'scheduler': scheduler_state
        }, os.path.join(path, 'optimizer.pt'))

        self.logger.info(f"模型已保存到 {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path (str): 加载路径
        """
        # 加载模型参数
        self.model.representation_network.load_state_dict(
            torch.load(os.path.join(path, 'representation_network.pt'), map_location=self.device)
        )
        self.model.dynamics_network.load_state_dict(
            torch.load(os.path.join(path, 'dynamics_network.pt'), map_location=self.device)
        )
        self.model.prediction_network.load_state_dict(
            torch.load(os.path.join(path, 'prediction_network.pt'), map_location=self.device)
        )

        # 加载优化器和训练状态
        checkpoint = torch.load(os.path.join(path, 'optimizer.pt'), map_location=self.device)
        self.optimizer.load_state_dict(checkpoint['optimizer'])
        self.train_steps = checkpoint['train_steps']

        if checkpoint['scheduler'] is not None and self.scheduler is not None:
            if hasattr(self.scheduler, 'load_state_dict'):
                self.scheduler.load_state_dict(checkpoint['scheduler'])
            else:
                # 如果调度器没有load_state_dict方法，手动设置属性
                scheduler_state = checkpoint['scheduler']
                if hasattr(self.scheduler, 'step_size') and 'step_size' in scheduler_state:
                    self.scheduler.step_size = scheduler_state['step_size']
                if hasattr(self.scheduler, 'gamma') and 'gamma' in scheduler_state:
                    self.scheduler.gamma = scheduler_state['gamma']
                if hasattr(self.scheduler, 'last_epoch') and 'last_epoch' in scheduler_state:
                    self.scheduler.last_epoch = scheduler_state['last_epoch']

        # 更新目标网络
        self._update_target_network(tau=1.0)

        self.logger.info(f"模型已从 {path} 加载")

    @property
    def name(self) -> str:
        """
        获取算法名称

        Returns:
            str: 算法名称
        """
        return 'MuZero'

    def _compute_target_values(self, rewards: torch.Tensor, dones: torch.Tensor, next_states: torch.Tensor) -> torch.Tensor:
        """
        计算n步目标值

        Args:
            rewards (torch.Tensor): 奖励张量
            dones (torch.Tensor): 终止标志张量
            next_states (torch.Tensor): 下一个状态张量

        Returns:
            torch.Tensor: 目标值张量
        """
        batch_size = rewards.shape[0]

        # 获取初始隐藏状态
        with torch.no_grad():
            initial_states = self.target_model.represent(next_states)
            _, bootstrap_values = self.target_model.predict(initial_states)

        # 初始化目标值张量
        target_values = torch.zeros(batch_size, self.num_unroll_steps).to(self.device)

        # 对于最后一步，使用引导值
        # 将布尔张量转换为浮点张量以避免PyTorch版本兼容性问题
        dones_float = dones.float()
        target_values[:, -1] = bootstrap_values * (1 - dones_float)

        # 对于前面的步骤，使用n步回报
        for idx in range(self.num_unroll_steps - 2, -1, -1):
            target_values[:, idx] = rewards + self.gamma * target_values[:, idx+1] * (1 - dones_float)

        return target_values

    def _compute_target_policies(self, states: torch.Tensor) -> torch.Tensor:
        """
        计算目标策略

        Args:
            states (torch.Tensor): 状态张量

        Returns:
            torch.Tensor: 目标策略张量
        """
        batch_size = states.shape[0]

        # 初始化目标策略张量
        action_dim = self.action_shape[0]  # 使用action_shape而不是action_dim
        target_policies = torch.zeros(batch_size, self.num_unroll_steps, action_dim).to(self.device)

        # 使用MCTS为每个状态计算策略
        for i in range(batch_size):
            state = states[i].cpu().numpy()
            # 在测试中，我们使用直接预测而不是MCTS，以减少测试时间
            # _, action_probs = self.mcts.run(state, self.model, temperature=1.0)

            # 直接使用预测网络
            hidden_state = self.model.represent(torch.FloatTensor(state).unsqueeze(0).to(self.device))
            policy_logits, _ = self.model.predict(hidden_state)
            policy_probs = F.softmax(policy_logits.squeeze(0), dim=0).detach().cpu().numpy()

            # 创建与原始MCTS返回格式相同的字典
            action_probs = {}
            for action in range(len(policy_probs)):
                action_probs[action] = float(policy_probs[action])

            # 转换为张量
            policy = torch.zeros(self.action_shape[0]).to(self.device)
            for action, prob in action_probs.items():
                policy[action] = prob

            target_policies[i, 0] = policy

            # 对于后续步骤，使用模型预测
            if self.num_unroll_steps > 1:
                hidden_state = self.model.represent(torch.FloatTensor(state).unsqueeze(0).to(self.device))

                for step in range(1, self.num_unroll_steps):
                    # 使用上一步策略中概率最高的动作
                    action = torch.argmax(target_policies[i, step-1]).unsqueeze(0)

                    # 预测下一个状态
                    next_hidden_state, _ = self.model.dynamics(hidden_state, action)

                    # 预测策略
                    policy_logits, _ = self.model.predict(next_hidden_state)
                    policy = F.softmax(policy_logits.squeeze(0), dim=0)

                    # 更新目标策略
                    target_policies[i, step] = policy

                    # 更新隐藏状态
                    hidden_state = next_hidden_state

        return target_policies

    def _update_target_network(self, tau: float = 0.1) -> None:
        """
        更新目标网络

        Args:
            tau (float, optional): 软更新参数. Defaults to 0.1.
        """
        # 更新表示网络
        for target_param, param in zip(
            self.target_model.representation_network.parameters(),
            self.model.representation_network.parameters()
        ):
            target_param.data.copy_(tau * param.data + (1 - tau) * target_param.data)

        # 更新动态网络
        for target_param, param in zip(
            self.target_model.dynamics_network.parameters(),
            self.model.dynamics_network.parameters()
        ):
            target_param.data.copy_(tau * param.data + (1 - tau) * target_param.data)

        # 更新预测网络
        for target_param, param in zip(
            self.target_model.prediction_network.parameters(),
            self.model.prediction_network.parameters()
        ):
            target_param.data.copy_(tau * param.data + (1 - tau) * target_param.data)

    def _process_batch(self, batch: List[Experience]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        处理经验批次

        Args:
            batch (List[Experience]): 经验批次

        Returns:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
            状态、动作、奖励、下一状态和终止标志张量
        """
        states = []
        actions = []
        rewards = []
        next_states = []
        dones = []

        for exp in batch:
            states.append(exp.state.get_observation() if isinstance(exp.state, State) else exp.state)
            actions.append(exp.action)
            rewards.append(exp.reward)
            next_states.append(exp.next_state.get_observation() if isinstance(exp.next_state, State) else exp.next_state)
            dones.append(exp.done)

        # 转换为numpy数组
        states = np.array(states)
        actions = np.array(actions)
        rewards = np.array(rewards)
        next_states = np.array(next_states)
        dones = np.array(dones)

        # 转换为PyTorch张量
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.FloatTensor(dones).to(self.device)

        # 重塑状态和下一状态
        if len(states.shape) > 2:
            states = states.reshape(states.shape[0], -1)
        if len(next_states.shape) > 2:
            next_states = next_states.reshape(next_states.shape[0], -1)

        return states, actions, rewards, next_states, dones

    def direct_predict(self, state: Union[State, np.ndarray]) -> Tuple[List[float], float]:
        """
        直接预测状态的动作概率分布和价值，不使用MCTS

        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察

        Returns:
            Tuple[List[float], float]: 动作概率分布、状态价值
        """
        # 保存当前训练状态
        representation_training = self.model.representation_network.training
        dynamics_training = self.model.dynamics_network.training
        prediction_training = self.model.prediction_network.training

        # 切换到评估模式
        self.model.representation_network.eval()
        self.model.dynamics_network.eval()
        self.model.prediction_network.eval()

        try:
            # 获取观察
            if isinstance(state, State):
                observation = state.get_observation()
            else:
                observation = state

            # 转换为tensor
            if len(observation.shape) == 3:  # 如果是单个图像，添加批次维度
                observation = np.expand_dims(observation, axis=0)

            # 获取隐藏状态
            hidden_state = self.model.represent(observation)

            # 使用预测网络获取动作概率和价值
            policy_logits, value = self.model.predict(hidden_state)

            # 将策略从logits转换为概率
            policy = torch.softmax(policy_logits, dim=1).detach().cpu().numpy()[0]

            return policy.tolist(), value.item()
        finally:
            # 恢复原始训练状态
            self.model.representation_network.train(representation_training)
            self.model.dynamics_network.train(dynamics_training)
            self.model.prediction_network.train(prediction_training)