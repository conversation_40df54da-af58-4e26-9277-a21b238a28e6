#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
运行 EfficientZero 训练脚本

该脚本用于启动 EfficientZero 算法训练斗地主模型，自动使用预配置的参数。
简化了训练启动流程，包含了参数验证、路径检查和训练进度跟踪功能。

使用方法:
    从任意位置运行:
    $ python path/to/run_efficient_zero_training.py

    自定义配置:
    $ python run_efficient_zero_training.py --config ../../configs/doudizhu/my_config.yaml

    指定 GPU 设备:
    $ python run_efficient_zero_training.py --device cuda:0

    多 GPU 训练:
    $ python run_efficient_zero_training.py --device cuda:0,cuda:1

    跳过依赖检查:
    $ python run_efficient_zero_training.py --skip-dependency-check

    跳过环境检查:
    $ python run_efficient_zero_training.py --skip-environment-check

    仅警告环境问题不阻止执行:
    $ python run_efficient_zero_training.py --environment-warn-only

功能特性:
    - 自动检查必要的依赖项是否已安装
    - 自动检查训练环境是否满足要求（如GPU显存、系统内存等）
    - 自动检查和切换到正确的工作目录
    - 自动检查训练脚本和配置文件路径的有效性
    - 记录详细的训练日志（控制台和文件双输出）
    - 计算并显示训练总时长
    - 根据训练结果提供相应的退出码
"""

import os
import sys
import argparse
import logging
import subprocess
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Tuple, Set, Callable, Deque
from collections import deque
import re
import time
import random

# 常量定义
DEFAULT_GAME = 'doudizhu'  # 默认游戏类型
DEFAULT_ALGO = 'efficient_zero'    # 默认算法
DEFAULT_CONFIG_PATH = '../../configs/doudizhu/efficient_zero_config.yaml'  # 默认配置文件路径
TRAIN_MAIN_SCRIPT = 'optimized_training_integrated.py'  # 训练主脚本文件名

# 数据加载相关常量
DEFAULT_NUM_WORKERS = 4  # 默认DataLoader的工作进程数

# 日志相关常量
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'  # 日志格式
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'  # 日志日期格式
LOG_LEVEL = logging.INFO  # 日志级别
LOG_SEPARATOR = '=' * 50  # 日志分隔符

# 进度监控相关常量
PROGRESS_UPDATE_INTERVAL = 5  # 进度信息更新间隔（秒）
SPEED_WINDOW_SIZE = 10        # 速度计算的窗口大小
PROGRESS_BAR_WIDTH = 50       # 进度条宽度

# 设备相关常量
VALID_DEVICE_PREFIXES = ['cpu', 'cuda', 'cuda:']  # 有效的设备前缀
MAX_CUDA_DEVICES = 8  # 支持的最大CUDA设备数量（一般不会超过8个GPU）

# 环境要求相关常量
DEFAULT_MIN_GPU_MEMORY = 4  # 默认最低要求的GPU显存，单位为GB
DEFAULT_MIN_SYSTEM_MEMORY = 8  # 默认最低要求的系统内存，单位为GB
DEFAULT_MIN_CPU_CORES = 4  # 默认最低要求的CPU核心数

# 将项目根目录添加到路径中，确保可以导入依赖检查模块
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '../..'))
sys.path.insert(0, project_root)

# 尝试导入验证模块
try:
    from cardgame_ai.utils.arg_validation import validate_device
    HAS_VALIDATION = True
except ImportError:
    HAS_VALIDATION = False
    logger = logging.getLogger(__name__)
    logger.warning("无法导入参数验证模块，将使用内置验证函数")

# 尝试导入依赖检查模块
try:
    from check_dependencies import check_dependencies, check_torch_cuda
    HAS_DEPENDENCY_CHECK = True
except ImportError:
    HAS_DEPENDENCY_CHECK = False
    # 如果无法导入依赖检查模块，定义简化的检查函数
    def check_dependencies(required_only: bool = True, force_continue: bool = False) -> bool:
        return True

    def check_torch_cuda(force_continue: bool = False) -> bool:
        try:
            import torch
            return torch.cuda.is_available()
        except:
            return False

# 尝试导入环境检查模块
try:
    from check_environment import check_environment
    HAS_ENVIRONMENT_CHECK = True
except ImportError:
    HAS_ENVIRONMENT_CHECK = False
    logger = logging.getLogger(__name__)
    logger.warning("无法导入环境检查模块，将跳过环境检查")

    # 如果无法导入环境检查模块，定义一个简化的检查函数
    def check_environment(
        min_gpu_memory: float = DEFAULT_MIN_GPU_MEMORY,
        min_system_memory: float = DEFAULT_MIN_SYSTEM_MEMORY,
        min_cpu_cores: int = DEFAULT_MIN_CPU_CORES,
        require_gpu: bool = True,
        warn_only: bool = False,
        force_continue: bool = False
    ) -> Tuple[bool, str]:
        """简化的环境检查函数"""
        return True, "环境检查模块不可用，跳过环境检查"


def ensure_correct_directory() -> bool:
    """
    确保脚本在正确的目录中运行。

    检查当前工作目录是否包含 train_main.py 文件，
    如果不包含则尝试切换到脚本所在目录。

    Returns:
        bool: 是否成功切换到包含 train_main.py 的目录
    """
    # 获取脚本所在的绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # 检查当前工作目录是否已经包含 train_main.py
    if os.path.exists(TRAIN_MAIN_SCRIPT):
        return True

    # 检查脚本所在目录是否包含 train_main.py
    if os.path.exists(os.path.join(script_dir, TRAIN_MAIN_SCRIPT)):
        # 切换到脚本所在目录
        os.chdir(script_dir)
        print(f"已自动切换工作目录到: {script_dir}")
        return True

    # 尝试在特定相对路径查找
    possible_paths: List[str] = [
        os.path.join('cardgame_ai', '主程序'),
        os.path.join('..', 'cardgame_ai', '主程序'),
        os.path.join('..', '..', 'cardgame_ai', '主程序')
    ]

    for path in possible_paths:
        full_path = os.path.abspath(path)
        if os.path.exists(os.path.join(full_path, TRAIN_MAIN_SCRIPT)):
            os.chdir(full_path)
            print(f"已自动切换工作目录到: {full_path}")
            return True

    # 无法找到正确的目录
    return False


def validate_args(args: argparse.Namespace) -> Tuple[bool, str]:
    """
    验证命令行参数的有效性，特别是device参数。

    Args:
        args: 解析后的命令行参数

    Returns:
        Tuple[bool, str]: (验证是否通过, 错误或警告信息)
    """
    # 如果可用，使用验证模块中的函数
    if HAS_VALIDATION:
        return validate_device(args.device)

    # 内置验证逻辑（如果验证模块不可用）
    # 如果没有指定device参数，不需要验证
    if not args.device:
        return True, ""

    # 验证device格式
    device = args.device.lower()

    # 多GPU配置验证（用逗号分隔的设备列表）
    if ',' in device:
        devices = [d.strip() for d in device.split(',')]

        # 检查每个设备
        for dev in devices:
            # 验证每个设备的有效性
            if not any(dev.startswith(prefix) for prefix in VALID_DEVICE_PREFIXES):
                return False, f"无效的设备格式: '{dev}'。有效格式: 'cpu', 'cuda', 'cuda:0', 'cuda:1', ..."

            # 验证GPU索引
            if dev.startswith('cuda:'):
                try:
                    index = int(dev.split(':')[1])
                    if index < 0 or index >= MAX_CUDA_DEVICES:
                        return False, f"无效的CUDA设备索引: {index}。索引应在0到{MAX_CUDA_DEVICES-1}之间。"
                except ValueError:
                    return False, f"无效的CUDA设备索引格式: '{dev}'。有效格式如: 'cuda:0'"

        # 检查CUDA支持和设备数量
        try:
            import torch
            if not torch.cuda.is_available():
                return False, "指定了CUDA设备，但CUDA不可用。请检查CUDA安装或使用'cpu'作为设备。"

            available_devices = torch.cuda.device_count()
            for dev in devices:
                if dev.startswith('cuda:'):
                    index = int(dev.split(':')[1])
                    if index >= available_devices:
                        return False, f"指定的CUDA设备索引 {index} 超出了可用设备数量 {available_devices}。"
        except ImportError:
            return False, "PyTorch导入失败，无法验证CUDA设备。请检查PyTorch安装。"
        except Exception as e:
            return False, f"验证CUDA设备时出错: {str(e)}"

        return True, ""

    # 单一设备验证
    if not any(device.startswith(prefix) for prefix in VALID_DEVICE_PREFIXES):
        return False, f"无效的设备格式: '{device}'。有效格式: 'cpu', 'cuda', 'cuda:0', 'cuda:1', ..."

    # 如果是CPU，无需进一步验证
    if device == 'cpu':
        return True, ""

    # 验证CUDA设备
    try:
        import torch
        if not torch.cuda.is_available():
            return False, "指定了CUDA设备，但CUDA不可用。请检查CUDA安装或使用'cpu'作为设备。"

        # 如果只是指定了'cuda'，意味着使用默认设备（通常是cuda:0）
        if device == 'cuda':
            return True, ""

        # 验证特定的CUDA设备索引
        if device.startswith('cuda:'):
            try:
                index = int(device.split(':')[1])
                available_devices = torch.cuda.device_count()

                if index < 0 or index >= MAX_CUDA_DEVICES:
                    return False, f"无效的CUDA设备索引: {index}。索引应在0到{MAX_CUDA_DEVICES-1}之间。"

                if index >= available_devices:
                    return False, f"指定的CUDA设备索引 {index} 超出了可用设备数量 {available_devices}。"
            except ValueError:
                return False, f"无效的CUDA设备索引格式: '{device}'。有效格式如: 'cuda:0'"
    except ImportError:
        return False, "PyTorch导入失败，无法验证CUDA设备。请检查PyTorch安装。"
    except Exception as e:
        return False, f"验证CUDA设备时出错: {str(e)}"

    return True, ""


def calculate_training_speed(
    progress_history: Deque[Tuple[float, int]],
    current_time: float,
    current_progress: int
) -> Tuple[float, float, str]:
    """
    计算训练速度和预计剩余时间

    Args:
        progress_history: 进度历史记录队列，每项包含(时间戳, 进度值)
        current_time: 当前时间戳
        current_progress: 当前进度百分比

    Returns:
        Tuple[float, float, str]: (每秒进度变化率, 预计剩余秒数, 状态描述)
    """
    # 如果历史记录为空或进度为0，无法计算
    if not progress_history or current_progress <= 0:
        return 0.0, 0.0, "初始化中..."

    # 计算速度 (进度变化/时间变化)
    oldest_time, oldest_progress = progress_history[0]
    time_diff = current_time - oldest_time
    progress_diff = current_progress - oldest_progress

    # 避免除以零
    if time_diff < 0.001 or progress_diff <= 0:
        return 0.0, 0.0, "计算中..."

    # 计算速度 (单位：进度百分比/秒)
    speed = progress_diff / time_diff

    # 估算剩余时间
    remaining_progress = 100 - current_progress
    remaining_time = remaining_progress / speed if speed > 0 else float('inf')

    # 确定状态描述
    if speed < 0.001:
        status = "速度极慢"
    elif speed < 0.01:
        status = "速度较慢"
    elif speed < 0.05:
        status = "速度正常"
    elif speed < 0.1:
        status = "速度较快"
    else:
        status = "速度极快"

    return speed, remaining_time, status


def analyze_metric_trend(history: Deque[float]) -> Tuple[str, float]:
    """
    分析性能指标趋势

    Args:
        history: 指标历史值队列

    Returns:
        Tuple[str, float]: (趋势描述, 变化率)
    """
    if len(history) < 2:
        return "持平", 0.0

    # 计算趋势
    first_value = history[0]
    last_value = history[-1]

    if abs(first_value) < 1e-6:  # 避免除以零
        if abs(last_value) < 1e-6:
            return "持平", 0.0
        return "上升" if last_value > 0 else "下降", 1.0

    change_rate = (last_value - first_value) / abs(first_value)

    # 确定趋势描述
    if abs(change_rate) < 0.01:
        trend = "持平"
    elif change_rate > 0:
        if change_rate > 0.1:
            trend = "显著上升"
        else:
            trend = "上升"
    else:
        if change_rate < -0.1:
            trend = "显著下降"
        else:
            trend = "下降"

    return trend, change_rate


def main() -> int:
    """主函数，处理命令行参数并启动训练过程。

    解析命令行参数，包括游戏类型、算法、配置文件路径和设备选项，
    验证训练环境，并将参数传递给训练主程序。监控训练执行过程，
    并提供训练结果摘要。

    Returns:
        int: 返回状态码，0 表示成功，非零表示出错

    Raises:
        FileNotFoundError: 如果训练主程序或配置文件不存在
        Exception: 执行训练命令时出现的其他异常
    """
    # 导入全局模块
    global sys, os, subprocess, re, time, random, datetime, timedelta, deque

    # 创建日志目录，确保日志存储位置存在
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../logs')
    os.makedirs(log_dir, exist_ok=True)

    # 生成包含时间戳的日志文件名，确保每次训练的日志不会覆盖
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f'efficient_zero_training_{timestamp}.log')

    # 配置日志系统，同时输出到控制台和文件，方便实时监控和后续分析
    handlers = [
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
    logging.basicConfig(
        level=LOG_LEVEL,
        format=LOG_FORMAT,
        datefmt=LOG_DATE_FORMAT,
        handlers=handlers
    )

    # 记录脚本启动信息，使用分隔符增强日志可读性
    logging.info(LOG_SEPARATOR)
    logging.info(f"EfficientZero 训练启动 - {timestamp}")
    logging.info(LOG_SEPARATOR)
    logging.info(f"当前工作目录: {os.getcwd()}")

    # 创建命令行参数解析器，提供友好的命令行接口
    parser = argparse.ArgumentParser(description='启动 EfficientZero 斗地主训练')

    # 添加固定参数，设置合理的默认值减少用户输入
    parser.add_argument('--game', type=str, default=DEFAULT_GAME,
                        help=f'要训练的游戏类型（默认: {DEFAULT_GAME}）')
    parser.add_argument('--algo', type=str, default=DEFAULT_ALGO,
                        help=f'要使用的算法（默认: {DEFAULT_ALGO}, 实际实现为 EfficientZero）')
    parser.add_argument('--config', type=str,
                        default=DEFAULT_CONFIG_PATH,
                        help=f'配置文件路径（默认: {DEFAULT_CONFIG_PATH}）')

    # 添加可选的设备参数，支持指定 CPU 或特定 GPU
    parser.add_argument('--device', type=str, default=None,
                        help='训练使用的设备，例如 "cuda:0" 或 "cpu"（默认: None，使用配置文件中指定的设备）')

    # 添加依赖和环境检查相关参数
    parser.add_argument('--skip-dependency-check', action='store_true',
                        help='跳过依赖检查过程')
    parser.add_argument('--skip-environment-check', action='store_true',
                        help='跳过环境检查过程')
    parser.add_argument('--environment-warn-only', action='store_true',
                        help='环境检查失败时只警告不阻止执行')

    # 添加环境要求相关参数
    parser.add_argument('--min-gpu-memory', type=float, default=DEFAULT_MIN_GPU_MEMORY,
                       help=f'最低要求的GPU显存，单位为GB（默认: {DEFAULT_MIN_GPU_MEMORY}）')
    parser.add_argument('--min-system-memory', type=float, default=DEFAULT_MIN_SYSTEM_MEMORY,
                       help=f'最低要求的系统内存，单位为GB（默认: {DEFAULT_MIN_SYSTEM_MEMORY}）')
    parser.add_argument('--min-cpu-cores', type=int, default=DEFAULT_MIN_CPU_CORES,
                       help=f'最低要求的CPU核心数（默认: {DEFAULT_MIN_CPU_CORES}）')

    # 添加执行控制参数
    parser.add_argument('--force-continue', action='store_true',
                        help='强制继续执行，即使某些检查失败')
    # 添加断点续训选项
    parser.add_argument('--resume', action='store_true',
                        help='是否从最新checkpoint恢复训练')
    parser.add_argument('--checkpoint-dir', type=str, default=None,
                        help='checkpoint保存目录，优先于配置文件中的dir设置')
    # 添加数据加载参数
    parser.add_argument('--num-workers', type=int, default=DEFAULT_NUM_WORKERS,
                        help=f'DataLoader的工作进程数（默认: {DEFAULT_NUM_WORKERS}）')

    # 解析命令行参数，获取用户输入
    args = parser.parse_args()

    # 记录解析后的参数，方便调试和问题追踪
    logging.info("训练参数:")
    logging.info(f"  - 游戏类型: {args.game}")
    logging.info(f"  - 算法: {args.algo}")
    logging.info(f"  - 配置文件: {args.config}")
    logging.info(f"  - 设备: {args.device if args.device else '使用配置文件默认值'}")
    logging.info(f"  - 跳过依赖检查: {args.skip_dependency_check}")
    logging.info(f"  - 跳过环境检查: {args.skip_environment_check}")
    logging.info(f"  - 环境检查仅警告: {args.environment_warn_only}")
    logging.info(f"  - 最低GPU显存要求: {args.min_gpu_memory}GB")
    logging.info(f"  - 最低系统内存要求: {args.min_system_memory}GB")
    logging.info(f"  - 最低CPU核心数要求: {args.min_cpu_cores}")
    logging.info(f"  - 强制继续执行: {args.force_continue}")

    # 变量初始化
    check_results = []  # 用于跟踪各项检查的结果
    force_continue = args.force_continue  # 强制继续标志
    should_use_cpu = False  # 是否应该使用CPU而非GPU

    # 集成流程：1. 先确保在正确的目录中运行
    logging.info(LOG_SEPARATOR)
    logging.info("1. 检查工作目录...")

    if ensure_correct_directory():
        logging.info("✓ 工作目录检查通过")
        check_results.append(True)
    else:
        error_msg = "无法找到训练主程序目录，请确保脚本路径正确"
        logging.error(f"✗ 工作目录检查失败: {error_msg}")
        check_results.append(False)
        if not force_continue:
            return 1

    # 集成流程：2. 检查训练主程序和配置文件路径的有效性
    logging.info(LOG_SEPARATOR)
    logging.info("2. 检查文件路径...")

    files_valid = True

    # 检查训练主程序路径
    if not os.path.exists(TRAIN_MAIN_SCRIPT):
        files_valid = False
        error_msg = f"训练主程序文件不存在: {TRAIN_MAIN_SCRIPT}"
        logging.error(f"✗ {error_msg}")
        check_results.append(False)
        if not force_continue:
            return 1

    # 检查配置文件路径
    if not os.path.exists(args.config):
        files_valid = False
        error_msg = f"配置文件不存在: {args.config}"
        logging.error(f"✗ {error_msg}")
        check_results.append(False)
        if not force_continue:
            return 1

    if files_valid:
        logging.info("✓ 文件路径检查通过")
        check_results.append(True)

    # 集成流程：3. 执行依赖检查
    logging.info(LOG_SEPARATOR)
    logging.info("3. 检查必要的依赖项...")

    if args.skip_dependency_check:
        logging.info("依赖检查已跳过（由用户指定）")
        check_results.append(True)  # 跳过检查视为通过
    elif not HAS_DEPENDENCY_CHECK:
        logging.warning("依赖检查模块不可用，将跳过依赖检查")
        check_results.append(True)  # 模块不可用视为通过
    else:
        # 执行依赖检查，仅检查必要依赖
        if check_dependencies(required_only=True, force_continue=force_continue):
            logging.info("✓ 必要依赖检查通过")
            check_results.append(True)

            # 检查CUDA支持（如果请求使用GPU）
            if args.device and 'cuda' in args.device:
                logging.info("检查CUDA支持...")
                if not check_torch_cuda(force_continue=force_continue):
                    logging.warning("⚠ 警告: 请求使用CUDA，但CUDA不可用！")
                    logging.warning("训练将自动切换到CPU，但这可能会非常慢。")
                    logging.warning("建议安装与您的GPU兼容的CUDA版本，或显式指定 --device cpu")
                    # 强制使用CPU
                    should_use_cpu = True
                    args.device = 'cpu'
                else:
                    logging.info("✓ CUDA支持检查通过")
        else:
            error_msg = "缺少必要的依赖"
            logging.error(f"✗ 依赖检查失败: {error_msg}")
            logging.error("执行 'python check_dependencies.py' 查看详细信息和安装建议。")
            check_results.append(False)
            if not force_continue:
                return 1

    # 集成流程：4. 验证命令行参数
    logging.info(LOG_SEPARATOR)
    logging.info("4. 验证命令行参数...")

    # 处理 CUDA 不可用但请求使用 CUDA 的情况
    if should_use_cpu and args.device and 'cuda' in args.device:
        args.device = 'cpu'
        logging.warning("⚠ 由于 CUDA 不可用，已将设备自动切换为 CPU")

    is_valid, error_msg = validate_args(args)
    if not is_valid:
        logging.error(f"✗ 参数验证失败: {error_msg}")
        check_results.append(False)
        if not force_continue:
            return 1
    else:
        logging.info("✓ 参数验证通过")
        check_results.append(True)

    # 集成流程：5. 执行环境检查
    logging.info(LOG_SEPARATOR)
    logging.info("5. 检查训练环境...")

    if args.skip_environment_check:
        logging.info("环境检查已跳过（由用户指定）")
        check_results.append(True)  # 跳过检查视为通过
    elif not HAS_ENVIRONMENT_CHECK:
        logging.warning("环境检查模块不可用，将跳过环境检查")
        check_results.append(True)  # 模块不可用视为通过
    else:
        # 确定是否需要 GPU
        require_gpu = not should_use_cpu and (args.device is None or (args.device and 'cuda' in args.device))

        # 执行环境检查
        try:
            is_ok, message = check_environment(
                min_gpu_memory=args.min_gpu_memory,
                min_system_memory=args.min_system_memory,
                min_cpu_cores=args.min_cpu_cores,
                require_gpu=require_gpu,
                warn_only=args.environment_warn_only,
                force_continue=force_continue
            )

            if not is_ok:
                logging.error(f"✗ 环境检查失败: {message}")
                if args.environment_warn_only or force_continue:
                    logging.warning("⚠ 环境检查失败，但将继续执行（警告模式或强制继续）")
                    check_results.append(True)  # 警告模式下视为通过
                else:
                    logging.error("请确保您的系统满足训练环境要求，或使用 --environment-warn-only 参数忽略此错误。")
                    check_results.append(False)
                    return 1
            else:
                logging.info(f"✓ 环境检查通过: {message}")
                check_results.append(True)
        except Exception as e:
            logging.error(f"✗ 环境检查过程中发生错误: {e}")
            if force_continue:
                logging.warning("⚠ 将继续执行（强制继续模式）")
                check_results.append(True)
            else:
                check_results.append(False)
                return 1

    # 总结前置检查结果
    logging.info(LOG_SEPARATOR)
    successful_checks = sum(check_results)
    total_checks = len(check_results)

    logging.info(f"前置检查完成: {successful_checks}/{total_checks} 项检查通过")

    if successful_checks < total_checks and not force_continue:
        logging.error("由于某些检查未通过，训练将不会启动。")
        logging.error("您可以修复问题后重试，或使用 --force-continue 参数强制继续。")
        return 1

    # 集成流程：6. 准备执行训练
    logging.info(LOG_SEPARATOR)
    logging.info("6. 准备执行训练...")

    # 构造训练命令，按标准格式组织命令参数
    # 添加 -u 参数，确保子进程的 Python 以无缓冲模式运行
    cmd: List[str] = [sys.executable, '-u', TRAIN_MAIN_SCRIPT, '--game', args.game, '--algo', args.algo,
           '--config', args.config]

    # 仅当用户指定设备时添加设备参数
    if args.device:
        cmd.extend(['--device', args.device])

    # 传递依赖检查和环境检查标志给训练主程序
    if args.skip_dependency_check:
        cmd.append('--skip-dependency-check')

    if args.skip_environment_check:
        cmd.append('--skip-environment-check')

    if args.environment_warn_only:
        cmd.append('--environment-warn-only')

    # 传递强制继续执行标志
    if args.force_continue:
        cmd.append('--force-continue')

    # 添加环境要求参数
    cmd.extend(['--min-gpu-memory', str(args.min_gpu_memory)])
    cmd.extend(['--min-system-memory', str(args.min_system_memory)])
    cmd.extend(['--min-cpu-cores', str(args.min_cpu_cores)])

    # 传递断点续训相关参数
    if args.resume:
        cmd.append('--resume')
    if args.checkpoint_dir:
        cmd.extend(['--checkpoint-dir', args.checkpoint_dir])

    # 传递数据加载参数
    cmd.extend(['--num-workers', str(args.num_workers)])

    # 记录完整命令，便于用户了解实际执行的操作
    cmd_str = ' '.join(cmd)
    logging.info(f"执行训练命令: {cmd_str}")
    logging.info("训练开始，这可能需要较长时间...")
    logging.info(LOG_SEPARATOR)

    try:
        # 使用子进程执行训练命令，记录开始时间
        start_time = datetime.now()

        # 设置环境变量，确保 Python 输出为无缓冲模式
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'

        # 使用Popen代替run，以便实时捕获输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='gbk',  # 在Windows中文系统中使用GBK编码读取子进程输出
            bufsize=0,  # 设置为0表示无缓冲
            env=env    # 传递设置了无缓冲环境变量的环境
        )

        # 进度和时间相关变量
        progress_pattern = r'(进度|训练进度)[:：]\s*(\d+)[%％]'  # 匹配进度百分比，包括"进度:"和"训练进度:"
        episode_pattern = r'(回合|第)\s*(\d+)[/](\d+)(\s轮训练|\s*回合|)'  # 匹配回合信息及"第 x/y 轮训练"格式
        loss_pattern = r'损失[:：]\s*([\d\.]+)'      # 匹配损失值
        reward_pattern = r'奖励[:：]\s*([-\d\.]+)'   # 匹配奖励值
        accuracy_pattern = r'准确率[:：]\s*([\d\.]+)[%％]?'  # 匹配准确率
        memory_pattern = r'显存使用[:：]\s*([\d\.]+)GB'  # 匹配显存使用

        current_progress = 0
        current_episode = 0
        total_episodes = 0
        current_loss = 0.0
        current_reward = 0.0
        current_accuracy = 0.0
        current_memory_usage = 0.0

        # 创建性能指标历史记录，用于跟踪趋势
        loss_history = deque(maxlen=SPEED_WINDOW_SIZE)
        reward_history = deque(maxlen=SPEED_WINDOW_SIZE)
        accuracy_history = deque(maxlen=SPEED_WINDOW_SIZE)

        # 上次更新进度的时间，用于控制更新频率
        last_progress_time = time.time()
        # 估计完成时间和剩余时间
        eta_str = "计算中..."
        remaining_str = "计算中..."

        # 训练速度历史记录，用于计算平均速度和估计剩余时间
        progress_history = deque(maxlen=SPEED_WINDOW_SIZE)

        # 读取并处理输出
        for line in iter(process.stdout.readline, ''):
            # 处理和记录输出
            logging.info(f"[子进程] {line.strip()}")

            # 尝试提取进度信息
            progress_match = re.search(progress_pattern, line)
            if progress_match:
                new_progress = int(progress_match.group(2))  # 注意: 组索引变为2，因为第1组是匹配类型
                current_time = time.time()

                if new_progress != current_progress:
                    # 添加到进度历史
                    progress_history.append((current_time, new_progress))
                    current_progress = new_progress

                    # 计算经过的时间
                    elapsed_time = (datetime.now() - start_time).total_seconds()

                    # 计算训练速度和估计剩余时间
                    speed, remaining_time, speed_status = calculate_training_speed(
                        progress_history, current_time, current_progress
                    )

                    # 格式化剩余时间
                    if remaining_time != float('inf') and remaining_time > 0:
                        h, r = divmod(remaining_time, 3600)
                        m, s = divmod(r, 60)
                        remaining_str = f"{int(h):02d}:{int(m):02d}:{int(s):02d}"

                        # 计算估计完成时间点
                        eta_time = datetime.now() + timedelta(seconds=remaining_time)
                        eta_str = eta_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        remaining_str = "计算中..."
                        eta_str = "计算中..."

                    # 限制更新频率，避免过多输出
                    if current_time - last_progress_time > PROGRESS_UPDATE_INTERVAL:
                        progress_bar = '#' * (current_progress * PROGRESS_BAR_WIDTH // 100) + '-' * (PROGRESS_BAR_WIDTH - current_progress * PROGRESS_BAR_WIDTH // 100)

                        # 构造并输出完整的进度信息
                        progress_info = (
                            f"\n{LOG_SEPARATOR}\n"
                            f"训练进度: [{progress_bar}] {current_progress}%\n"
                            f"已用时间: {int(elapsed_time//3600):02d}:{int((elapsed_time%3600)//60):02d}:{int(elapsed_time%60):02d}\n"
                            f"训练速度: {speed*100:.4f}%/秒 ({speed_status})\n"
                            f"预计剩余: {remaining_str}\n"
                            f"预计完成: {eta_str}\n"
                        )

                        # 如果有其他信息，添加到进度信息中
                        if current_episode > 0 and total_episodes > 0:
                            progress_info += f"当前回合: {current_episode}/{total_episodes} ({current_episode*100/total_episodes:.1f}%)\n"

                        # 添加性能指标和趋势分析
                        performance_info = []

                        # 损失值及趋势
                        if current_loss > 0:
                            loss_trend, loss_change = analyze_metric_trend(loss_history)
                            trend_symbol = "→" if loss_trend == "持平" else "↑" if "上升" in loss_trend else "↓"
                            performance_info.append(f"损失: {current_loss:.6f} {trend_symbol} [{loss_trend}]")

                        # 奖励值及趋势
                        if reward_history:
                            reward_trend, reward_change = analyze_metric_trend(reward_history)
                            trend_symbol = "→" if reward_trend == "持平" else "↑" if "上升" in reward_trend else "↓"
                            performance_info.append(f"奖励: {current_reward:.4f} {trend_symbol} [{reward_trend}]")

                        # 准确率及趋势
                        if accuracy_history:
                            accuracy_trend, accuracy_change = analyze_metric_trend(accuracy_history)
                            trend_symbol = "→" if accuracy_trend == "持平" else "↑" if "上升" in accuracy_trend else "↓"
                            performance_info.append(f"准确率: {current_accuracy:.2f}% {trend_symbol} [{accuracy_trend}]")

                        # 显存使用
                        if current_memory_usage > 0:
                            performance_info.append(f"显存使用: {current_memory_usage:.2f}GB")

                        # 将性能指标添加到进度信息
                        if performance_info:
                            progress_info += "性能指标:\n  " + "\n  ".join(performance_info) + "\n"

                        progress_info += f"{LOG_SEPARATOR}\n"

                        logging.info(progress_info)
                        last_progress_time = current_time

            # 尝试提取回合信息
            episode_match = re.search(episode_pattern, line)
            if episode_match:
                # 提取回合信息，处理两种不同格式
                if episode_match.group(1) == "回合":
                    current_episode = int(episode_match.group(2))
                    total_episodes = int(episode_match.group(3))
                else:  # "第 x/y 轮训练"格式
                    current_episode = int(episode_match.group(2))
                    total_episodes = int(episode_match.group(3))
                    # 对于轮次信息，记录到日志
                    logging.debug(f"检测到训练轮次信息: 第 {current_episode}/{total_episodes} 轮")

                # 如果我们首次获取到总回合数，但当前进度为0，则可根据回合计算进度
                if current_progress == 0 and total_episodes > 0 and current_episode > 0:
                    new_progress = int(current_episode * 100 / total_episodes)
                    if new_progress > current_progress:
                        current_progress = new_progress
                        # 添加到进度历史，确保进度可跟踪
                        current_time = time.time()
                        progress_history.append((current_time, current_progress))
                        logging.debug(f"基于轮次计算进度: {current_progress}%")

            # 尝试提取损失信息
            loss_match = re.search(loss_pattern, line)
            if loss_match:
                try:
                    current_loss = float(loss_match.group(1))
                    loss_history.append(current_loss)
                except ValueError:
                    pass

            # 尝试提取奖励信息
            reward_match = re.search(reward_pattern, line)
            if reward_match:
                try:
                    current_reward = float(reward_match.group(1))
                    reward_history.append(current_reward)
                except ValueError:
                    pass

            # 尝试提取准确率信息
            accuracy_match = re.search(accuracy_pattern, line)
            if accuracy_match:
                try:
                    current_accuracy = float(accuracy_match.group(1))
                    accuracy_history.append(current_accuracy)
                except ValueError:
                    pass

            # 尝试提取显存使用信息
            memory_match = re.search(memory_pattern, line)
            if memory_match:
                try:
                    current_memory_usage = float(memory_match.group(1))
                except ValueError:
                    pass

        # 等待进程结束
        return_code = process.wait()
        end_time = datetime.now()

        # 计算训练总时长，便于用户评估训练效率
        duration = end_time - start_time
        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)

        # 根据命令返回码判断训练结果并记录相应日志
        if return_code == 0:
            logging.info(LOG_SEPARATOR)
            logging.info("训练成功完成!")
            logging.info(f"训练时长: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
            logging.info(f"训练日志已保存至: {log_file}")
            return 0
        else:
            # 训练失败，提供详细的错误信息和日志位置
            logging.error(LOG_SEPARATOR)
            logging.error(f"训练失败，返回码: {return_code}")
            logging.error(f"训练时长: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
            logging.error(f"请查看训练日志了解详细错误信息: {log_file}")
            return 1
    except Exception as e:
        # 捕获并记录执行过程中的异常
        logging.error(LOG_SEPARATOR)
        logging.error(f"执行训练命令时发生异常: {e}")
        logging.error(f"请检查系统环境和权限")
        return 1
    finally:
        # 确保关闭所有日志处理器，避免资源泄漏
        for handler in logging.root.handlers[:]:
            handler.close()
            logging.root.removeHandler(handler)


if __name__ == "__main__":
    # 使用 main 函数的返回值作为脚本的退出码
    sys.exit(main())