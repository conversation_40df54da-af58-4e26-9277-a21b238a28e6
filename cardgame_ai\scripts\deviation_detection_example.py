#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
偏离检测示例脚本

展示如何使用偏离检测器检测对手偏离GTO的行为，并采取剥削性的对策。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.opponent_modeling.deviation_detector import DeviationDetector
from cardgame_ai.algorithms.gto_approximation import GTOPolicy
from cardgame_ai.algorithms.human_policy_network import HumanPolicyNetwork
from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='偏离检测示例')
    
    parser.add_argument('--gto_policy_path', type=str, default='models/gto/gto_policy.pkl',
                        help='GTO策略文件路径')
    parser.add_argument('--human_policy_path', type=str, default='models/human_policy/human_policy.pt',
                        help='人类策略网络路径')
    parser.add_argument('--model_path', type=str, default='models/efficient_zero/model.pt',
                        help='EfficientZero模型路径')
    parser.add_argument('--deviation_threshold', type=float, default=0.3,
                        help='偏离阈值，超过此值被视为显著偏离')
    parser.add_argument('--enable_exploitation', action='store_true',
                        help='启用剥削策略')
    parser.add_argument('--num_games', type=int, default=5,
                        help='模拟游戏数量')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def state_feature_extractor(state: DouDizhuState) -> str:
    """
    状态特征提取器
    
    将状态转换为特征字符串，用于GTO策略查询。
    
    Args:
        state: 斗地主游戏状态
        
    Returns:
        特征字符串
    """
    # 简化的特征提取，实际应用中可能需要更复杂的逻辑
    features = []
    
    # 添加当前玩家
    features.append(f"player:{state.current_player}")
    
    # 添加地主信息
    features.append(f"landlord:{state.landlord_player}")
    
    # 添加手牌信息
    for i in range(3):
        features.append(f"hand{i}:{len(state.player_hands[i])}")
    
    # 添加上一手牌信息
    if state.last_move:
        features.append(f"last_move:{state.last_move.card_type.name}")
        features.append(f"last_player:{state.last_player}")
    else:
        features.append("last_move:None")
        features.append("last_player:None")
    
    # 添加PASS次数
    features.append(f"num_passes:{state.num_passes}")
    
    return "|".join(features)


def default_policy_generator(state: DouDizhuState, legal_actions: List[int]) -> np.ndarray:
    """
    默认策略生成器
    
    为没有预计算策略的状态生成近似GTO策略。
    
    Args:
        state: 斗地主游戏状态
        legal_actions: 合法动作列表
        
    Returns:
        近似GTO策略分布
    """
    # 简化的策略生成，实际应用中可能需要更复杂的逻辑
    policy = np.zeros(max(legal_actions) + 1)
    
    # 如果有炸弹或火箭，增加其概率
    for action in legal_actions:
        card_group = state.get_card_group_from_action(action)
        if card_group and card_group.type.name in ['BOMB', 'ROCKET']:
            policy[action] = 2.0
        else:
            policy[action] = 1.0
    
    # 归一化
    policy = policy / np.sum(policy)
    
    return policy


def simulate_game(hybrid_system: HybridDecisionSystem, env: DouDizhuEnvironment):
    """
    模拟游戏
    
    Args:
        hybrid_system: 混合决策系统
        env: 游戏环境
    """
    # 重置环境
    state = env.reset()
    done = False
    
    # 创建对手代理（使用规则代理）
    opponent_agent = RuleBasedAgent()
    
    # 游戏循环
    while not done:
        # 获取当前玩家
        current_player = state.current_player
        
        # 获取合法动作
        legal_actions = env.get_legal_actions(state)
        
        # 如果是AI玩家（玩家0）
        if current_player == 0:
            # 使用混合决策系统做出决策
            action = hybrid_system.act(state, legal_actions)
        else:
            # 使用对手代理做出决策
            action = opponent_agent.act(state, legal_actions)
            
            # 记录对手动作（用于偏离检测）
            hybrid_system.record_opponent_action(state, action)
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 更新状态
        state = next_state
        
        # 如果游戏结束，更新奖励
        if done:
            # 获取玩家0的奖励
            player_reward = reward[0] if isinstance(reward, list) else reward
            hybrid_system.update_reward(player_reward)
            
            # 打印游戏结果
            winner = info.get('winner', -1)
            logger.info(f"游戏结束，获胜玩家: {winner}, 玩家0奖励: {player_reward}")


def main():
    """主函数"""
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
    
    # 创建环境
    env = DouDizhuEnvironment()
    
    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)
    
    # 创建GTO策略
    gto_policy = GTOPolicy(
        policy_path=args.gto_policy_path if os.path.exists(args.gto_policy_path) else None,
        feature_extractor=state_feature_extractor,
        default_policy_generator=default_policy_generator
    )
    
    # 创建人类策略网络
    human_policy = HumanPolicyNetwork(
        state_dim=observation_shape[0],
        action_dim=env.action_space.n
    )
    
    # 如果有预训练模型，加载参数
    if args.human_policy_path and os.path.exists(args.human_policy_path):
        checkpoint = torch.load(args.human_policy_path, map_location='cpu')
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            human_policy.load_state_dict(checkpoint['model_state_dict'])
        else:
            human_policy.load_state_dict(checkpoint)
        logger.info(f"已加载人类策略网络: {args.human_policy_path}")
    
    # 创建偏离检测器
    deviation_detector = DeviationDetector(
        state_dim=observation_shape[0],
        action_dim=env.action_space.n,
        gto_policy_source=gto_policy,
        human_policy_network=human_policy,
        feature_extractor=state_feature_extractor,
        deviation_threshold=args.deviation_threshold
    )
    
    # 创建EfficientZero模型
    model = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=128,
        state_dim=64,
        use_resnet=False
    )
    
    # 如果有预训练模型，加载参数
    if args.model_path and os.path.exists(args.model_path):
        model.load(args.model_path)
        logger.info(f"已加载预训练模型: {args.model_path}")
    
    # 创建规则代理
    rule_agent = RuleBasedAgent()
    
    # 创建混合决策系统
    hybrid_system = HybridDecisionSystem(
        neural_network_model=model,
        search_model=model,
        rule_agent=rule_agent,
        deviation_detector=deviation_detector,
        gto_policy=gto_policy,
        meta_strategy="adaptive",
        deviation_threshold=args.deviation_threshold,
        enable_exploitation=args.enable_exploitation
    )
    
    # 模拟游戏
    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        simulate_game(hybrid_system, env)
    
    # 打印最终统计信息
    stats = hybrid_system.get_stats()
    logger.info(f"总决策次数: {stats['decisions']}")
    
    if "deviation_detection" in stats:
        deviation_stats = stats["deviation_detection"]
        logger.info(f"偏离检测次数: {deviation_stats['total_checks']}")
        logger.info(f"检测到的偏离次数: {deviation_stats['deviation_detections']}")
        logger.info(f"偏离比例: {deviation_stats['deviation_ratio']:.2f}")
        logger.info(f"剥削动作次数: {deviation_stats['exploitation_actions']}")
        logger.info(f"剥削比例: {deviation_stats['exploitation_ratio']:.2f}")
        
        if "deviation_pattern" in deviation_stats:
            pattern = deviation_stats["deviation_pattern"]
            logger.info(f"偏离模式: {pattern.get('pattern', 'unknown')}")
            logger.info(f"模式置信度: {pattern.get('confidence', 0.0):.2f}")
    
    return 0


if __name__ == "__main__":
    main()
