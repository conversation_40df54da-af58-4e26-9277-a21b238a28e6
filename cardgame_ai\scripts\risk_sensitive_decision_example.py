#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
风险敏感决策示例脚本

展示如何在MCTS中使用CVaR风险敏感决策，使AI在不确定环境中更加谨慎。
"""

import os
import sys
import logging
import argparse
import numpy as np
import torch
from typing import Dict, Any, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.mcts import MCTS
from cardgame_ai.algorithms.risk_sensitive_rl import CVaRCalculator
from cardgame_ai.algorithms.distributional_value_head import DistributionalValueHead


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='风险敏感决策示例')

    # MCTS参数
    parser.add_argument('--num_simulations', type=int, default=50, help='MCTS模拟次数')
    parser.add_argument('--discount', type=float, default=0.997, help='折扣因子')
    parser.add_argument('--temperature', type=float, default=1.0, help='温度参数')

    # 风险敏感决策参数
    parser.add_argument('--risk_alpha', type=float, default=0.05, help='CVaR的置信水平')
    parser.add_argument('--risk_beta', type=float, default=0.1, help='风险厌恶系数')

    # 游戏参数
    parser.add_argument('--game', type=str, default='landlord',
                        choices=['landlord', 'poker'],
                        help='游戏类型')

    return parser.parse_args()


def create_mock_model():
    """
    创建模拟模型，用于测试
    """
    class MockModel:
        def __init__(self):
            self.state_dim = 128
            self.action_dim = 100
            self.use_distributional_value = True
            self.value_support_size = 601
            self.value_min = -300
            self.value_max = 300

            # 创建分布式价值头
            self.distributional_value_head = DistributionalValueHead(
                input_dim=128,
                hidden_dim=64,
                value_support_size=601,
                value_min=-300,
                value_max=300
            )

        def represent(self, state):
            """将状态转换为隐藏状态"""
            # 模拟表示网络
            return torch.randn(1, self.state_dim)

        def predict(self, hidden_state):
            """预测策略和值"""
            # 模拟预测网络
            policy = torch.softmax(torch.randn(1, self.action_dim), dim=1)

            # 使用分布式价值头
            value_logits = self.distributional_value_head(hidden_state)
            value = self.distributional_value_head.compute_expected_value(value_logits)

            return policy, value

        def dynamics(self, hidden_state, action):
            """预测下一个隐藏状态和奖励"""
            # 模拟动态网络
            next_hidden_state = hidden_state + 0.1 * torch.randn_like(hidden_state)
            reward = torch.tanh(torch.randn(1))
            return next_hidden_state, reward

    return MockModel()


def create_mock_environment(game_type='landlord'):
    """
    创建模拟环境，用于测试
    """
    class MockEnvironment:
        def __init__(self, game_type):
            self.game_type = game_type
            self.action_space = MockActionSpace()
            self.current_player = "landlord"

        def reset(self):
            """重置环境"""
            return MockState()

        def get_legal_actions(self, state):
            """获取合法动作"""
            return [i for i in range(20) if i % 3 != 0]  # 简单地排除一些动作

    class MockActionSpace:
        def __init__(self):
            self.n = 100

    class MockState:
        def __init__(self):
            self.current_player = "landlord"

        def get_player_id(self):
            return self.current_player

    return MockEnvironment(game_type)


def create_mock_belief_state():
    """
    创建模拟信念状态，用于测试
    """
    class MockBeliefState:
        def __init__(self):
            self.confidence = 0.7
            self.card_probabilities = {
                'A': 0.8, '2': 0.6, '3': 0.4, '4': 0.2, '5': 0.5,
                '6': 0.3, '7': 0.7, '8': 0.9, '9': 0.1, '10': 0.5,
                'J': 0.6, 'Q': 0.4, 'K': 0.2
            }

    return MockBeliefState()


def compare_risk_profiles(risk_alpha=0.05, risk_beta=0.1):
    """
    比较不同风险配置的决策差异

    Args:
        risk_alpha: CVaR的置信水平
        risk_beta: 风险厌恶系数
    """
    # 创建CVaR计算器
    cvar_calc = CVaRCalculator(alpha=risk_alpha, beta=risk_beta)

    # 创建两种不同的回报分布
    # 分布1：均值较高但有小概率极端负值（高风险高回报）
    dist1 = np.concatenate([
        np.random.normal(5.0, 1.0, 90),  # 90%的样本是均值为5的正态分布
        np.random.normal(-20.0, 5.0, 10)  # 10%的样本是均值为-20的正态分布
    ])

    # 分布2：均值较低但更稳定（低风险低回报）
    dist2 = np.random.normal(3.0, 1.0, 100)  # 均值为3的正态分布

    # 计算标准统计量
    mean1 = np.mean(dist1)
    mean2 = np.mean(dist2)
    std1 = np.std(dist1)
    std2 = np.std(dist2)

    # 计算风险度量
    var1 = cvar_calc.compute_var(dist1)
    var2 = cvar_calc.compute_var(dist2)
    cvar1 = cvar_calc.compute_cvar(dist1)
    cvar2 = cvar_calc.compute_cvar(dist2)

    # 计算风险敏感值
    risk_value1 = cvar_calc.compute_risk_sensitive_value(dist1)
    risk_value2 = cvar_calc.compute_risk_sensitive_value(dist2)

    # 打印结果
    logger.info("\n=== 风险配置 ===")
    logger.info(f"CVaR置信水平 (alpha): {risk_alpha}")
    logger.info(f"风险厌恶系数 (beta): {risk_beta}")

    logger.info("\n=== 分布1（高风险高回报）===")
    logger.info(f"均值: {mean1:.4f}")
    logger.info(f"标准差: {std1:.4f}")
    logger.info(f"VaR: {var1:.4f}")
    logger.info(f"CVaR: {cvar1:.4f}")
    logger.info(f"风险敏感值: {risk_value1:.4f}")

    logger.info("\n=== 分布2（低风险低回报）===")
    logger.info(f"均值: {mean2:.4f}")
    logger.info(f"标准差: {std2:.4f}")
    logger.info(f"VaR: {var2:.4f}")
    logger.info(f"CVaR: {cvar2:.4f}")
    logger.info(f"风险敏感值: {risk_value2:.4f}")

    # 风险中性决策（仅考虑均值）
    risk_neutral_choice = "分布1" if mean1 > mean2 else "分布2"

    # 风险敏感决策（考虑风险敏感值）
    risk_sensitive_choice = "分布1" if risk_value1 > risk_value2 else "分布2"

    logger.info("\n=== 决策比较 ===")
    logger.info(f"风险中性决策选择: {risk_neutral_choice}")
    logger.info(f"风险敏感决策选择: {risk_sensitive_choice}")

    return {
        "dist1": {
            "mean": mean1,
            "std": std1,
            "var": var1,
            "cvar": cvar1,
            "risk_value": risk_value1
        },
        "dist2": {
            "mean": mean2,
            "std": std2,
            "var": var2,
            "cvar": cvar2,
            "risk_value": risk_value2
        },
        "risk_neutral_choice": risk_neutral_choice,
        "risk_sensitive_choice": risk_sensitive_choice
    }


def run_mcts_comparison(args):
    """
    运行MCTS比较实验

    比较标准MCTS和风险敏感MCTS的决策差异

    Args:
        args: 命令行参数
    """
    # 创建模拟环境和模型
    env = create_mock_environment(args.game)
    model = create_mock_model()

    # 获取初始状态
    state = env.reset()

    # 获取合法动作
    legal_actions = env.get_legal_actions(state)
    actions_mask = [i < env.action_space.n and i in legal_actions for i in range(env.action_space.n)]

    # 创建信念追踪器
    belief_trackers = {
        "landlord": {"get_belief_state": create_mock_belief_state}
    }

    # 创建标准MCTS
    standard_mcts = MCTS(
        num_simulations=args.num_simulations,
        discount=args.discount,
        use_belief_state=True,
        use_risk_sensitive_decision=False
    )

    # 创建风险敏感MCTS
    risk_sensitive_mcts = MCTS(
        num_simulations=args.num_simulations,
        discount=args.discount,
        use_belief_state=True,
        use_risk_sensitive_decision=True,
        risk_alpha=args.risk_alpha,
        risk_beta=args.risk_beta
    )

    # 运行标准MCTS
    logger.info("\n=== 运行标准MCTS ===")
    standard_visit_counts, standard_policy, standard_explanation = standard_mcts.run(
        root_state=state,
        model=model,
        temperature=args.temperature,
        actions_mask=actions_mask,
        belief_trackers=belief_trackers,
        explain=True
    )

    # 运行风险敏感MCTS
    logger.info("\n=== 运行风险敏感MCTS ===")
    risk_visit_counts, risk_policy, risk_explanation = risk_sensitive_mcts.run(
        root_state=state,
        model=model,
        temperature=args.temperature,
        actions_mask=actions_mask,
        belief_trackers=belief_trackers,
        explain=True
    )

    # 比较结果
    logger.info("\n=== 决策比较 ===")

    # 获取标准MCTS的最佳动作
    standard_best_action = max(standard_visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"标准MCTS最佳动作: {standard_best_action}")

    # 获取风险敏感MCTS的最佳动作
    risk_best_action = max(risk_visit_counts.items(), key=lambda x: x[1])[0]
    logger.info(f"风险敏感MCTS最佳动作: {risk_best_action}")

    # 比较访问次数分布
    logger.info("\n访问次数分布比较:")
    for action in sorted(set(standard_visit_counts.keys()) | set(risk_visit_counts.keys()))[:10]:  # 只显示前10个动作
        std_count = standard_visit_counts.get(action, 0)
        risk_count = risk_visit_counts.get(action, 0)
        diff = risk_count - std_count
        logger.info(f"  动作 {action}: {std_count} -> {risk_count} (差异: {diff:+d})")

    # 比较策略分布
    logger.info("\n策略分布比较:")
    for action in sorted(set(standard_policy.keys()) | set(risk_policy.keys()))[:10]:  # 只显示前10个动作
        std_prob = standard_policy.get(action, 0.0)
        risk_prob = risk_policy.get(action, 0.0)
        diff = risk_prob - std_prob
        logger.info(f"  动作 {action}: {std_prob:.4f} -> {risk_prob:.4f} (差异: {diff:+.4f})")

    return {
        "standard_mcts": {
            "best_action": standard_best_action,
            "visit_counts": standard_visit_counts,
            "policy": standard_policy,
            "explanation": standard_explanation
        },
        "risk_sensitive_mcts": {
            "best_action": risk_best_action,
            "visit_counts": risk_visit_counts,
            "policy": risk_policy,
            "explanation": risk_explanation
        }
    }


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 比较风险配置
    logger.info("=== 比较风险配置 ===")
    risk_comparison = compare_risk_profiles(args.risk_alpha, args.risk_beta)

    # 运行MCTS比较
    logger.info("\n=== 运行MCTS比较 ===")
    mcts_comparison = run_mcts_comparison(args)

    logger.info("\n=== 实验完成 ===")


if __name__ == "__main__":
    main()
