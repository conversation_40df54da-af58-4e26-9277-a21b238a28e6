#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化配置是否正确应用
"""

import sys
import os
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_optimized_training_config():
    """测试优化训练配置是否正确加载"""
    print("🧪 测试优化配置加载...")
    
    try:
        # 导入训练系统
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        # 创建训练系统实例
        training_system = OptimizedTrainingSystem()
        
        # 设置日志
        training_system.setup_logging('INFO', 'logs')
        
        # 加载默认配置（应该包含我们的优化参数）
        config = training_system.get_default_config()
        
        print("✅ 配置加载成功！")
        print("\n📊 关键优化参数检查:")
        
        # 检查训练配置
        training_config = config.get('training', {})
        print(f"  批处理大小: {training_config.get('batch_size', '未设置')}")
        print(f"  数据加载线程: {training_config.get('num_workers', '未设置')}")
        
        # 检查资源配置
        resources_config = config.get('resources', {})
        gpu_config = resources_config.get('gpu', {})
        cpu_config = resources_config.get('cpu', {})
        print(f"  GPU显存使用率: {gpu_config.get('memory_fraction', '未设置')}")
        print(f"  CPU线程数: {cpu_config.get('num_threads', '未设置')}")
        
        # 检查数据配置
        data_config = config.get('data', {})
        print(f"  预取因子: {data_config.get('prefetch_factor', '未设置')}")
        print(f"  内存锁定: {data_config.get('pin_memory', '未设置')}")
        
        # 检查性能配置
        performance_config = config.get('performance', {})
        compute_config = performance_config.get('compute', {})
        print(f"  模型编译: {compute_config.get('compile_model', '未设置')}")
        
        # 验证关键优化参数
        expected_values = {
            'batch_size': 512,
            'num_workers': 12,
            'memory_fraction': 0.95,
            'num_threads': 16,
            'prefetch_factor': 6,
            'compile_model': True
        }
        
        print("\n🔍 优化参数验证:")
        all_correct = True
        
        if training_config.get('batch_size') == expected_values['batch_size']:
            print(f"  ✅ batch_size: {training_config.get('batch_size')} (正确)")
        else:
            print(f"  ❌ batch_size: {training_config.get('batch_size')} (期望: {expected_values['batch_size']})")
            all_correct = False
            
        if training_config.get('num_workers') == expected_values['num_workers']:
            print(f"  ✅ num_workers: {training_config.get('num_workers')} (正确)")
        else:
            print(f"  ❌ num_workers: {training_config.get('num_workers')} (期望: {expected_values['num_workers']})")
            all_correct = False
            
        if gpu_config.get('memory_fraction') == expected_values['memory_fraction']:
            print(f"  ✅ memory_fraction: {gpu_config.get('memory_fraction')} (正确)")
        else:
            print(f"  ❌ memory_fraction: {gpu_config.get('memory_fraction')} (期望: {expected_values['memory_fraction']})")
            all_correct = False
            
        if cpu_config.get('num_threads') == expected_values['num_threads']:
            print(f"  ✅ num_threads: {cpu_config.get('num_threads')} (正确)")
        else:
            print(f"  ❌ num_threads: {cpu_config.get('num_threads')} (期望: {expected_values['num_threads']})")
            all_correct = False
            
        if data_config.get('prefetch_factor') == expected_values['prefetch_factor']:
            print(f"  ✅ prefetch_factor: {data_config.get('prefetch_factor')} (正确)")
        else:
            print(f"  ❌ prefetch_factor: {data_config.get('prefetch_factor')} (期望: {expected_values['prefetch_factor']})")
            all_correct = False
            
        if compute_config.get('compile_model') == expected_values['compile_model']:
            print(f"  ✅ compile_model: {compute_config.get('compile_model')} (正确)")
        else:
            print(f"  ❌ compile_model: {compute_config.get('compile_model')} (期望: {expected_values['compile_model']})")
            all_correct = False
        
        if all_correct:
            print("\n🎉 所有优化参数配置正确！")
            return True
        else:
            print("\n⚠️  部分优化参数配置不正确，请检查配置文件。")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_yaml_config_loading():
    """测试YAML配置文件加载"""
    print("\n🧪 测试YAML配置文件加载...")
    
    try:
        import yaml
        
        # 测试base.yaml
        base_config_path = os.path.join(project_root, 'configs', 'base.yaml')
        if os.path.exists(base_config_path):
            with open(base_config_path, 'r', encoding='utf-8') as f:
                base_config = yaml.safe_load(f)
            print(f"  ✅ base.yaml 加载成功")
            print(f"    num_workers: {base_config.get('data', {}).get('num_workers', '未设置')}")
            print(f"    prefetch_factor: {base_config.get('data', {}).get('prefetch_factor', '未设置')}")
        else:
            print(f"  ❌ base.yaml 文件不存在: {base_config_path}")
            
        # 测试training/efficient_zero.yaml
        training_config_path = os.path.join(project_root, 'configs', 'training', 'efficient_zero.yaml')
        if os.path.exists(training_config_path):
            with open(training_config_path, 'r', encoding='utf-8') as f:
                training_config = yaml.safe_load(f)
            print(f"  ✅ efficient_zero.yaml 加载成功")
            print(f"    batch_size: {training_config.get('training', {}).get('batch_size', '未设置')}")
            print(f"    memory_fraction: {training_config.get('resources', {}).get('gpu', {}).get('memory_fraction', '未设置')}")
        else:
            print(f"  ❌ efficient_zero.yaml 文件不存在: {training_config_path}")
            
        return True
        
    except Exception as e:
        print(f"❌ YAML配置文件测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试优化配置...")
    print("=" * 60)
    
    # 测试1: 优化训练配置
    test1_result = test_optimized_training_config()
    
    # 测试2: YAML配置文件加载
    test2_result = test_yaml_config_loading()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  优化配置测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  YAML文件测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！优化配置已正确应用。")
        print("💡 现在可以重新运行训练脚本，应该能看到优化效果。")
    else:
        print("\n⚠️  部分测试失败，请检查配置文件。")
