"""
EfficientZero模型实现

该模块提供了一个基于图神经网络（GNN）增强的EfficientZero模型实现，
将手牌和场面公共信息构建成图结构，使用GNN提取特征，
并将这些特征融合到EfficientZero的状态表示层中。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union

from cardgame_ai.algorithms.efficient_zero import EfficientZeroModel
from cardgame_ai.algorithms.representation.gnn_encoder import GNNHandEncoder, build_card_graph


class GNNEfficientZeroModel(EfficientZeroModel):
    """
    GNN增强的EfficientZero模型

    使用图神经网络（GNN）增强手牌表示能力的EfficientZero模型。
    """

    def __init__(
        self,
        observation_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        projection_dim: int = 256,
        prediction_dim: int = 128,
        value_prefix_length: int = 5,
        belief_dim: int = 54,
        use_belief_state: bool = False,
        # GNN相关参数
        use_gnn: bool = True,
        node_feature_dim: int = 17,  # 4(花色) + 13(点数)
        gnn_hidden_dim: int = 64,
        gnn_output_dim: int = 32,
        gnn_type: str = 'gcn',
        gnn_layers: int = 2,
        gnn_dropout: float = 0.1,
        gnn_use_gate: bool = True,
        device: str = None
    ):
        """
        初始化GNN增强的EfficientZero模型

        Args:
            observation_shape: 观察空间形状
            action_shape: 动作空间形状
            hidden_dim: 隐藏层维度
            state_dim: 隐藏状态维度
            use_resnet: 是否使用残差网络架构
            projection_dim: 投影维度
            prediction_dim: 预测网络隐藏层维度
            value_prefix_length: 值前缀长度
            belief_dim: 信念状态维度
            use_belief_state: 是否使用信念状态
            use_gnn: 是否使用GNN增强
            node_feature_dim: 节点特征维度
            gnn_hidden_dim: GNN隐藏层维度
            gnn_output_dim: GNN输出维度
            gnn_type: GNN类型，可选'gcn'或'gat'
            gnn_layers: GNN层数
            gnn_dropout: GNN Dropout比率
            gnn_use_gate: 是否使用门控机制
            device: 计算设备
        """
        # 初始化父类
        super(GNNEfficientZeroModel, self).__init__(
            observation_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim,
            value_prefix_length=value_prefix_length,
            belief_dim=belief_dim,
            use_belief_state=use_belief_state,
            device=device
        )

        # GNN相关参数
        self.use_gnn = use_gnn
        self.node_feature_dim = node_feature_dim
        self.gnn_hidden_dim = gnn_hidden_dim
        self.gnn_output_dim = gnn_output_dim
        self.gnn_type = gnn_type
        self.gnn_layers = gnn_layers
        self.gnn_dropout = gnn_dropout
        self.gnn_use_gate = gnn_use_gate

        # 如果启用GNN
        if use_gnn:
            # 创建GNN手牌编码器
            self.gnn_encoder = GNNHandEncoder(
                node_feature_dim=node_feature_dim,
                gnn_hidden_dim=gnn_hidden_dim,
                output_dim=gnn_output_dim,
                num_gnn_layers=gnn_layers,
                gnn_type=gnn_type,
                dropout=gnn_dropout,
                use_gate=gnn_use_gate,
                device=self.device
            ).to(self.device)

            # 创建特征融合层
            self.feature_fusion = nn.Sequential(
                nn.Linear(state_dim + gnn_output_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, state_dim)
            ).to(self.device)

    def represent(self, observation: torch.Tensor, hand_cards: Optional[List[int]] = None, 
                  public_info: Optional[Dict[str, Any]] = None) -> torch.Tensor:
        """
        表示函数：将观察转换为隐藏状态，并可选地融合GNN特征

        Args:
            observation: 观察张量
            hand_cards: 手牌ID列表（可选）
            public_info: 公共信息（可选）

        Returns:
            隐藏状态
        """
        # 使用原始表示网络处理观察
        original_state = self.representation_network(observation)

        # 如果不使用GNN或未提供手牌，直接返回原始状态
        if not self.use_gnn or hand_cards is None:
            return original_state

        # 构建手牌图
        graph_data = build_card_graph(hand_cards, public_info)
        
        # 添加批次维度
        if not hasattr(graph_data, 'batch') or graph_data.batch is None:
            batch = torch.zeros(graph_data.x.size(0), dtype=torch.long, device=self.device)
            graph_data.batch = batch
        
        # 确保图数据在正确的设备上
        graph_data = graph_data.to(self.device)
        
        # 使用GNN编码手牌
        gnn_features = self.gnn_encoder(graph_data)
        
        # 确保批次维度匹配
        if original_state.size(0) != gnn_features.size(0):
            # 如果批次大小不匹配，复制GNN特征以匹配原始状态的批次大小
            gnn_features = gnn_features.repeat(original_state.size(0), 1)
        
        # 组合原始状态和GNN特征
        combined_features = torch.cat([original_state, gnn_features], dim=1)
        
        # 使用特征融合层生成最终状态
        enhanced_state = self.feature_fusion(combined_features)
        
        return enhanced_state

    def to(self, device):
        """
        移动模型到指定设备

        Args:
            device: 目标设备

        Returns:
            模型自身
        """
        self.device = device
        super().to(device)
        if self.use_gnn:
            self.gnn_encoder.to(device)
            self.feature_fusion.to(device)
        return self

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        state_dict = self.state_dict()
        torch.save(state_dict, path)

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        state_dict = torch.load(path, map_location=self.device)
        self.load_state_dict(state_dict) 