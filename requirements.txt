# 斗地主AI项目依赖文件
# 基于项目代码分析重构的完整依赖列表
# 更新时间: 2025-05-28
# 最新修复: EfficientZero训练系统PyTorch兼容性问题

# ============================================================================
# 核心必需依赖 (EfficientZero训练系统已验证)
# ============================================================================

# PyTorch 深度学习框架 - 核心依赖
torch>=2.0.0,<3.0.0
# NumPy 数值计算库 - 基础依赖
numpy>=1.20.0,<2.0.0
# Pandas 数据分析库
pandas>=1.3.0,<3.0.0
# Matplotlib 绘图库
matplotlib>=3.5.0,<4.0.0
# Seaborn 统计可视化
seaborn>=0.11.0,<1.0.0
# YAML配置文件处理
PyYAML>=6.0,<7.0.0
# 进度条库
tqdm>=4.62.0

# ============================================================================
# 扩展机器学习框架 (可选)
# ============================================================================

# PyTorch 扩展组件 (可选)
torchvision>=0.15.0
torchaudio>=2.0.0

# SciPy 科学计算库 (可选)
scipy>=1.7.0

# ============================================================================
# 强化学习和游戏环境
# ============================================================================

# OpenAI Gym 强化学习环境接口
gym>=0.21.0
gymnasium>=0.26.0

# 图神经网络库 (用于GNN模型)
torch-geometric>=2.3.0
torch-scatter>=2.1.0
torch-sparse>=0.6.0

# 网络图处理库
networkx>=2.6.0

# ============================================================================
# 数据处理和分析
# ============================================================================

# Pandas 数据分析库 - 已验证兼容性
pandas>=1.3.0,<3.0.0

# 数据验证库 (可选)
# pydantic>=1.8.0

# JSON处理增强 (可选)
# jsonschema>=4.0.0

# ============================================================================
# 可视化和绘图 - 已验证兼容性
# ============================================================================

# Matplotlib 绘图库 - 已验证兼容性
matplotlib>=3.5.0,<4.0.0

# Seaborn 统计可视化 - 已验证兼容性
seaborn>=0.11.0,<1.0.0

# Plotly 交互式可视化 (可选)
# plotly>=5.0.0

# ============================================================================
# 用户界面
# ============================================================================

# PySide6 Qt界面框架
PySide6>=6.4.0

# ============================================================================
# 进度条和日志
# ============================================================================

# 进度条库
tqdm>=4.62.0

# 日志配置增强
colorlog>=6.0.0

# ============================================================================
# 配置文件处理 - 已验证兼容性
# ============================================================================

# YAML配置文件处理 - 已验证兼容性
PyYAML>=6.0,<7.0.0

# TOML配置文件处理 (可选)
# toml>=0.10.0

# ============================================================================
# 网络和通信
# ============================================================================

# HTTP请求库
requests>=2.26.0

# WebSocket支持
websockets>=10.0

# ============================================================================
# 数据库和存储
# ============================================================================

# SQLite数据库增强 (内置模块)
# sqlite3

# 内存数据库 (可选)
# redis>=4.0.0

# ============================================================================
# 并行计算和多进程
# ============================================================================

# 多进程增强 (可选)
# multiprocessing-logging>=0.3.0

# 作业队列 (可选)
# joblib>=1.1.0

# 进程池增强 (可选)
# psutil>=5.8.0

# ============================================================================
# 数学和统计
# ============================================================================

# 统计分布库
statsmodels>=0.13.0

# 优化算法库
scikit-optimize>=0.9.0

# 机器学习库
scikit-learn>=1.0.0

# ============================================================================
# 文件处理和压缩
# ============================================================================

# 压缩文件处理
zipfile36>=0.1.3

# 文件监控
watchdog>=2.1.0

# ============================================================================
# 时间和日期处理
# ============================================================================

# 时间处理增强
python-dateutil>=2.8.0

# ============================================================================
# 系统和平台
# ============================================================================

# 跨平台路径处理 (Python 3.8+ 内置pathlib)
# pathlib2>=2.3.0

# 系统信息获取 (内置模块)
# platform

# 操作系统接口 (内置模块)
# os

# ============================================================================
# 开发和调试工具
# ============================================================================

# 内存分析
memory-profiler>=0.60.0

# 性能分析
line-profiler>=3.3.0

# ============================================================================
# 测试框架
# ============================================================================

# 单元测试框架
pytest>=7.0.0
pytest-cov>=3.0.0
pytest-xdist>=2.5.0

# ============================================================================
# 代码质量工具
# ============================================================================

# 代码格式化
black>=22.0.0
isort>=5.10.0

# 代码检查
flake8>=4.0.0
pylint>=2.12.0

# 类型检查
mypy>=0.950

# ============================================================================
# 文档生成
# ============================================================================

# 文档生成工具
sphinx>=4.0.0
sphinx-rtd-theme>=1.0.0

# Markdown文档
mkdocs>=1.4.0
mkdocs-material>=8.5.0

# ============================================================================
# 打包和分发
# ============================================================================

# 应用打包
PyInstaller>=5.7.0

# 包管理
setuptools>=60.0.0
wheel>=0.37.0

# ============================================================================
# 特殊算法库
# ============================================================================

# 高阶梯度计算 (用于MAML等元学习算法)
higher>=0.2.1

# 贝叶斯优化
bayesian-optimization>=1.4.0

# 遗传算法
deap>=1.3.0

# ============================================================================
# 图像处理 (如果需要可视化卡牌等)
# ============================================================================

# 图像处理库
Pillow>=8.3.0

# OpenCV (可选，用于高级图像处理)
opencv-python>=4.5.0

# ============================================================================
# 音频处理 (如果需要音效)
# ============================================================================

# 音频处理库
soundfile>=0.10.0

# ============================================================================
# 加密和安全
# ============================================================================

# 加密库
cryptography>=3.4.0

# ============================================================================
# 其他实用工具
# ============================================================================

# 命令行参数解析增强
click>=8.0.0

# 环境变量管理
python-dotenv>=0.19.0

# 缓存工具
cachetools>=4.2.0

# 重试机制
tenacity>=8.0.0

# 随机数生成增强
randomgen>=1.21.0

# ============================================================================
# 版本兼容性说明
# ============================================================================

# Python版本要求: >=3.8
# CUDA版本要求: >=11.8 (如果使用GPU)
# 操作系统: Windows 10+, Linux, macOS

# ============================================================================
# 安装说明
# ============================================================================

# 1. 最小安装 (仅核心依赖，适用于EfficientZero训练)：
#    pip install torch>=2.0.0 numpy>=1.20.0 pandas>=1.3.0 matplotlib>=3.5.0 seaborn>=0.11.0 PyYAML>=6.0 tqdm>=4.62.0

# 2. 完整安装 (所有依赖)：
#    pip install -r requirements.txt

# 3. 开发环境安装 (包含测试和代码质量工具)：
#    pip install -r requirements.txt
#    pip install pytest>=7.0.0 black>=22.0.0 flake8>=4.0.0

# 4. 验证安装：
#    python test_efficient_zero_real.py

# ============================================================================
# 修复历史
# ============================================================================

# 2025-05-28: 修复EfficientZero训练系统PyTorch兼容性问题
# - 修复PyTorch Sequential参数错误
# - 修复布尔张量兼容性问题
# - 修复Unicode编码问题
# - 验证核心依赖兼容性
