"""
模型保存工具模块

提供模型保存和加载功能。
"""
import os
import torch
import pickle
from typing import Dict, Any, Optional, Union


class ModelSaver:
    """
    模型保存器
    
    提供模型保存和加载功能。
    """
    
    @staticmethod
    def save_model(model: torch.nn.Module, path: str) -> None:
        """
        保存PyTorch模型
        
        Args:
            model (torch.nn.Module): PyTorch模型
            path (str): 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 保存模型
        torch.save(model.state_dict(), path)
    
    @staticmethod
    def load_model(model: torch.nn.Module, path: str) -> torch.nn.Module:
        """
        加载PyTorch模型
        
        Args:
            model (torch.nn.Module): PyTorch模型
            path (str): 加载路径
            
        Returns:
            torch.nn.Module: 加载后的模型
        """
        # 加载模型
        model.load_state_dict(torch.load(path))
        return model
    
    @staticmethod
    def save_checkpoint(state: Dict[str, Any], path: str) -> None:
        """
        保存检查点
        
        Args:
            state (Dict[str, Any]): 检查点状态
            path (str): 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 保存检查点
        torch.save(state, path)
    
    @staticmethod
    def load_checkpoint(path: str) -> Dict[str, Any]:
        """
        加载检查点
        
        Args:
            path (str): 加载路径
            
        Returns:
            Dict[str, Any]: 检查点状态
        """
        # 加载检查点
        return torch.load(path)
    
    @staticmethod
    def save_object(obj: Any, path: str) -> None:
        """
        保存Python对象
        
        Args:
            obj (Any): Python对象
            path (str): 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 保存对象
        with open(path, 'wb') as f:
            pickle.dump(obj, f)
    
    @staticmethod
    def load_object(path: str) -> Any:
        """
        加载Python对象
        
        Args:
            path (str): 加载路径
            
        Returns:
            Any: Python对象
        """
        # 加载对象
        with open(path, 'rb') as f:
            return pickle.load(f)
