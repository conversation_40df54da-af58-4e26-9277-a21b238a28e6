# ============================================================================
# EfficientZero 斗地主专用配置文件
# ============================================================================
#
# 【文件用途】
# 此文件是专门为斗地主游戏优化的EfficientZero算法配置，包含了针对斗地主
# 游戏特点调优的参数设置，如RLHF、EWC防遗忘、信念状态等高级功能。
#
# 【与其他配置文件关系】
# - 比base.yaml更具体，专注于斗地主游戏
# - 比training/efficient_zero.yaml更全面，包含高级功能
# - 是实际训练时的完整配置参考
#
# 【主要特色功能】
# - RLHF人类反馈学习
# - EWC防遗忘机制
# - 信念状态追踪
# - 风险敏感决策
# ============================================================================

# ============================================================================
# 基础配置 - 系统运行的基本设置
# ============================================================================
base:
  # 【随机种子】确保实验可重复性
  # - 42: 经典种子值 (推荐)
  # - 任意整数: 用于不同的实验
  seed: 42

  # 【设备选择】
  # - 'auto': 自动检测，有GPU用GPU (推荐)
  # - 'cpu': 强制使用CPU (调试用)
  # - 'cuda': 强制使用GPU (性能最佳)
  device: auto

  # 【并行配置】影响自对弈数据收集速度
  # 【num_actors】并行Actor数量，用于自对弈数据收集
  # - 4: 默认值，适合单机训练
  # - 8: 当前设置，平衡性能和资源
  # - 16: 高性能设置，需要更多内存
  # - 32: 极限设置，需要大量内存
  # 【代码位置】cardgame_ai/distributed/coordinator.py:433
  # 【性能影响】增加Actor数量可以加速数据收集，但会增加内存使用
  num_actors: 16

  # 【num_envs_per_actor】每个Actor管理的环境数量
  # - 2: 默认值，平衡内存和效率
  # - 4: 当前设置，更多并行环境
  # - 8: 高性能设置，需要更多内存
  # 【总环境数】= num_actors × num_envs_per_actor = 16 × 4 = 64个并行环境
  num_envs_per_actor: 4

  verbose: true    # 是否输出详细日志
  debug: false     # 是否启用调试模式

# ============================================================================
# 神经网络架构配置 - 影响模型性能和计算复杂度
# ============================================================================
model:
  # 【模型类型】
  # - "resnet": ResNet架构 (推荐，表达能力强)
  # - "mlp": 多层感知机 (简单，速度快)
  model_type: resnet

  # 【模型大小】预设的模型规模
  # - "small": 小模型 (速度快，容量有限)
  # - "medium": 中等模型 (推荐，平衡性能)
  # - "large": 大模型 (容量大，速度慢)
  model_size: medium

  # 【网络维度配置】
  # 【hidden_dim】隐藏层维度，影响模型表达能力
  # - 128: 轻量级 (速度快)
  # - 256: 标准型 (推荐)
  # - 512: 重量级 (表达能力强，需要更多显存)
  # 【代码位置】cardgame_ai/algorithms/efficient_zero/model.py:30
  hidden_dim: 256

  # 【state_dim】状态表示维度
  # - 64: 标准维度 (推荐)
  # - 128: 更丰富的状态表示
  state_dim: 64

  # 【分布式价值网络】提供更准确的价值估计
  # - true: 启用分布式价值 (推荐，更准确)
  # - false: 使用标量价值 (简单但精度较低)
  # 【代码位置】cardgame_ai/algorithms/efficient_zero.py:366
  use_distributional_value: true
  value_support_size: 601    # 分布支持大小，影响价值估计精度
  value_min: -300           # 价值范围最小值
  value_max: 300            # 价值范围最大值

  # 【自监督学习】提升表示学习质量
  # - true: 启用自监督学习 (推荐，提升样本效率)
  # - false: 禁用自监督学习
  use_self_supervision: true
  projection_dim: 256       # 投影维度
  prediction_dim: 128       # 预测维度

# MCTS配置
mcts:
  num_simulations: 50
  discount: 0.997
  dirichlet_alpha: 0.25
  exploration_fraction: 0.8  # 增加探索比例，从0.25提高到0.8
  pb_c_base: 19652
  pb_c_init: 2.5  # 增加探索常数，从1.25提高到2.5
  use_act: true
  act_min_simulations: 10
  act_confidence_threshold: 0.8  # 降低置信度阈值，从0.95降低到0.8
  act_visit_threshold: 20

  # 关键时刻检测器配置
  use_key_moment_detector: true  # 是否使用关键时刻检测器
  key_moment_detector_config:  # 关键时刻检测器配置
    end_game_threshold: 5  # 终局阈值（牌数）
    bomb_weight: 1.5  # 炸弹权重
    rocket_weight: 2.0  # 火箭权重
    critical_card_count_threshold: 0.3  # 关键牌数比例
    use_ml_model: false  # 是否使用机器学习模型

  # 动态预算分配配置
  use_dynamic_budget: true  # 是否使用动态预算分配
  dynamic_budget_config:  # 动态预算分配器配置
    default_budget: 50  # 默认预算（模拟次数）
    high_budget: 100  # 关键时刻高预算
    low_budget: 30  # 非关键时刻低预算
    key_moment_factor: 2.0  # 关键时刻预算乘数
    non_key_moment_factor: 0.6  # 非关键时刻预算乘数
    adaptive_mode: false  # 是否使用自适应模式
    min_budget: 10  # 最小预算
    max_budget: 200  # 最大预算
    use_budget_pool: true  # 是否使用预算池
    budget_pool_size: 1000  # 预算池大小
    budget_pool_refresh_interval: 100  # 预算池刷新间隔
    game_stage_weights:  # 游戏阶段权重
      early: 0.8  # 早期阶段
      mid: 1.0  # 中期阶段
      late: 1.2  # 晚期阶段
    player_role_weights:  # 玩家角色权重
      landlord: 1.1  # 地主
      farmer: 0.9  # 农民

  # 信念状态相关配置
  use_belief_state: true
  use_information_value: true
  information_value_weight: 0.3
  information_value_method: combined

  # 深度信念追踪器配置
  use_deep_belief_tracker: true  # 是否使用深度信念追踪器
  deep_belief_weight: 0.7  # 信念追踪器权重
  deep_belief_tracker_path: "models/belief_tracker_v1.pt"  # 预训练的信念追踪器模型路径
  deep_belief_tracker_enabled: true  # 是否启用深度信念追踪
  belief_tracker_update_interval: 5  # 信念更新间隔（动作数）
  belief_tracker_threshold: 0.05  # 信念更新阈值

  # 对手建模配置
  use_opponent_model_prior: true  # 是否使用对手模型先验
  opponent_model_prior_weight: 0.5  # 对手模型先验权重
  opponent_modeler_enabled: true  # 是否启用在线对手建模
  opponent_modeler_method: "stats"  # 对手建模方法：stats(统计), neural(神经网络), rule(规则), hybrid(混合)
  opponent_modeler_window_size: 15  # 对手动作历史窗口大小
  opponent_modeler_decay_factor: 0.95  # 历史动作权重衰减因子
  opponent_modeler_update_threshold: 0.1  # 模型更新阈值

  # 风险敏感决策配置
  use_risk_sensitive_decision: true
  risk_alpha: 0.05
  risk_beta: 0.1

# 训练配置
training:
  batch_size: 128
  num_unroll_steps: 5
  td_steps: 10
  value_loss_weight: 1.0
  policy_loss_weight: 1.0
  consistency_loss_weight: 0.1
  self_supervised_loss_weight: 0.1

  # 温度调度配置
  use_temperature_scheduler: true  # 启用温度调度
  initial_temperature: 2.0  # 初始温度，设置较高以增加随机性
  final_temperature: 0.5  # 最终温度
  temperature_decay: 0.9995  # 温度衰减率
  min_temperature: 0.3  # 最小温度

  # GTO正则化相关配置
  use_gto_regularization: true  # 是否使用GTO正则化
  gto_policy_path: "data/gto_policy.pkl"  # GTO策略文件路径
  gto_regularization_weight: 0.1  # GTO正则化权重
  gto_regularization_method: "js"  # 正则化方法: "kl"(KL散度), "js"(JS散度), "l2"(L2距离)
  gto_adaptive_weight: true  # 是否使用自适应权重
  gto_adaptive_weight_params:  # 自适应权重参数
    max_weight: 0.3  # 最大权重
    min_weight: 0.01  # 最小权重
    decay_rate: 0.995  # 权重衰减率
    increase_rate: 1.02  # 权重增长率
    performance_threshold: 0.05  # 性能阈值
    anneal_steps: 5000  # 衰减步数

  # RLHF 相关配置
  use_rlhf: true  # 是否使用强化学习来自人类反馈
  rlhf_loss_weight: 0.5  # RLHF 损失权重
  rlhf_data_path: "data/human_logs/preferences.json"  # 人类偏好数据文件路径
  rlhf_batch_size: 32  # RLHF 批次大小
  rlhf_preference_weight: 1.0  # 偏好学习损失权重
  rlhf_feedback_weight: 1.0  # 反馈分数损失权重
  rlhf_imitation_weight: 1.0  # 模仿学习损失权重

  # EWC防遗忘相关配置
  use_ewc: true  # 是否使用弹性权重固化防遗忘
  ewc_lambda: 1000.0  # EWC惩罚项权重
  ewc_diagonal_fisher: true  # 是否使用对角Fisher近似
  ewc_fisher_samples: 200  # 用于估计Fisher矩阵的样本数
  ewc_task_register_interval: 1000  # 任务注册间隔（回合数/步数）
  ewc_save_path: "models/ewc_checkpoints"  # EWC检查点保存路径

  learning_rate: 0.001
  weight_decay: 1e-4
  lr_scheduler: cosine
  mixed_precision: true

# Checkpoint 保存配置
checkpoint:
  dir: checkpoints/efficient_zero  # checkpoint保存目录
  save_interval: 1000              # 每隔多少步保存一次

# 对手建模配置
opponent_modeling:
  enabled: true  # 是否启用对手建模
  model_type: statistical  # statistical, neural
  window_size: 10  # 动作历史窗口大小
  decay_factor: 0.95  # 历史动作权重衰减因子
  update_threshold: 0.1  # 模型更新阈值
  enable_logging: true  # 是否启用日志记录
  model_path: "models/opponent_modeler.pt"  # 预训练的对手模型路径（如存在）

  # 上下文相关建模配置
  use_context_aware: true  # 是否使用上下文相关建模
  context_types:  # 上下文类型列表
    - player_role  # 玩家角色（地主/农民）
    - cards_left  # 剩余手牌数量
    - game_stage  # 游戏阶段（早期/中期/晚期）
    - relative_score  # 相对分数（领先/落后）

# 元学习与持续学习配置
continual_learning:
  enabled: true  # 是否启用持续学习
  meta_algorithm: "maml"  # 元学习算法: maml, reptile, both
  inner_lr: 0.01  # 内循环学习率
  num_inner_steps: 5  # 内循环步数
  task_sampler: "opponent_style"  # 任务采样方式: opponent_style, game_variation

  # EWC相关配置
  use_ewc: true  # 是否使用EWC防遗忘
  ewc_lambda: 1000.0  # EWC惩罚项权重
  ewc_diagonal_fisher: true  # 是否使用对角Fisher近似
  ewc_fisher_samples: 200  # 用于估计Fisher矩阵的样本数

  # 元学习验证相关
  validation_interval: 5  # 验证间隔（任务数）
  validation_episodes: 10  # 每个任务的验证局数

  # 快速适应相关
  adaptation_enabled: true  # 是否启用快速适应
  adaptation_interval: 10  # 适应间隔（对局数）
  adaptation_steps: 3  # 适应步数
  adaptation_lr: 0.05  # 适应学习率

# 强化学习来自人类反馈（RLHF）配置
rlhf:
  enabled: true  # 是否启用 RLHF
  data_path: "data/human_logs/preferences.json"  # 人类偏好数据文件路径
  batch_size: 32  # RLHF 批次大小
  weight: 0.5  # RLHF 损失权重（与总损失相关）
  loss_type: "bradley_terry"  # 损失类型：bradley_terry, dpo, ipo
  model_path: "models/preference_model.pt"  # 预训练的偏好模型路径（如存在）

  # 偏好学习相关
  use_preference_learning: true  # 是否使用偏好学习
  preference_weight: 1.0  # 偏好学习损失权重
  margin: 0.0  # 偏好学习边界值
  temperature: 1.0  # 偏好学习温度

  # 反馈分数相关
  use_feedback_scores: false  # 是否使用反馈分数
  feedback_weight: 0.5  # 反馈分数损失权重

  # 行为克隆相关
  use_imitation_learning: false  # 是否使用行为克隆
  imitation_weight: 0.3  # 行为克隆损失权重

  # 验证相关
  validation_split: 0.2  # 验证集比例
  validation_interval: 10  # 验证间隔（回合数）

  # 参考模型相关（用于 DPO/IPO）
  reference_model_path: null  # 参考模型路径
  reference_free: true  # 是否使用无参考模型的变体
  kl_weight: 0.1  # KL散度权重（用于 DPO/IPO）

# 环境配置
environment:
  max_episode_steps: 1000
  reward_scaling: 1.0
  vectorized: true
  action_encoding: one_hot  # one_hot, embedding
  state_encoding: vector  # vector, image
  difficulty: auto  # easy, medium, hard, auto (自动调整)

# 日志与评估
logging:
  tensorboard: true
  wandb: false
  log_interval: 3  # 从10改为3，增加日志记录频率
  checkpoint_interval: 100
  evaluation_interval: 50
  n_eval_games: 100
  eval_opponent_pool: [rule_based, random, efficient_zero]

# 实验与评估配置
experiment:
  name: "enhanced_model_v1"  # 实验名称
  description: "集成DeepBeliefTracker,OnlineOpponentModeler和RLHF的模型评估"
  baselines: ["standard_muzero", "rule_based"]  # 基准模型列表
  metrics: ["win_rate", "avg_reward", "completion_time", "decision_quality"]  # 评估指标
  ablation_studies:  # 消融实验配置
    - name: "no_belief_tracker"
      description: "禁用信念追踪器"
      mcts.use_deep_belief_tracker: false
    - name: "no_opponent_modeling"
      description: "禁用对手建模"
      mcts.use_opponent_model_prior: false
      opponent_modeling.enabled: false
    - name: "no_rlhf"
      description: "禁用RLHF"
      training.use_rlhf: false
      rlhf.enabled: false
    - name: "no_ewc"
      description: "禁用EWC防遗忘"
      training.use_ewc: false
      continual_learning.use_ewc: false
    - name: "no_gto_regularization"
      description: "禁用GTO正则化"
      training.use_gto_regularization: false
    - name: "no_dynamic_budget"
      description: "禁用动态预算分配"
      mcts.use_dynamic_budget: false
    - name: "full_model"
      description: "完整模型，包含所有新功能"
  run_configs:  # 运行配置
    - name: "standard"
      num_games: 1000
      opponent_types: ["rule_based", "random", "efficient_zero"]
    - name: "competitive"
      num_games: 500
      opponent_types: ["human_policy"]
    - name: "dynamic_budget_evaluation"
      description: "评估动态预算分配的效果"
      num_games: 500
      opponent_types: ["rule_based", "efficient_zero"]
      metrics: ["win_rate", "avg_reward", "decision_time", "quality_vs_time_ratio"]
      custom_settings:
        - mcts.use_dynamic_budget: true
          mcts.use_key_moment_detector: true
        - mcts.use_dynamic_budget: false
          mcts.use_key_moment_detector: false