#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化训练效果
"""

import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_optimized_training():
    """测试优化训练效果"""
    print("🚀 开始测试优化训练效果...")
    print("=" * 60)
    
    try:
        # 导入训练系统
        from cardgame_ai.zhuchengxu.optimized_training_integrated import OptimizedTrainingSystem
        
        # 创建训练系统实例
        training_system = OptimizedTrainingSystem()
        
        # 设置日志
        training_system.setup_logging('INFO', 'logs')
        
        # 加载配置
        config = training_system.get_default_config()
        
        # 检测设备
        device = training_system.detect_device()
        
        print(f"🖥️  使用设备: {device}")
        print("📊 优化配置摘要:")
        
        training_config = config.get('training', {})
        resources_config = config.get('resources', {})
        data_config = config.get('data', {})
        performance_config = config.get('performance', {})
        
        print(f"  批处理大小: {training_config.get('batch_size')} (优化: 256→512)")
        print(f"  数据加载线程: {training_config.get('num_workers')} (优化: 4→12)")
        print(f"  GPU显存使用: {resources_config.get('gpu', {}).get('memory_fraction', 0.9)*100:.0f}% (优化: 90%→95%)")
        print(f"  CPU线程数: {resources_config.get('cpu', {}).get('num_threads')} (优化: 8→16)")
        print(f"  预取因子: {data_config.get('prefetch_factor')} (优化: 2→6)")
        print(f"  模型编译: {performance_config.get('compute', {}).get('compile_model')} (优化: 启用PyTorch 2.0)")
        
        print("\n⏱️  开始短期训练测试 (仅测试配置是否生效)...")
        
        # 修改配置为短期测试
        test_config = config.copy()
        test_config['training']['epochs'] = 2  # 只训练2个epoch用于测试
        test_config['training']['episodes_per_epoch'] = 2  # 每轮只玩2局游戏
        test_config['training']['updates_per_epoch'] = 5   # 每轮只更新5次
        test_config['training']['log_frequency'] = 1       # 每步都记录日志
        
        print("🎮 测试配置:")
        print(f"  训练轮数: {test_config['training']['epochs']}")
        print(f"  每轮游戏数: {test_config['training']['episodes_per_epoch']}")
        print(f"  每轮更新数: {test_config['training']['updates_per_epoch']}")
        
        print("\n🏃 开始训练...")
        start_time = time.time()
        
        # 运行训练
        success = training_system.run_training(test_config, device, resume=False)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️  训练耗时: {duration:.2f} 秒")
        
        if success:
            print("✅ 优化训练测试成功！")
            print("\n📈 预期优化效果:")
            print("  - 模型更新速度应该比之前快 40-50%")
            print("  - GPU利用率应该达到 95%+")
            print("  - 显存利用率应该达到 80-90%")
            print("  - 数据加载等待时间显著减少")
            
            print("\n💡 建议:")
            print("  1. 现在可以运行完整训练，应该能看到显著的性能提升")
            print("  2. 监控GPU利用率和显存使用情况")
            print("  3. 如果系统稳定，可以考虑进一步优化参数")
            
            return True
        else:
            print("❌ 优化训练测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_optimization_summary():
    """显示优化总结"""
    print("\n" + "=" * 60)
    print("📋 优化配置总结")
    print("=" * 60)
    
    optimizations = [
        ("数据加载线程数", "4", "12", "+200%", "减少GPU等待时间"),
        ("预取因子", "2", "6", "+200%", "提前加载更多批次"),
        ("批处理大小", "256", "512", "+100%", "充分利用52GB显存"),
        ("GPU显存使用率", "90%", "95%", "+5%", "最大化硬件利用"),
        ("CPU线程数", "8", "16", "+100%", "提升并行计算效率"),
        ("并行Actor数", "4", "8", "+100%", "加速数据收集"),
        ("每Actor环境数", "2", "4", "+100%", "更多并行环境"),
        ("模型编译优化", "关闭", "开启", "20-30%", "PyTorch 2.0新特性"),
    ]
    
    print(f"{'配置项':<12} {'优化前':<8} {'优化后':<8} {'提升':<8} {'说明'}")
    print("-" * 60)
    
    for item, before, after, improvement, description in optimizations:
        print(f"{item:<12} {before:<8} {after:<8} {improvement:<8} {description}")
    
    print("\n🎯 预期总体效果:")
    print("  - 训练速度提升: 40-50%")
    print("  - GPU利用率: 88% → 95%+")
    print("  - 显存利用率: 3.5% → 80-90%")
    print("  - 模型更新时间: 30秒 → 15-20秒")

if __name__ == "__main__":
    print("🧪 优化训练效果测试")
    
    # 显示优化总结
    show_optimization_summary()
    
    # 询问是否进行测试
    print("\n❓ 是否进行短期训练测试？(y/n): ", end="")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes', '是', '1']:
            result = test_optimized_training()
            if result:
                print("\n🎉 测试完成！优化配置工作正常。")
            else:
                print("\n⚠️  测试未完全成功，请检查配置。")
        else:
            print("📝 跳过训练测试。配置已优化，可以直接运行完整训练。")
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断。")
