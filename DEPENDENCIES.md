# 斗地主AI项目依赖安装指南

## 📋 概述

本项目提供了两个依赖文件：
- `requirements-core.txt` - 核心必需依赖（推荐）
- `requirements.txt` - 完整依赖列表（包含可选组件）

## 🚀 快速开始（推荐）

### 1. 安装核心依赖

```bash
pip install -r requirements-core.txt
```

核心依赖包含：
- ✅ PyTorch 2.x (深度学习框架)
- ✅ NumPy (数值计算)
- ✅ Pandas (数据处理)
- ✅ Matplotlib (绘图)
- ✅ Seaborn (统计可视化)
- ✅ PyYAML (配置文件)
- ✅ tqdm (进度条)

### 2. 验证安装

```bash
python test_efficient_zero_real.py
```

如果看到 "EfficientZero训练系统修复验证成功！" 说明安装正确。

## 🔧 完整安装

如果需要所有功能（包括开发工具、测试框架等）：

```bash
pip install -r requirements.txt
```

## 🐛 常见问题

### 问题1：PyTorch安装失败
```bash
# 手动安装PyTorch
pip install torch>=2.0.0 --index-url https://download.pytorch.org/whl/cpu
```

### 问题2：依赖冲突
```bash
# 创建新的虚拟环境
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 重新安装
pip install -r requirements-core.txt
```

### 问题3：权限问题（Windows）
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## ✅ 系统要求

- **Python**: >=3.8
- **操作系统**: Windows 10+, Linux, macOS
- **内存**: 建议8GB+
- **存储**: 建议2GB可用空间

## 🎯 已验证环境

- ✅ Windows 11 + Python 3.13 + PyTorch 2.x
- ✅ EfficientZero训练系统正常运行
- ✅ 所有核心功能测试通过

## 📝 修复历史

### 2025-05-28
- ✅ 修复PyTorch Sequential参数错误
- ✅ 修复布尔张量兼容性问题
- ✅ 修复Unicode编码问题
- ✅ 验证核心依赖兼容性

## 🆘 获取帮助

如果遇到问题：
1. 首先尝试核心依赖安装：`pip install -r requirements-core.txt`
2. 运行验证脚本：`python test_efficient_zero_real.py`
3. 检查Python版本：`python --version`
4. 检查PyTorch版本：`python -c "import torch; print(torch.__version__)"`
