# 确保使用UTF-8编码
# 在PowerShell中设置输出编码
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

<#
.SYNOPSIS
    Find-VSCodeAugmentConfigs.ps1 - 查找 VS Code 插件 Augment.vscode-augment 的缓存和配置文件。
.DESCRIPTION
    该脚本会扫描常见的 VS Code 配置位置，列出与 Augment.vscode-augment 插件相关的文件和文件夹。
    它不会自动删除任何内容，仅供用户参考。
.NOTES
    作者: AI Assistant
    版本: 1.0
    创建日期: 2024-07-30
#>

# --- 配置区域 ---
$PluginId = "Augment.vscode-augment" # 要查找的插件ID

# --- 脚本主体 ---

Write-Host "开始查找插件 '$PluginId' 的配置文件和缓存..." -ForegroundColor Yellow

# 定义查找结果数组
$foundItems = @()

# 1. 用户设置 (settings.json)
Write-Host "`n[1] 正在检查用户设置 (settings.json)..." -ForegroundColor Cyan
$userSettingsPath = "$env:APPDATA\Code\User\settings.json"
if (Test-Path $userSettingsPath) {
    try {
        $settingsContent = Get-Content $userSettingsPath -Raw | ConvertFrom-Json -ErrorAction Stop
        $pluginSettings = $settingsContent.PSObject.Properties | Where-Object { $_.Name -like "*$PluginId*" -or $_.Name -like "*augment*" } # 更宽松的匹配
        if ($pluginSettings) {
            Write-Host "  [-] 在用户 settings.json 中找到以下相关配置:" -ForegroundColor Green
            $pluginSettings | ForEach-Object { Write-Host "    - $($_.Name)" }
            $foundItems += [PSCustomObject]@{
                Type = "User Settings Entry"
                Path = $userSettingsPath
                Details = ($pluginSettings | ForEach-Object { $_.Name }) -join ", "
            }
        } else {
            Write-Host "  [+] 用户 settings.json 中未直接找到与 '$PluginId' 相关的顶级键。" -ForegroundColor Gray
        }
    } catch {
        Write-Warning "  [!] 读取或解析用户 settings.json 出错: $($_.Exception.Message)"
    }
} else {
    Write-Warning "  [!] 未找到用户 settings.json: $userSettingsPath"
}

# 2. 全局存储 (Global Storage)
Write-Host "`n[2] 正在检查全局存储..." -ForegroundColor Cyan
$globalStoragePath = "$env:APPDATA\Code\User\globalStorage"
$pluginGlobalStoragePath = Join-Path -Path $globalStoragePath -ChildPath $PluginId.ToLower() # 插件ID在文件夹中通常是小写

if (Test-Path $pluginGlobalStoragePath) {
    Write-Host "  [-] 找到插件的全局存储文件夹:" -ForegroundColor Green
    Write-Host "    $pluginGlobalStoragePath"
    $foundItems += [PSCustomObject]@{
        Type = "Global Storage Folder"
        Path = $pluginGlobalStoragePath
        Details = "建议手动检查并删除此文件夹中的内容。"
    }
    # 可以选择列出其中的文件和子文件夹
    # Get-ChildItem -Path $pluginGlobalStoragePath -Recurse | ForEach-Object { Write-Host "      - $($_.FullName)" }
} else {
    Write-Host "  [+] 未找到插件 '$PluginId' 的全局存储文件夹: $pluginGlobalStoragePath" -ForegroundColor Gray
}

# 3. 扩展安装目录
Write-Host "`n[3] 正在检查扩展安装目录..." -ForegroundColor Cyan
$extensionsPath = "$env:USERPROFILE\.vscode\extensions"
if (Test-Path $extensionsPath) {
    $pluginExtensionDirs = Get-ChildItem -Path $extensionsPath -Directory | Where-Object { $_.Name -match "^$($PluginId.Split('.')[0])\.$($PluginId.Split('.')[1])-" } # 根据ID匹配, 例如 "augment.vscode-augment-"
    if ($pluginExtensionDirs) {
        Write-Host "  [-] 找到以下可能的插件安装目录:" -ForegroundColor Green
        $pluginExtensionDirs | ForEach-Object {
            Write-Host "    $($_.FullName)"
            $foundItems += [PSCustomObject]@{
                Type = "Extension Directory"
                Path = $_.FullName
                Details = "这是插件的安装位置，卸载插件时通常会移除，但可能残留配置。重新安装前可考虑手动删除。"
            }
        }
    } else {
        Write-Host "  [+] 未在 '$extensionsPath' 中找到名为 '$PluginId' 开头的扩展目录。" -ForegroundColor Gray
    }
} else {
    Write-Warning "  [!] 未找到 VS Code 扩展目录: $extensionsPath"
}

# --- 总结 ---
Write-Host "`n--- 查找完毕 ---" -ForegroundColor Yellow
if ($foundItems.Count -gt 0) {
    Write-Host "`n已找到以下与插件 '$PluginId' 相关的文件和文件夹:" -ForegroundColor Green
    $foundItems | Format-Table -AutoSize | Out-String | Write-Host
    Write-Host "`n重要提示:" -ForegroundColor Yellow
    Write-Host "  - 此脚本仅列出找到的项目，不会自动删除任何内容。"
    Write-Host "  - 在删除任何文件或文件夹之前，请确保 VS Code 已完全关闭。"
    Write-Host "  - 对于 'User Settings Entry'，您需要手动编辑 '$userSettingsPath' 文件并移除相关条目。"
    Write-Host "  - 对于文件夹 (Global Storage Folder, Extension Directory)，您可以导航到相应路径手动删除它们。"
    Write-Host "  - 删除全局存储和扩展目录通常是解决顽固配置问题的最有效方法。"
    Write-Host "  - 如果不确定，建议先备份相关文件/文件夹再进行删除。"
} else {
    Write-Host "未找到与插件 '$PluginId' 明确相关的已知配置文件或缓存位置。" -ForegroundColor Yellow
    Write-Host "如果问题依旧存在，可能需要检查插件是否有其他非标准的配置存储方式。"
}

Write-Host "`n脚本执行结束。" -ForegroundColor Yellow 