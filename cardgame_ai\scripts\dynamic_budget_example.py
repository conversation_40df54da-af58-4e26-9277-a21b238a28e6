#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
动态计算预算分配示例脚本

展示如何使用关键决策点检测和动态计算预算分配功能。
"""

import os
import sys
import argparse
import logging
import numpy as np
import torch
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from cardgame_ai.algorithms.hybrid_decision_system import HybridDecisionSystem
from cardgame_ai.algorithms.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.efficient_zero import EfficientZero
from cardgame_ai.algorithms.rule_based import RuleBasedAgent
from cardgame_ai.games.doudizhu.environment import DouDizhuEnvironment
from cardgame_ai.games.doudizhu.state import DouDizhuState

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='动态计算预算分配示例')
    
    parser.add_argument('--model_path', type=str, default=None,
                        help='模型路径')
    parser.add_argument('--key_moment_model', type=str, default=None,
                        help='关键决策点检测器模型路径')
    parser.add_argument('--base_simulations', type=int, default=50,
                        help='基础MCTS模拟次数')
    parser.add_argument('--critical_multiplier', type=int, default=10,
                        help='关键决策点时的MCTS模拟次数倍数')
    parser.add_argument('--critical_threshold', type=float, default=0.8,
                        help='关键决策点阈值')
    parser.add_argument('--num_games', type=int, default=5,
                        help='游戏数量')
    parser.add_argument('--seed', type=int, default=None,
                        help='随机种子')
    
    return parser.parse_args()


def simulate_game(hybrid_system: HybridDecisionSystem, env: DouDizhuEnvironment):
    """
    模拟一局游戏
    
    Args:
        hybrid_system: 混合决策系统
        env: 游戏环境
    """
    # 重置环境
    state = env.reset()
    done = False
    total_reward = 0
    
    # 游戏循环
    while not done:
        # 获取合法动作
        legal_actions = env.get_legal_actions()
        
        # 使用混合决策系统做出决策
        action = hybrid_system.act(state, legal_actions)
        
        # 执行动作
        state, reward, done, info = env.step(action)
        total_reward += reward
    
    # 游戏结束，更新奖励
    hybrid_system.update_reward(total_reward)
    
    # 打印游戏结果
    logger.info(f"游戏结束，总奖励: {total_reward}")
    
    # 打印统计信息
    stats = hybrid_system.get_stats()
    
    # 打印关键决策点统计
    if "key_moment_detection" in stats:
        key_moment_stats = stats["key_moment_detection"]
        logger.info(f"关键决策点统计:")
        logger.info(f"  关键决策点数量: {key_moment_stats['critical_moments']}")
        logger.info(f"  关键决策点比例: {key_moment_stats['critical_ratio']:.2f}")
        logger.info(f"  平均关键程度评分: {key_moment_stats['avg_criticality']:.4f}")
        logger.info(f"  基础模拟次数: {key_moment_stats['base_simulations']}")
        logger.info(f"  关键决策点倍数: {key_moment_stats['critical_multiplier']}")
        logger.info(f"  平均模拟次数: {key_moment_stats['avg_simulations']:.2f}")
    
    # 打印组件使用统计
    logger.info(f"组件使用统计:")
    for name, count in stats["component_usage"].items():
        logger.info(f"  {name}: {count}")


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置随机种子
    if args.seed is not None:
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
    
    # 创建游戏环境
    env = DouDizhuEnvironment()
    
    # 创建模型
    model = EfficientZero(model_path=args.model_path)
    
    # 创建规则代理
    rule_agent = RuleBasedAgent()
    
    # 创建关键决策点检测器
    key_moment_detector = None
    if args.key_moment_model:
        key_moment_detector = KeyMomentDetector.load(args.key_moment_model)
        logger.info(f"已加载关键决策点检测器: {args.key_moment_model}")
    
    # 创建混合决策系统
    hybrid_system = HybridDecisionSystem(
        neural_network_model=model,
        search_model=model,
        rule_agent=rule_agent,
        key_moment_detector=key_moment_detector,
        meta_strategy="adaptive",
        base_mcts_simulations=args.base_simulations,
        critical_mcts_multiplier=args.critical_multiplier,
        critical_threshold=args.critical_threshold
    )
    
    # 模拟游戏
    for i in range(args.num_games):
        logger.info(f"开始游戏 {i+1}/{args.num_games}")
        simulate_game(hybrid_system, env)
    
    # 打印最终统计信息
    stats = hybrid_system.get_stats()
    logger.info(f"总决策次数: {stats['decisions']}")
    
    if "key_moment_detection" in stats:
        key_moment_stats = stats["key_moment_detection"]
        logger.info(f"总关键决策点数量: {key_moment_stats['critical_moments']}")
        logger.info(f"关键决策点比例: {key_moment_stats['critical_ratio']:.2f}")
    
    return 0


if __name__ == "__main__":
    main()
