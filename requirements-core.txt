# 斗地主AI项目核心依赖文件
# 仅包含EfficientZero训练系统必需的核心依赖
# 更新时间: 2025-05-28
# 已验证兼容性: EfficientZero训练系统正常运行

# ============================================================================
# 核心必需依赖 (已验证兼容性)
# ============================================================================

# PyTorch 深度学习框架 - 核心依赖
torch>=2.0.0,<3.0.0

# NumPy 数值计算库 - 基础依赖
numpy>=1.20.0,<2.0.0

# Pandas 数据分析库
pandas>=1.3.0,<3.0.0

# Matplotlib 绘图库
matplotlib>=3.5.0,<4.0.0

# Seaborn 统计可视化
seaborn>=0.11.0,<1.0.0

# YAML配置文件处理
PyYAML>=6.0,<7.0.0

# 进度条库
tqdm>=4.62.0

# ============================================================================
# 安装说明
# ============================================================================

# 安装命令:
# pip install -r requirements-core.txt

# 验证安装:
# python test_efficient_zero_real.py

# ============================================================================
# 版本兼容性
# ============================================================================

# Python版本要求: >=3.8
# 操作系统: Windows 10+, Linux, macOS
# 已验证环境: Windows 11 + Python 3.13 + PyTorch 2.x

# ============================================================================
# 修复历史
# ============================================================================

# 2025-05-28: 修复EfficientZero训练系统PyTorch兼容性问题
# - 修复PyTorch Sequential参数错误
# - 修复布尔张量兼容性问题
# - 修复Unicode编码问题
# - 验证核心依赖兼容性
