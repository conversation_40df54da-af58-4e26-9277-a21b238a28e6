#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速启动脚本 - 最简单的IDE运行方式

🚀 一键启动斗地主AI训练
📝 只需在IDE中运行此文件即可开始训练

使用方法:
1. 在IDE中打开此文件
2. 点击运行按钮
3. 等待训练开始

默认设置:
- 游戏: 斗地主
- 算法: EfficientZero
- 设备: 自动检测GPU/CPU
- 配置: 使用默认配置
"""

import os
import sys

# 添加项目路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '../..'))
sys.path.insert(0, project_root)

def main():
    """快速启动主函数"""
    print("🎮 斗地主AI训练 - 快速启动")
    print("=" * 40)

    try:
        # 检测设备
        device = "cpu"
        try:
            import torch
            if torch.cuda.is_available():
                device = "cuda:0"
                print(f"✅ 使用GPU: {torch.cuda.get_device_name(0)}")
            else:
                print("ℹ️  使用CPU训练")
        except ImportError:
            print("ℹ️  使用CPU训练")

        print("🚀 启动优化训练...")
        print("📊 优化特性:")
        print("   ✨ MCTS模拟次数: 50→100")
        print("   📦 批次大小: 128→256")
        print("   🎯 学习率: 精细调整至0.0005")
        print("   🤝 农民协作权重: 0.7→0.8")
        print("   🏆 团队奖励权重: 0.8→0.9")
        print()

        # 导入并运行训练
        from cardgame_ai.algorithms.efficient_zero import train_efficient_zero

        # 优化配置
        config = {
            'device': device,
            'seed': 42,
            'algorithm': 'efficient_zero',
            'game': 'doudizhu',
            'training': {
                'epochs': 100,  # 快速启动使用较少epochs
                'batch_size': 256,  # 优化：从128提升至256
                'learning_rate': 0.0005,  # 优化：精细调整
                'num_simulations': 100,  # 优化：从50提升至100
                'save_frequency': 50,
                'eval_frequency': 25,
                'log_frequency': 5
            },
            'multi_agent': {
                'farmer_cooperation': {
                    'enabled': True,
                    'cooperation_weight': 0.8,  # 优化：从0.7提升
                    'team_reward_weight': 0.9   # 优化：从0.8提升
                }
            },
            'logging': {
                'level': 'INFO',
                'log_dir': 'logs'
            },
            'checkpoint': {
                'dir': 'models',
                'save_interval': 50
            }
        }

        # 开始训练
        result = train_efficient_zero('doudizhu', config)

        if result == 0:
            print("\n✅ 训练完成！")
        else:
            print(f"\n❌ 训练失败 (代码: {result})")

        return result

    except KeyboardInterrupt:
        print("\n⏹️  训练被中断")
        return 130
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
