#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cursor文件监控工具测试脚本

此脚本用于测试cursor_file_monitor.py的功能是否正常工作。
通过模拟文件操作并检查日志文件来验证监控是否有效。

注意：测试时需要先运行cursor_file_monitor.py
"""

import os
import time
import glob
import subprocess
import threading
import shutil
import unittest

class TestCursorMonitor(unittest.TestCase):
    """测试Cursor文件监控工具的功能"""
    
    @classmethod
    def setUpClass(cls):
        """测试开始前的准备工作"""
        # 创建测试目录
        cls.test_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'monitor_test_dir')
        os.makedirs(cls.test_dir, exist_ok=True)
        
        # 启动监控进程
        cls.monitor_process = None
        cls.start_monitor()
        
        # 等待监控程序初始化
        time.sleep(2)
    
    @classmethod
    def tearDownClass(cls):
        """测试结束后的清理工作"""
        # 终止监控进程
        if cls.monitor_process:
            cls.stop_monitor()
        
        # 清理测试目录
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
    
    @classmethod
    def start_monitor(cls):
        """启动监控程序"""
        monitor_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cursor_file_monitor.py')
        
        # 使用子进程启动监控程序
        cmd = [
            'python', 
            monitor_script, 
            '--path', cls.test_dir, 
            '--refresh-interval', '5'
        ]
        
        # 在Windows中隐藏窗口运行
        startupinfo = None
        if os.name == 'nt':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = 0  # 隐藏窗口
        
        cls.monitor_process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            startupinfo=startupinfo
        )
        
        print(f"监控进程已启动，PID: {cls.monitor_process.pid}")
    
    @classmethod
    def stop_monitor(cls):
        """停止监控程序"""
        if cls.monitor_process:
            cls.monitor_process.terminate()
            cls.monitor_process.wait(timeout=5)
            print("监控进程已终止")
    
    def get_latest_log_file(self):
        """获取最新的日志文件"""
        log_files = glob.glob('cursor_file_operations_*.csv')
        if not log_files:
            return None
        
        # 按修改时间排序，返回最新的文件
        return max(log_files, key=os.path.getmtime)
    
    def read_log_content(self):
        """读取日志文件内容"""
        log_file = self.get_latest_log_file()
        if not log_file:
            return []
        
        with open(log_file, 'r', encoding='utf-8') as f:
            # 跳过标题行
            lines = f.readlines()[1:]
            return [line.strip() for line in lines]
    
    def simulate_cursor_activity(self, file_path, operation):
        """
        模拟Cursor.exe的文件操作
        
        Args:
            file_path: 要操作的文件路径
            operation: 操作类型（'create', 'modify', 'delete', 'move'）
        """
        # 创建一个模拟Cursor.exe进程的脚本
        script_content = f"""
import os
import time

# 执行{operation}操作
if '{operation}' == 'create':
    with open('{file_path}', 'w') as f:
        f.write('测试内容')
elif '{operation}' == 'modify':
    with open('{file_path}', 'a') as f:
        f.write('\\n修改内容')
elif '{operation}' == 'delete':
    os.remove('{file_path}')
elif '{operation}' == 'move':
    os.rename('{file_path}', '{file_path}.moved')

# 停留一段时间以确保操作被监控程序检测到
time.sleep(1)
"""
        
        # 将脚本写入临时文件
        temp_script = os.path.join(self.test_dir, 'temp_simulate.py')
        with open(temp_script, 'w') as f:
            f.write(script_content)
        
        # 修改进程名称为"cursor"以触发监控
        # 注意：实际上很难修改进程名称，这里我们可以通过环境变量来标记
        env = os.environ.copy()
        env['PROCESS_NAME'] = 'cursor'
        
        # 执行模拟脚本
        process = subprocess.Popen(
            ['python', temp_script],
            env=env
        )
        process.wait()
        
        # 删除临时脚本
        if os.path.exists(temp_script):
            os.remove(temp_script)
        
        # 等待一段时间以确保监控程序记录了操作
        time.sleep(2)
    
    def test_file_create(self):
        """测试文件创建监控"""
        test_file = os.path.join(self.test_dir, 'test_create.txt')
        
        # 确保文件不存在
        if os.path.exists(test_file):
            os.remove(test_file)
        
        # 模拟文件创建
        self.simulate_cursor_activity(test_file, 'create')
        
        # 检查日志
        logs = self.read_log_content()
        create_logs = [log for log in logs if test_file in log and '创建' in log]
        
        self.assertTrue(len(create_logs) > 0, f"未能监控到文件创建操作: {test_file}")
        print(f"测试文件创建监控成功: {create_logs[0]}")
    
    def test_file_modify(self):
        """测试文件修改监控"""
        test_file = os.path.join(self.test_dir, 'test_modify.txt')
        
        # 确保文件存在
        with open(test_file, 'w') as f:
            f.write('初始内容')
        
        # 模拟文件修改
        self.simulate_cursor_activity(test_file, 'modify')
        
        # 检查日志
        logs = self.read_log_content()
        modify_logs = [log for log in logs if test_file in log and '修改' in log]
        
        self.assertTrue(len(modify_logs) > 0, f"未能监控到文件修改操作: {test_file}")
        print(f"测试文件修改监控成功: {modify_logs[0]}")
    
    def test_file_delete(self):
        """测试文件删除监控"""
        test_file = os.path.join(self.test_dir, 'test_delete.txt')
        
        # 确保文件存在
        with open(test_file, 'w') as f:
            f.write('要删除的内容')
        
        # 模拟文件删除
        self.simulate_cursor_activity(test_file, 'delete')
        
        # 检查日志
        logs = self.read_log_content()
        delete_logs = [log for log in logs if test_file in log and '删除' in log]
        
        self.assertTrue(len(delete_logs) > 0, f"未能监控到文件删除操作: {test_file}")
        print(f"测试文件删除监控成功: {delete_logs[0]}")
    
    def test_file_move(self):
        """测试文件移动监控"""
        test_file = os.path.join(self.test_dir, 'test_move.txt')
        
        # 确保文件存在
        with open(test_file, 'w') as f:
            f.write('要移动的内容')
        
        # 模拟文件移动
        self.simulate_cursor_activity(test_file, 'move')
        
        # 检查日志
        logs = self.read_log_content()
        move_logs = [log for log in logs if test_file in log and '移动' in log]
        
        self.assertTrue(len(move_logs) > 0, f"未能监控到文件移动操作: {test_file}")
        print(f"测试文件移动监控成功: {move_logs[0]}")


if __name__ == '__main__':
    unittest.main() 