#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查找和清除 Cursor/VS Code 插件 Augment.vscode-augment 的配置文件和缓存

该脚本会扫描 Cursor 和 VS Code 的常见配置位置，列出与 Augment.vscode-augment 插件相关的文件和文件夹。
脚本提供交互式选择，允许用户选择是否清除找到的文件和文件夹。

作者: AI Assistant
版本: 1.2
创建日期: 2024-07-30
"""

import os
import json
import platform
import sys
import shutil
import re
from pathlib import Path
from typing import List, Dict, Any, Optional

# --- 配置区域 ---
PLUGIN_ID = "Augment.vscode-augment"  # 要查找的插件ID
PLUGIN_PUBLISHER = PLUGIN_ID.split('.')[0].lower()  # 发布者名称 (augment)
PLUGIN_NAME = PLUGIN_ID.split('.')[1].lower()  # 插件名称 (vscode-augment)y


# --- 辅助函数 ---
def print_color(text: str, color: str = None):
    """打印彩色文本 (尽量在不同操作系统上提供一致体验)"""
    colors = {
        "reset": "\033[0m",
        "yellow": "\033[93m",
        "green": "\033[92m",
        "cyan": "\033[96m",
        "red": "\033[91m",
        "gray": "\033[90m",
    }
    
    if color and color in colors and platform.system() != "Windows":
        print(f"{colors[color]}{text}{colors['reset']}")
    else:
        print(text)


def get_editor_paths() -> Dict[str, str]:
    """获取 Cursor 和 VS Code 相关的路径"""
    system = platform.system()
    paths = {}
    
    if system == "Windows":
        # Windows路径
        appdata = os.environ.get("APPDATA", "")
        userprofile = os.environ.get("USERPROFILE", "")
        localappdata = os.environ.get("LOCALAPPDATA", "")
        
        # Cursor 路径
        paths["cursor_user_settings"] = os.path.join(appdata, "Cursor", "User", "settings.json")
        paths["cursor_global_storage"] = os.path.join(appdata, "Cursor", "User", "globalStorage")
        paths["cursor_extensions"] = os.path.join(userprofile, ".cursor", "extensions")
        
        # VS Code 路径 (作为备选)
        paths["vscode_user_settings"] = os.path.join(appdata, "Code", "User", "settings.json")
        paths["vscode_global_storage"] = os.path.join(appdata, "Code", "User", "globalStorage")
        paths["vscode_extensions"] = os.path.join(userprofile, ".vscode", "extensions")
        
        # Cursor 可能的额外位置
        paths["cursor_user_data"] = os.path.join(localappdata, "Cursor", "User")
        paths["cursor_roaming_data"] = os.path.join(appdata, "Cursor")
        
    elif system == "Darwin":
        # macOS路径
        home = os.path.expanduser("~")
        
        # Cursor 路径
        paths["cursor_user_settings"] = os.path.join(home, "Library", "Application Support", "Cursor", "User", "settings.json")
        paths["cursor_global_storage"] = os.path.join(home, "Library", "Application Support", "Cursor", "User", "globalStorage")
        paths["cursor_extensions"] = os.path.join(home, ".cursor", "extensions")
        
        # VS Code 路径 (作为备选)
        paths["vscode_user_settings"] = os.path.join(home, "Library", "Application Support", "Code", "User", "settings.json")
        paths["vscode_global_storage"] = os.path.join(home, "Library", "Application Support", "Code", "User", "globalStorage")
        paths["vscode_extensions"] = os.path.join(home, ".vscode", "extensions")
        
    else:
        # Linux路径
        home = os.path.expanduser("~")
        
        # Cursor 路径
        paths["cursor_user_settings"] = os.path.join(home, ".config", "Cursor", "User", "settings.json")
        paths["cursor_global_storage"] = os.path.join(home, ".config", "Cursor", "User", "globalStorage")
        paths["cursor_extensions"] = os.path.join(home, ".cursor", "extensions")
        
        # VS Code 路径 (作为备选)
        paths["vscode_user_settings"] = os.path.join(home, ".config", "Code", "User", "settings.json")
        paths["vscode_global_storage"] = os.path.join(home, ".config", "Code", "User", "globalStorage")
        paths["vscode_extensions"] = os.path.join(home, ".vscode", "extensions")
    
    return paths


def load_json_file(file_path: str) -> Optional[Dict[str, Any]]:
    """加载JSON文件，返回解析后的内容或None"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print_color(f"  [!] 读取或解析文件出错: {e}", "red")
    return None


def save_json_file(file_path: str, data: Dict[str, Any]) -> bool:
    """保存JSON文件，成功返回True，失败返回False"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print_color(f"  [!] 保存文件出错: {e}", "red")
        return False


def find_extension_dirs(extensions_path: str, plugin_id: str) -> List[str]:
    """查找扩展目录中与插件ID匹配的目录"""
    if not os.path.exists(extensions_path):
        return []
    
    publisher, name = plugin_id.split('.')
    pattern_start = f"{publisher}.{name}-"
    
    matching_dirs = []
    try:
        for item in os.listdir(extensions_path):
            item_path = os.path.join(extensions_path, item)
            if os.path.isdir(item_path) and item.lower().startswith(pattern_start.lower()):
                matching_dirs.append(item_path)
    except Exception as e:
        print_color(f"  [!] 读取扩展目录出错: {e}", "red")
        
    return matching_dirs


def find_matching_files(base_dir: str, name_patterns: List[str], max_depth: int = 3) -> List[str]:
    """在目录中查找与指定模式匹配的文件 (使用部分匹配)"""
    if not os.path.exists(base_dir) or max_depth <= 0:
        return []
    
    result = []
    patterns_lower = [p.lower() for p in name_patterns]
    
    try:
        for item in os.listdir(base_dir):
            full_path = os.path.join(base_dir, item)
            item_lower = item.lower()
            
            # 检查是否匹配任何模式
            if any(pattern in item_lower for pattern in patterns_lower):
                result.append(full_path)
            
            # 如果是目录，递归搜索
            if os.path.isdir(full_path) and max_depth > 1:
                subdir_results = find_matching_files(full_path, name_patterns, max_depth - 1)
                result.extend(subdir_results)
    except Exception as e:
        print_color(f"  [!] 搜索目录时出错: {e}", "red")
    
    return result


def remove_settings_entries(settings_path: str, key_patterns: List[str]) -> bool:
    """从settings.json文件中移除匹配指定模式的条目"""
    # 加载设置文件
    settings_data = load_json_file(settings_path)
    if not settings_data:
        return False
    
    # 记录原始键数量
    original_key_count = len(settings_data)
    removed_keys = []
    
    # 移除匹配的键
    for key in list(settings_data.keys()):
        if any(pattern.lower() in key.lower() for pattern in key_patterns):
            removed_keys.append(key)
            del settings_data[key]
    
    # 如果有键被移除，保存文件
    if removed_keys:
        if save_json_file(settings_path, settings_data):
            print_color(f"  [✓] 成功从 {settings_path} 中移除 {len(removed_keys)} 个设置项:", "green")
            for key in removed_keys:
                print(f"    - {key}")
            return True
        else:
            print_color(f"  [!] 保存设置文件失败: {settings_path}", "red")
            return False
    else:
        print_color(f"  [i] 在设置文件中未找到匹配的项: {settings_path}", "gray")
        return False


def safe_remove(path: str) -> bool:
    """安全删除文件或文件夹"""
    try:
        if not os.path.exists(path):
            print_color(f"  [i] 路径不存在: {path}", "gray")
            return False
        
        if os.path.isfile(path):
            os.remove(path)
            print_color(f"  [✓] 成功删除文件: {path}", "green")
        else:
            shutil.rmtree(path)
            print_color(f"  [✓] 成功删除文件夹: {path}", "green")
        return True
    except Exception as e:
        print_color(f"  [!] 删除失败: {path} - {e}", "red")
        return False


def get_user_confirmation(prompt: str, default: str = 'n') -> bool:
    """获取用户确认"""
    valid = {"y": True, "yes": True, "n": False, "no": False}
    if default.lower() not in ['y', 'n']:
        default = 'n'
    
    prompt_with_options = f"{prompt} [{'Y/n' if default.lower() == 'y' else 'y/N'}]: "
    
    while True:
        sys.stdout.write(prompt_with_options)
        choice = input().lower()
        
        if choice == '':
            return valid[default.lower()]
        elif choice in valid:
            return valid[choice]
        else:
            sys.stdout.write("请输入 'y' 或 'n'。\n")


def clean_items(found_items: List[Dict[str, str]]) -> bool:
    """清理找到的项目"""
    if not found_items:
        print_color("没有找到需要清理的项目。", "yellow")
        return False
    
    print_color("\n开始清理操作...", "yellow")
    success_count = 0
    failed_count = 0
    
    # 对找到的项目进行分类处理
    for item in found_items:
        item_type = item["Type"]
        path = item["Path"]
        
        print_color(f"\n正在处理: {item_type} - {path}", "cyan")
        
        # 根据不同类型的项目执行不同的清理操作
        if "Settings" in item_type:
            # 处理设置文件
            if remove_settings_entries(path, [PLUGIN_PUBLISHER, PLUGIN_ID]):
                success_count += 1
            else:
                failed_count += 1
        else:
            # 处理文件或文件夹
            if safe_remove(path):
                success_count += 1
            else:
                failed_count += 1
    
    # 打印清理结果摘要
    print_color("\n--- 清理操作完成 ---", "yellow")
    print(f"成功: {success_count}, 失败: {failed_count}")
    
    return success_count > 0


# --- 主函数 ---
def main():
    """主函数"""
    found_items = []
    
    print_color(f"开始查找插件 '{PLUGIN_ID}' 的配置文件和缓存...", "yellow")
    print_color("主要针对 Cursor 编辑器, 同时也会检查 VS Code 位置", "yellow")
    
    # 获取 Cursor 和 VS Code 路径
    editor_paths = get_editor_paths()
    
    # 1. 用户设置 (settings.json)
    print_color("\n[1] 正在检查用户设置 (settings.json)...", "cyan")
    
    # 1.1 检查 Cursor 设置
    settings_path = editor_paths["cursor_user_settings"]
    print_color(f"  检查 Cursor 设置: {settings_path}", "cyan")
    
    if os.path.exists(settings_path):
        settings_data = load_json_file(settings_path)
        if settings_data:
            # 查找与插件相关的设置
            plugin_settings = []
            for key in settings_data.keys():
                if PLUGIN_ID.lower() in key.lower() or PLUGIN_PUBLISHER.lower() in key.lower():
                    plugin_settings.append(key)
            
            if plugin_settings:
                print_color("  [-] 在 Cursor settings.json 中找到以下相关配置:", "green")
                for setting in plugin_settings:
                    print(f"    - {setting}")
                found_items.append({
                    "Type": "Cursor User Settings",
                    "Path": settings_path,
                    "Details": ", ".join(plugin_settings)
                })
            else:
                print_color(f"  [+] Cursor settings.json 中未直接找到与 '{PLUGIN_ID}' 相关的顶级键。", "gray")
    else:
        print_color(f"  [!] 未找到 Cursor settings.json: {settings_path}", "red")
    
    # 1.2 检查 VS Code 设置 (作为备选)
    settings_path = editor_paths["vscode_user_settings"]
    print_color(f"  检查 VS Code 设置: {settings_path}", "cyan")
    
    if os.path.exists(settings_path):
        settings_data = load_json_file(settings_path)
        if settings_data:
            # 查找与插件相关的设置
            plugin_settings = []
            for key in settings_data.keys():
                if PLUGIN_ID.lower() in key.lower() or PLUGIN_PUBLISHER.lower() in key.lower():
                    plugin_settings.append(key)
            
            if plugin_settings:
                print_color("  [-] 在 VS Code settings.json 中找到以下相关配置:", "green")
                for setting in plugin_settings:
                    print(f"    - {setting}")
                found_items.append({
                    "Type": "VS Code User Settings",
                    "Path": settings_path,
                    "Details": ", ".join(plugin_settings)
                })
            else:
                print_color(f"  [+] VS Code settings.json 中未直接找到与 '{PLUGIN_ID}' 相关的顶级键。", "gray")
    else:
        print_color(f"  [!] 未找到 VS Code settings.json: {settings_path}", "red")
    
    # 2. 全局存储 (Global Storage)
    print_color("\n[2] 正在检查全局存储...", "cyan")
    
    # 2.1 检查 Cursor 全局存储
    global_storage_path = editor_paths["cursor_global_storage"]
    plugin_global_storage_path = os.path.join(global_storage_path, PLUGIN_ID.lower())
    print_color(f"  检查 Cursor 全局存储: {plugin_global_storage_path}", "cyan")
    
    if os.path.exists(plugin_global_storage_path):
        print_color("  [-] 找到插件的 Cursor 全局存储文件夹:", "green")
        print(f"    {plugin_global_storage_path}")
        found_items.append({
            "Type": "Cursor Global Storage",
            "Path": plugin_global_storage_path,
            "Details": "建议手动检查并删除此文件夹中的内容。"
        })
    else:
        print_color(f"  [+] 未找到插件在 Cursor 中的全局存储文件夹", "gray")
    
    # 2.2 检查 VS Code 全局存储
    global_storage_path = editor_paths["vscode_global_storage"]
    plugin_global_storage_path = os.path.join(global_storage_path, PLUGIN_ID.lower())
    print_color(f"  检查 VS Code 全局存储: {plugin_global_storage_path}", "cyan")
    
    if os.path.exists(plugin_global_storage_path):
        print_color("  [-] 找到插件的 VS Code 全局存储文件夹:", "green")
        print(f"    {plugin_global_storage_path}")
        found_items.append({
            "Type": "VS Code Global Storage",
            "Path": plugin_global_storage_path,
            "Details": "建议手动检查并删除此文件夹中的内容。"
        })
    else:
        print_color(f"  [+] 未找到插件在 VS Code 中的全局存储文件夹", "gray")
    
    # 3. 扩展安装目录
    print_color("\n[3] 正在检查扩展安装目录...", "cyan")
    
    # 3.1 检查 Cursor 扩展目录
    extensions_path = editor_paths["cursor_extensions"]
    print_color(f"  检查 Cursor 扩展目录: {extensions_path}", "cyan")
    extension_dirs = find_extension_dirs(extensions_path, PLUGIN_ID)
    
    if extension_dirs:
        print_color("  [-] 找到以下可能的 Cursor 插件安装目录:", "green")
        for dir_path in extension_dirs:
            print(f"    {dir_path}")
            found_items.append({
                "Type": "Cursor Extension Directory",
                "Path": dir_path,
                "Details": "这是插件在 Cursor 中的安装位置，建议重新安装前手动删除。"
            })
    else:
        print_color(f"  [+] 未在 Cursor 扩展目录中找到相关插件", "gray")
    
    # 3.2 检查 VS Code 扩展目录
    extensions_path = editor_paths["vscode_extensions"]
    print_color(f"  检查 VS Code 扩展目录: {extensions_path}", "cyan")
    extension_dirs = find_extension_dirs(extensions_path, PLUGIN_ID)
    
    if extension_dirs:
        print_color("  [-] 找到以下可能的 VS Code 插件安装目录:", "green")
        for dir_path in extension_dirs:
            print(f"    {dir_path}")
            found_items.append({
                "Type": "VS Code Extension Directory",
                "Path": dir_path,
                "Details": "这是插件在 VS Code 中的安装位置，建议重新安装前手动删除。"
            })
    else:
        print_color(f"  [+] 未在 VS Code 扩展目录中找到相关插件", "gray")
    
    # 4. 搜索其他可能的位置
    print_color("\n[4] 正在搜索其他可能的位置...", "cyan")
    
    # 4.1 检查 Cursor 用户数据目录
    if "cursor_user_data" in editor_paths:
        user_data_path = editor_paths["cursor_user_data"]
        print_color(f"  检查 Cursor 用户数据目录: {user_data_path}", "cyan")
        
        if os.path.exists(user_data_path):
            # 搜索包含 augment 关键字的文件和文件夹
            search_patterns = ["augment", "vscode-augment"]
            matching_files = find_matching_files(user_data_path, search_patterns)
            
            if matching_files:
                print_color("  [-] 在 Cursor 用户数据目录中找到以下可能相关的文件:", "green")
                for file_path in matching_files:
                    print(f"    {file_path}")
                    found_items.append({
                        "Type": "Cursor User Data File",
                        "Path": file_path,
                        "Details": "可能与插件相关的配置文件，请检查内容后考虑删除。"
                    })
            else:
                print_color(f"  [+] 未在 Cursor 用户数据目录中找到相关文件", "gray")
        else:
            print_color(f"  [!] 未找到 Cursor 用户数据目录: {user_data_path}", "red")
    
    # 4.2 检查 Cursor 漫游数据目录
    if "cursor_roaming_data" in editor_paths:
        roaming_data_path = editor_paths["cursor_roaming_data"]
        print_color(f"  检查 Cursor 漫游数据目录: {roaming_data_path}", "cyan")
        
        if os.path.exists(roaming_data_path):
            # 搜索包含 augment 关键字的文件和文件夹
            search_patterns = ["augment", "vscode-augment"]
            matching_files = find_matching_files(roaming_data_path, search_patterns)
            
            if matching_files:
                print_color("  [-] 在 Cursor 漫游数据目录中找到以下可能相关的文件:", "green")
                for file_path in matching_files:
                    print(f"    {file_path}")
                    found_items.append({
                        "Type": "Cursor Roaming Data File",
                        "Path": file_path,
                        "Details": "可能与插件相关的配置文件，请检查内容后考虑删除。"
                    })
            else:
                print_color(f"  [+] 未在 Cursor 漫游数据目录中找到相关文件", "gray")
        else:
            print_color(f"  [!] 未找到 Cursor 漫游数据目录: {roaming_data_path}", "red")
    
    # --- 总结 ---
    print_color("\n--- 查找完毕 ---", "yellow")
    if found_items:
        print_color(f"\n已找到以下与插件 '{PLUGIN_ID}' 相关的文件和文件夹:", "green")
        
        # 打印表格
        print("\n{:<25} {:<70} {:<45}".format("类型", "路径", "详情"))
        print("-" * 140)
        for item in found_items:
            path_display = item["Path"]
            if len(path_display) > 68:
                path_display = "..." + path_display[-65:]
            print("{:<25} {:<70} {:<45}".format(
                item["Type"], 
                path_display,
                item["Details"][:42] + "..." if len(item["Details"]) > 42 else item["Details"]
            ))
        
        print_color("\n重要提示:", "yellow")
        print("  - 此脚本可以帮助您自动删除找到的文件和文件夹。")
        print("  - 在继续之前，请确保 Cursor 和 VS Code 已完全关闭。")
        print("  - 删除全局存储和扩展目录通常是解决顽固配置问题的最有效方法。")
        print("  - 建议在删除前先备份重要文件。")
        
        # 交互式选择是否清除
        if get_user_confirmation("\n是否希望自动清除上述文件和文件夹？(此操作不可撤销)"):
            clean_items(found_items)
            print_color("\n清理操作已完成。", "green")
            print_color("建议重新启动 Cursor 和 VS Code 以应用更改。", "green")
        else:
            print_color("\n您选择了不清除文件。如果需要手动清理，请参考以上列表。", "yellow")
    else:
        print_color(f"未找到与插件 '{PLUGIN_ID}' 明确相关的已知配置文件或缓存位置。", "yellow")
        print("如果问题依旧存在，可能需要检查插件是否有其他非标准的配置存储方式，或直接联系 Cursor 支持团队。")
    
    print_color("\n脚本执行结束。", "yellow")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_color("\n\n操作已取消。", "yellow")
    except Exception as e:
        print_color(f"\n\n执行脚本时出错: {e}", "red")
        import traceback
        traceback.print_exc()
    
    # 等待用户按键退出
    print("\n按任意键退出...")
    input() 