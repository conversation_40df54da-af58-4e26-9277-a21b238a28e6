"""
EfficientZero算法实现

该模块实现了EfficientZero算法，它是MuZero的改进版本，通过自监督表示学习、
值前缀预测和自适应数据重用等技术，显著提高了样本效率。

参考论文：
- "Mastering Atari Games with Limited Data" (<PERSON> et al., 2021)
"""

import os
import copy
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
import time
import glob
import random
import traceback
from typing import Tuple, Dict, Any, Union, List, Optional, Callable
from torch.cuda.amp import autocast, GradScaler
from torch.utils.data import Dataset, DataLoader

from .muzero import MuZero, MuZeroModel
from .simsiam_loss import SelfSupervisedModule
from .continual_learning import EWC  # 导入EWC算法
from .distributional_value_head import DistributionalValueHead  # 导入分布式价值头
from cardgame_ai.core.base import State, Action, Experience, Batch
from cardgame_ai.utils.opponent_distribution_switcher import OpponentDistributionSwitcher
from cardgame_ai.algorithms.risk_sensitive_rl import CVaRCalculator  # 导入CVaR计算器
from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector
from cardgame_ai.algorithms.components.dynamic_budget_allocator import DynamicBudgetAllocator
from cardgame_ai.games.doudizhu.state import GamePhase  # 添加阶段枚举用于日志区分
from cardgame_ai.games.doudizhu.action import BidAction, GrabAction  # 添加动作类型用于日志区分

# 配置日志
logger = logging.getLogger(__name__)


class EfficientZeroModel(MuZeroModel):
    """
    EfficientZero模型

    扩展MuZero模型，添加自监督表示学习组件和信念状态支持。
    """

    def representation(self, observations: torch.Tensor) -> torch.Tensor:
        """
        将观察转换为隐藏状态

        Args:
            observations (torch.Tensor): 观察数据

        Returns:
            torch.Tensor: 隐藏状态
        """
        return self.represent(observations)

    def predict_with_belief(self, hidden_state: torch.Tensor, belief_state: Any) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        使用信念状态进行预测

        Args:
            hidden_state (torch.Tensor): 隐藏状态
            belief_state (Any): 信念状态，可以是BeliefState或JointBeliefState

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        # 如果有专门的信念处理网络，则使用它
        if hasattr(self, 'belief_processor'):
            # 将信念状态转换为张量
            belief_tensor = self._convert_belief_to_tensor(belief_state)

            # 使用注意力机制处理信念状态
            if hasattr(self, 'belief_attention'):
                # 准备query: 从隐藏状态投影
                query = self.belief_query_proj(hidden_state).unsqueeze(1)  # [B, 1, head_dim*num_heads]

                # 准备key和value: 从信念状态投影
                key = self.belief_key_proj(belief_tensor).unsqueeze(0).expand(hidden_state.size(0), -1, -1)  # [B, belief_len, head_dim*num_heads]
                value = self.belief_value_proj(belief_tensor).unsqueeze(0).expand(hidden_state.size(0), -1, -1)  # [B, belief_len, head_dim*num_heads]

                # 应用注意力
                processed_belief, attention_weights = self.belief_attention(
                    query, key, value
                )

                # 摊平注意力输出
                processed_belief = processed_belief.view(hidden_state.size(0), -1)

                # 可视化注意力权重（调试用）
                # self._visualize_attention(attention_weights, belief_state)
            else:
                # 使用标准信念处理器
                processed_belief = self.belief_processor(belief_tensor)

            # 使用融合门控机制
            if hasattr(self, 'fusion_gate'):
                # 计算门控值 (0-1之间的值，决定信念的影响程度)
                gate_value = torch.sigmoid(self.fusion_gate(torch.cat([hidden_state, processed_belief], dim=1)))

                # 应用门控
                processed_belief = processed_belief * gate_value

            # 将隐藏状态和处理后的信念状态连接起来
            combined_state = torch.cat([hidden_state, processed_belief], dim=1)

            # 使用组合处理器处理组合状态
            if hasattr(self, 'combined_processor'):
                if hasattr(self, 'residual_belief') and self.residual_belief:
                    # 使用残差连接
                    residual = hidden_state
                    combined_features = self.combined_processor(combined_state)
                    combined_features = combined_features + residual
                else:
                    combined_features = self.combined_processor(combined_state)
            else:
                combined_features = combined_state

            # 使用预测网络预测策略和价值
            if self.use_distributional_value:
                # 如果使用分布式价值头
                if hasattr(self, 'prediction_network_with_belief_policy') and hasattr(self, 'prediction_network_with_belief_value'):
                    # 使用分离的策略头和价值头
                    policy_logits = self.prediction_network_with_belief_policy(combined_features)
                    value_logits = self.prediction_network_with_belief_value(combined_features)

                    # 计算风险敏感价值
                    value = self.prediction_network_with_belief_value.compute_risk_sensitive_value(
                        value_logits,
                        alpha=self.risk_alpha,
                        beta=self.risk_beta
                    )

                    return policy_logits, value
                else:
                    # 如果没有分离的头，则使用标准预测
                    return self.predict(hidden_state)
            else:
                # 使用标准预测网络
                if hasattr(self, 'prediction_network_with_belief'):
                    return self.prediction_network_with_belief(combined_features)
                else:
                    return self.predict(hidden_state)
        else:
            # 如果没有专门的信念处理网络，则使用标准预测
            return self.predict(hidden_state)

    def _convert_belief_to_tensor(self, belief_state: Any) -> torch.Tensor:
        """
        将信念状态转换为张量

        Args:
            belief_state (Any): 信念状态对象，可以是BeliefState或JointBeliefState

        Returns:
            torch.Tensor: 信念状态张量
        """
        # 处理JointBeliefState
        if hasattr(belief_state, 'individual_beliefs') and hasattr(belief_state, 'joint_factors'):
            # 这是一个JointBeliefState对象
            player_beliefs = []

            # 获取每个玩家的信念状态
            for player_id in belief_state.player_ids:
                individual_belief = belief_state.get_individual_belief(player_id)
                if individual_belief:
                    # 获取概率分布
                    card_probs = list(individual_belief.card_probabilities.values())
                    player_beliefs.append(card_probs)

            # 如果没有信念状态，返回空张量
            if not player_beliefs:
                return torch.zeros(1, 1).to(self.device)

            # 连接所有玩家的信念状态
            all_beliefs = np.concatenate(player_beliefs)

            # 添加联合因子信息（可选，取平均值作为简化）
            joint_info = []
            for factor_key, factor_matrix in belief_state.joint_factors.items():
                # 简化：使用因子矩阵的平均值和最大值
                joint_info.extend([
                    np.mean(factor_matrix),
                    np.max(factor_matrix)
                ])

            # 将所有信息连接起来
            if joint_info:
                all_info = np.concatenate([all_beliefs, joint_info])
            else:
                all_info = all_beliefs

            # 转换为张量
            belief_tensor = torch.tensor(all_info, dtype=torch.float32).to(self.device)

            # 添加批次维度
            if belief_tensor.dim() == 1:
                belief_tensor = belief_tensor.unsqueeze(0)

            return belief_tensor

        # 处理普通BeliefState
        elif hasattr(belief_state, 'card_probabilities'):
            card_probs = list(belief_state.card_probabilities.values())

            # 添加信念状态的元信息（可选）
            if hasattr(belief_state, 'confidence') and hasattr(belief_state, 'source'):
                # 添加置信度和来源信息
                meta_info = [
                    belief_state.confidence,
                    float(belief_state.source.value) / 5.0  # 归一化来源枚举
                ]

                # 如果有估计手牌长度，也添加它
                if hasattr(belief_state, 'estimated_hand_length') and belief_state.estimated_hand_length is not None:
                    meta_info.append(belief_state.estimated_hand_length / 20.0)  # 假设最大手牌为20张

                # 连接概率和元信息
                all_info = np.concatenate([card_probs, meta_info])
                belief_tensor = torch.tensor(all_info, dtype=torch.float32).to(self.device)
            else:
                belief_tensor = torch.tensor(card_probs, dtype=torch.float32).to(self.device)

            # 添加批次维度
            if belief_tensor.dim() == 1:
                belief_tensor = belief_tensor.unsqueeze(0)

            return belief_tensor
        else:
            # 如果没有概率分布，则返回空张量
            return torch.zeros(1, 1).to(self.device)

    def __init__(
        self,
        observation_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        projection_dim: int = 256,
        prediction_dim: int = 128,
        value_prefix_length: int = 5,
        belief_dim: int = 54,  # 默认为标准扑克牌数量
        use_belief_state: bool = False,
        use_distributional_value: bool = False,  # 是否使用分布式价值头
        value_support_size: int = 601,  # 价值分布支持大小
        value_min: float = -300,  # 价值范围最小值
        value_max: float = 300,  # 价值范围最大值
        risk_alpha: float = 0.05,  # CVaR的置信水平
        risk_beta: float = 0.1,  # 风险厌恶系数
        use_belief_attention: bool = True,  # 是否使用注意力机制处理信念
        belief_attention_heads: int = 4,  # 注意力头数
        use_residual_belief: bool = True,  # 是否使用残差连接
        use_gating_mechanism: bool = True,  # 是否使用门控机制
        device: str = None
    ):
        """
        初始化EfficientZero模型

        Args:
            observation_shape (Tuple[int, ...]): 观察空间形状
            action_shape (Tuple[int, ...]): 动作空间形状
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            state_dim (int, optional): 隐藏状态维度. Defaults to 64.
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
            projection_dim (int, optional): 投影维度. Defaults to 256.
            prediction_dim (int, optional): 预测网络隐藏层维度. Defaults to 128.
            value_prefix_length (int, optional): 值前缀长度. Defaults to 5.
            belief_dim (int, optional): 信念状态维度. Defaults to 54.
            use_belief_state (bool, optional): 是否使用信念状态. Defaults to False.
            use_distributional_value (bool, optional): 是否使用分布式价值头. Defaults to False.
            value_support_size (int, optional): 价值分布支持大小. Defaults to 601.
            value_min (float, optional): 价值范围最小值. Defaults to -300.
            value_max (float, optional): 价值范围最大值. Defaults to 300.
            risk_alpha (float, optional): CVaR的置信水平. Defaults to 0.05.
            risk_beta (float, optional): 风险厌恶系数. Defaults to 0.1.
            use_belief_attention (bool, optional): 是否使用注意力机制处理信念. Defaults to True.
            belief_attention_heads (int, optional): 注意力头数. Defaults to 4.
            use_residual_belief (bool, optional): 是否使用残差连接. Defaults to True.
            use_gating_mechanism (bool, optional): 是否使用门控机制. Defaults to True.
            device (str, optional): 计算设备. Defaults to None.
        """
        # 初始化父类
        super(EfficientZeroModel, self).__init__(
            observation_shape=observation_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            device=device
        )

        # 添加自监督学习模块
        self.self_supervised_module = SelfSupervisedModule(
            state_dim=state_dim,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim
        ).to(self.device)

        # 值前缀长度
        self.value_prefix_length = value_prefix_length

        # 信念状态相关设置
        self.use_belief_state = use_belief_state
        self.belief_dim = belief_dim
        self.residual_belief = use_residual_belief

        # 分布式价值头相关设置
        self.use_distributional_value = use_distributional_value
        self.value_support_size = value_support_size
        self.value_min = value_min
        self.value_max = value_max
        self.risk_alpha = risk_alpha
        self.risk_beta = risk_beta

        # 如果使用分布式价值头，则替换预测网络的价值头
        if use_distributional_value:
            # 创建分布式价值头
            self.distributional_value_head = DistributionalValueHead(
                input_dim=state_dim,
                hidden_dim=hidden_dim // 2,
                value_support_size=value_support_size,
                value_min=value_min,
                value_max=value_max,
                device=self.device
            ).to(self.device)

            # 修改预测网络，分离策略头和价值头
            # 策略头保持不变
            self.policy_head = nn.Sequential(
                nn.Linear(state_dim, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, action_shape[0])
            ).to(self.device)

            # 创建CVaR计算器
            self.cvar_calculator = CVaRCalculator(
                alpha=risk_alpha,
                beta=risk_beta,
                adaptive_beta=True,
                beta_update_rate=0.001
            )

        # 如果使用信念状态，则添加信念处理网络
        if use_belief_state:
            # 信念状态处理器
            self.belief_processor = nn.Sequential(
                nn.Linear(belief_dim, hidden_dim // 2),
                nn.ReLU(),
                nn.Linear(hidden_dim // 2, hidden_dim // 4)
            ).to(self.device)

            # 组合处理器
            self.combined_processor = nn.Sequential(
                nn.Linear(state_dim + hidden_dim // 4, hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, state_dim)
            ).to(self.device)

            # 修改预测网络以支持信念状态
            if use_distributional_value:
                # 如果同时使用信念状态和分布式价值头
                self.prediction_network_with_belief_policy = nn.Sequential(
                    nn.Linear(state_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, action_shape[0])
                ).to(self.device)

                # 分布式价值头
                self.prediction_network_with_belief_value = DistributionalValueHead(
                    input_dim=state_dim,
                    hidden_dim=hidden_dim // 2,
                    value_support_size=value_support_size,
                    value_min=value_min,
                    value_max=value_max,
                    device=self.device
                ).to(self.device)
            else:
                # 使用标准预测网络
                self.prediction_network_with_belief = nn.Sequential(
                    nn.Linear(state_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, action_shape[0] + 1)  # 输出动作概率和价值
                ).to(self.device)

        # 创建自定义的EfficientZero动态网络，支持两个参数的forward调用
        class EfficientZeroDynamicsNetwork(nn.Module):
            def __init__(self, state_dim, action_shape, hidden_dim, value_prefix_length, device):
                super().__init__()
                self.state_dim = state_dim
                self.value_prefix_length = value_prefix_length
                self.network = nn.Sequential(
                    nn.Linear(state_dim + np.prod(action_shape).item(), hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, state_dim + value_prefix_length)
                )
                self.to(device)

            def forward(self, state, action):
                """
                前向传播，接受两个参数：状态和动作

                Args:
                    state (torch.Tensor): 当前状态
                    action (torch.Tensor): 执行的动作

                Returns:
                    Tuple[torch.Tensor, torch.Tensor]: 下一个状态和值前缀
                """
                # 确保action的维度与state匹配
                if action.dim() == 1:
                    action = action.unsqueeze(1)  # 将形状从[batch_size]变为[batch_size, 1]

                x = torch.cat([state, action], dim=1)
                dynamics_output = self.network(x)

                # 分离状态和值前缀
                next_state = dynamics_output[:, :self.state_dim]
                value_prefix = dynamics_output[:, self.state_dim:]

                return next_state, value_prefix

        # 使用自定义的动态网络
        self.dynamics_network = EfficientZeroDynamicsNetwork(
            state_dim=state_dim,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            value_prefix_length=self.value_prefix_length,
            device=self.device
        )

    def predict(self, hidden_state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测策略和价值

        Args:
            hidden_state (torch.Tensor): 隐藏状态

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 策略对数和价值
        """
        # 保存当前训练状态
        training = self.prediction_network.training

        # 切换到评估模式
        self.prediction_network.eval()

        try:
            with torch.no_grad():
                if self.use_distributional_value:
                    # 如果使用分布式价值头
                    # 使用策略头获取策略对数
                    policy_logits = self.policy_head(hidden_state)

                    # 使用分布式价值头获取价值分布
                    value_logits = self.distributional_value_head(hidden_state)

                    # 计算风险敏感价值
                    value = self.distributional_value_head.compute_risk_sensitive_value(
                        value_logits,
                        alpha=self.risk_alpha,
                        beta=self.risk_beta
                    )

                    return policy_logits, value
                else:
                    # 使用标准预测网络
                    policy_logits, value = self.prediction_network(hidden_state)
                    return policy_logits, value
        finally:
            # 恢复原始训练状态
            self.prediction_network.train(training)

    def dynamics(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        动态函数：预测下一个状态和值前缀

        Args:
            state (torch.Tensor): 当前状态
            action (torch.Tensor): 执行的动作

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 下一个状态和值前缀
        """
        # 直接调用dynamics_network的forward方法，传递两个参数
        return self.dynamics_network(state, action)

    def self_supervised_loss(self, state1: torch.Tensor, state2: torch.Tensor) -> torch.Tensor:
        """
        计算自监督损失

        Args:
            state1 (torch.Tensor): 第一个状态表示
            state2 (torch.Tensor): 第二个状态表示

        Returns:
            torch.Tensor: 自监督损失值
        """
        p1, p2, z1, z2 = self.self_supervised_module(state1, state2)
        return self.self_supervised_module.compute_loss(p1, p2, z1, z2)

    def consistency_loss(self, predicted_state: torch.Tensor, target_state: torch.Tensor) -> torch.Tensor:
        """
        计算一致性损失

        Args:
            predicted_state (torch.Tensor): 预测的状态表示
            target_state (torch.Tensor): 目标状态表示

        Returns:
            torch.Tensor: 一致性损失值
        """
        # 使用均方误差计算一致性损失
        return F.mse_loss(predicted_state, target_state)

    def _visualize_attention(self, attention_weights: torch.Tensor, belief_state: Any) -> None:
        """
        可视化注意力权重（用于调试和分析）

        Args:
            attention_weights (torch.Tensor): 注意力权重张量 [batch_size, num_heads, query_len, key_len]
            belief_state (Any): 信念状态对象
        """
        try:
            import matplotlib.pyplot as plt
            import numpy as np
            import os

            # 确保存在可视化目录
            os.makedirs("logs/attention_vis", exist_ok=True)

            # 获取当前时间戳作为文件名
            timestamp = int(time.time())

            # 转换为CPU上的numpy数组
            attention_np = attention_weights.detach().cpu().numpy()

            # 获取形状信息
            batch_size, num_heads, query_len, key_len = attention_np.shape

            # 对每个批次和每个头进行可视化
            for b in range(batch_size):
                for h in range(num_heads):
                    # 创建图形
                    plt.figure(figsize=(10, 8))
                    plt.imshow(attention_np[b, h], cmap='viridis')
                    plt.colorbar()

                    # 添加标题和标签
                    plt.title(f"Attention Weights - Batch {b}, Head {h}")
                    plt.xlabel("Key (Belief Cards)")
                    plt.ylabel("Query (Hidden State)")

                    # 添加卡牌标签（如果可用）
                    if hasattr(belief_state, 'card_probabilities'):
                        card_labels = list(belief_state.card_probabilities.keys())
                        if len(card_labels) == key_len:
                            plt.xticks(range(key_len), card_labels, rotation=90)

                    # 保存图形
                    plt.tight_layout()
                    plt.savefig(f"logs/attention_vis/attention_b{b}_h{h}_{timestamp}.png")
                    plt.close()

            # 记录平均注意力权重
            avg_attention = np.mean(attention_np, axis=(0, 1))  # 平均所有批次和头
            plt.figure(figsize=(10, 8))
            plt.imshow(avg_attention, cmap='viridis')
            plt.colorbar()
            plt.title(f"Average Attention Weights")
            plt.xlabel("Key (Belief Cards)")
            plt.ylabel("Query (Hidden State)")
            plt.tight_layout()
            plt.savefig(f"logs/attention_vis/attention_avg_{timestamp}.png")
            plt.close()

            logging.info(f"注意力权重可视化已保存到logs/attention_vis/attention_*_{timestamp}.png")

        except Exception as e:
            logging.warning(f"可视化注意力权重时发生错误: {e}")
            return


class EfficientZero(MuZero):
    """
    EfficientZero算法

    扩展MuZero算法，添加自监督表示学习、值前缀预测等技术以提高样本效率。
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        projection_dim: int = 256,
        prediction_dim: int = 128,
        value_prefix_length: int = 5,
        num_simulations: int = 100,  # 优化：从50提升至100
        discount: float = 0.997,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: int = 19652,
        pb_c_init: float = 1.25,
        replay_buffer_size: int = 100000,
        batch_size: int = 256,  # 优化：从128提升至256
        num_unroll_steps: int = 5,
        td_steps: int = 10,
        value_loss_weight: float = 1.0,
        policy_loss_weight: float = 1.0,
        consistency_loss_weight: float = 1.0,
        self_supervised_loss_weight: float = 1.0,
        rlhf_loss_weight: float = 1.0,
        use_rlhf: bool = False,
        rlhf_preference_weight: float = 1.0,
        rlhf_feedback_weight: float = 1.0,
        rlhf_imitation_weight: float = 1.0,
        use_importance_weighting: bool = False,
        importance_weight_scale: float = 2.0,
        use_distributional_value: bool = True,  # 新增：是否使用分布式价值头
        value_support_size: int = 601,  # 新增：价值分布支持大小
        value_min: float = -300,  # 新增：价值范围最小值
        value_max: float = 300,  # 新增：价值范围最大值
        risk_alpha: float = 0.05,  # 新增：CVaR的置信水平
        risk_beta: float = 0.1,  # 新增：风险厌恶系数
        use_gto_regularization: bool = False,  # 新增：是否使用GTO正则化
        gto_policy_path: Optional[str] = None,  # 新增：GTO策略文件路径
        gto_regularization_weight: float = 0.1,  # 新增：GTO正则化权重
        gto_regularization_method: str = 'kl',  # 新增：GTO正则化方法
        gto_adaptive_weight: bool = False,  # 新增：是否使用自适应GTO正则化权重
        gto_adaptive_weight_params: Optional[Dict[str, float]] = None,  # 新增：自适应权重参数
        gto_feature_extractor: Optional[Callable[[Any], str]] = None,  # 新增：GTO特征提取器
        gto_default_policy_generator: Optional[Callable[[Any, List[int]], np.ndarray]] = None,  # 新增：GTO默认策略生成器
        use_opponent_distribution_switcher: bool = False,  # 新增：是否使用对手分布切换器
        opponent_switcher: Optional['OpponentDistributionSwitcher'] = None,  # 新增：对手分布切换器
        switch_strategy: str = 'periodic',  # 新增：切换策略
        switch_interval: int = 1000,  # 新增：切换间隔
        distribution_weights: Optional[Dict[str, float]] = None,  # 新增：分布权重
        use_ewc: bool = False,  # 新增：是否使用EWC算法
        ewc_lambda: float = 100.0,  # 新增：EWC正则化系数
        ewc_state_path: Optional[str] = None,  # 新增：EWC状态保存路径
        learning_rate: float = 0.0005,  # 优化：精细调整学习率
        weight_decay: float = 1e-4,
        lr_scheduler: str = 'step',
        device: str = None,
        amp_enabled: bool = False,
        # 新增：关键时刻检测和动态预算相关参数
        use_key_moment_detector: bool = False,
        key_moment_detector_config: Optional[Dict[str, Any]] = None,
        use_dynamic_budget: bool = False,
        dynamic_budget_config: Optional[Dict[str, Any]] = None
    ):
        """
        初始化EfficientZero算法

        Args:
            state_shape (Tuple[int, ...]): 状态空间形状
            action_shape (Tuple[int, ...]): 动作空间形状
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            state_dim (int, optional): 隐藏状态维度. Defaults to 64.
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
            projection_dim (int, optional): 投影维度. Defaults to 256.
            prediction_dim (int, optional): 预测网络隐藏层维度. Defaults to 128.
            value_prefix_length (int, optional): 值前缀长度. Defaults to 5.
            num_simulations (int, optional): MCTS模拟次数. Defaults to 50.
            discount (float, optional): 折扣因子. Defaults to 0.997.
            dirichlet_alpha (float, optional): Dirichlet噪声参数. Defaults to 0.25.
            exploration_fraction (float, optional): 探索比例. Defaults to 0.25.
            pb_c_base (int, optional): PUCT算法基数. Defaults to 19652.
            pb_c_init (float, optional): PUCT算法初始值. Defaults to 1.25.
            replay_buffer_size (int, optional): 回放缓冲区大小. Defaults to 100000.
            batch_size (int, optional): 批次大小. Defaults to 128.
            num_unroll_steps (int, optional): 展开步数. Defaults to 5.
            td_steps (int, optional): 时差学习步数. Defaults to 10.
            value_loss_weight (float, optional): 值函数损失权重. Defaults to 1.0.
            policy_loss_weight (float, optional): 策略损失权重. Defaults to 1.0.
            consistency_loss_weight (float, optional): 一致性损失权重. Defaults to 1.0.
            self_supervised_loss_weight (float, optional): 自监督损失权重. Defaults to 1.0.
            learning_rate (float, optional): 学习率. Defaults to 0.001.
            weight_decay (float, optional): 权重衰减. Defaults to 1e-4.
            lr_scheduler (str, optional): 学习率调度器. Defaults to 'step'.
            device (str, optional): 计算设备. Defaults to None.
        """
        # 调用父类初始化
        super(EfficientZero, self).__init__(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            replay_buffer_size=replay_buffer_size,
            batch_size=batch_size,
            num_unroll_steps=num_unroll_steps,
            td_steps=td_steps,
            value_loss_weight=value_loss_weight,
            policy_loss_weight=policy_loss_weight,
            consistency_loss_weight=consistency_loss_weight,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            lr_scheduler=lr_scheduler,
            device=device
        )

        # EfficientZero特有参数
        self.value_prefix_length = value_prefix_length
        self.self_supervised_loss_weight = self_supervised_loss_weight
        self.use_belief_state = False  # 默认不使用信念状态

        # 分布式价值头相关参数
        self.use_distributional_value = use_distributional_value
        self.value_support_size = value_support_size
        self.value_min = value_min
        self.value_max = value_max
        self.risk_alpha = risk_alpha
        self.risk_beta = risk_beta

        # 修复：此处应实例化EfficientZeroModel而不是EfficientZero，避免递归和参数不匹配错误
        self.model = EfficientZeroModel(
            observation_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim,
            value_prefix_length=value_prefix_length,
            belief_dim=54,  # 默认为标准扑克牌数量
            use_belief_state=self.use_belief_state,
            use_distributional_value=self.use_distributional_value,
            value_support_size=self.value_support_size,
            value_min=self.value_min,
            value_max=self.value_max,
            risk_alpha=self.risk_alpha,
            risk_beta=self.risk_beta,
            device=self.device
        )

        # 修复：此处应实例化EfficientZeroModel而不是EfficientZero，避免递归和参数不匹配错误
        self.target_model = EfficientZeroModel(
            observation_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim,
            value_prefix_length=value_prefix_length,
            belief_dim=54,  # 默认为标准扑克牌数量
            use_belief_state=self.use_belief_state,
            use_distributional_value=self.use_distributional_value,
            value_support_size=self.value_support_size,
            value_min=self.value_min,
            value_max=self.value_max,
            risk_alpha=self.risk_alpha,
            risk_beta=self.risk_beta,
            device=self.device
        )
        self._update_target_network(tau=1.0)  # 完全复制参数

        # 创建MCTS搜索器
        self.num_simulations = num_simulations
        self.discount = discount
        self.dirichlet_alpha = dirichlet_alpha
        self.exploration_fraction = exploration_fraction
        self.pb_c_base = pb_c_base
        self.pb_c_init = pb_c_init

        self.mcts = self._create_mcts(
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            use_opponent_model_prior=True,  # 启用对手模型先验
            opponent_model_prior_weight=0.5  # 设置对手模型先验权重
        )

        # 创建回放缓冲区
        self.replay_buffer = self._create_replay_buffer(replay_buffer_size)

        # 设置训练参数
        self.batch_size = batch_size
        self.num_unroll_steps = num_unroll_steps
        self.td_steps = td_steps
        self.value_loss_weight = value_loss_weight
        self.policy_loss_weight = policy_loss_weight
        self.consistency_loss_weight = consistency_loss_weight
        self.self_supervised_loss_weight = self_supervised_loss_weight

        # RLHF相关参数
        self.use_rlhf = use_rlhf
        self.rlhf_loss_weight = rlhf_loss_weight
        self.rlhf_preference_weight = rlhf_preference_weight
        self.rlhf_feedback_weight = rlhf_feedback_weight
        self.rlhf_imitation_weight = rlhf_imitation_weight

        # 重要性加权训练参数
        self.use_importance_weighting = use_importance_weighting
        self.importance_weight_scale = importance_weight_scale

        # 关键决策点检测器（初始为None，可通过set_key_moment_detector方法设置）
        self.key_moment_detector = None

        # 创建优化器
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # 创建学习率调度器
        self.scheduler = self._create_scheduler(lr_scheduler)

        # 训练步数
        self.train_steps = 0

        # 正则化损失
        self.regularization_loss = torch.tensor(0.0, device=self.device)

        # GTO正则化相关
        self.use_gto_regularization = use_gto_regularization
        self.gto_regularization_weight = gto_regularization_weight
        self.gto_regularization_method = gto_regularization_method
        self.gto_adaptive_weight = gto_adaptive_weight
        self.gto_adaptive_weight_params = gto_adaptive_weight_params
        self.gto_regularizer = None
        self.gto_policy = None

        # 混合精度训练
        self.amp_enabled = amp_enabled

        # 对手分布切换器相关设置
        self.use_opponent_distribution_switcher = use_opponent_distribution_switcher
        self.opponent_switcher = opponent_switcher
        self.switch_strategy = switch_strategy
        self.switch_interval = switch_interval
        self.distribution_weights = distribution_weights

        # 如果启用对手分布切换器，但没有提供切换器实例，则记录警告
        if self.use_opponent_distribution_switcher and self.opponent_switcher is None:
            logger.warning("启用了对手分布切换器，但未提供切换器实例。请使用set_opponent_switcher方法设置切换器。")

        # 如果启用GTO正则化，初始化GTO策略和正则化器
        if self.use_gto_regularization:
            try:
                # 导入GTO相关模块
                from cardgame_ai.algorithms.gto_approximation import GTOPolicy, GTORegularizer

                # 创建GTO策略
                self.gto_policy = GTOPolicy(
                    policy_path=gto_policy_path,
                    feature_extractor=gto_feature_extractor,
                    default_policy_generator=gto_default_policy_generator
                )

                # 创建GTO正则化器
                self.gto_regularizer = GTORegularizer(
                    gto_policy=self.gto_policy,
                    regularization_weight=gto_regularization_weight,
                    regularization_method=gto_regularization_method,
                    adaptive_weight=gto_adaptive_weight,
                    adaptive_weight_params=gto_adaptive_weight_params,
                    device=self.device
                )

                logger.info(f"GTO正则化初始化成功，方法: {gto_regularization_method}, 权重: {gto_regularization_weight}, "
                           f"自适应权重: {gto_adaptive_weight}")
                if gto_policy_path and os.path.exists(gto_policy_path):
                    logger.info(f"已加载GTO策略: {gto_policy_path}")
                else:
                    logger.info("使用默认GTO策略生成器")
            except Exception as e:
                logger.error(f"GTO正则化初始化失败: {e}")
                self.use_gto_regularization = False

        # EWC相关参数
        self.use_ewc = use_ewc
        self.ewc_lambda = ewc_lambda
        self.ewc_state_path = ewc_state_path
        self.ewc = None  # EWC实例初始为None

        # 关键时刻检测器初始化
        self.use_key_moment_detector = use_key_moment_detector
        self.key_moment_detector = None
        if use_key_moment_detector:
            detector_config = key_moment_detector_config or {}
            self.key_moment_detector = KeyMomentDetector(**detector_config)
            logger.info("初始化关键时刻检测器")

        # 动态预算分配器初始化
        self.use_dynamic_budget = use_dynamic_budget
        self.dynamic_budget_allocator = None
        if use_dynamic_budget:
            budget_config = dynamic_budget_config or {}
            self.dynamic_budget_allocator = DynamicBudgetAllocator(
                key_moment_detector=self.key_moment_detector,
                default_budget=self.num_simulations,
                **budget_config
            )
            logger.info("初始化动态预算分配器")

    def initialize_ewc(self, dataloader=None):
        """
        初始化EWC算法对象

        Args:
            dataloader: 用于计算Fisher信息矩阵的数据加载器
        """
        if not self.use_ewc:
            logger.info("EWC未启用，跳过初始化")
            return

        if dataloader is None:
            logger.warning("未提供数据加载器，无法初始化EWC")
            return

        try:
            # 创建EWC实例
            self.ewc = EWC(
                model=self.model,
                dataloader=dataloader,
                fisher_importance=self.ewc_lambda,
                device=self.device
            )
            logger.info(f"EWC初始化成功，Fisher重要性系数: {self.ewc_lambda}")

            # 如果存在状态路径，尝试加载EWC状态
            if self.ewc_state_path and os.path.exists(f"{self.ewc_state_path}_fisher.pt"):
                success = self.ewc.load_state(self.ewc_state_path)
                if success:
                    logger.info(f"已加载EWC状态: {self.ewc_state_path}")
                else:
                    logger.warning(f"加载EWC状态失败: {self.ewc_state_path}")
        except Exception as e:
            logger.error(f"EWC初始化失败: {e}")
            self.ewc = None

    def update_ewc_importance(self, dataloader=None):
        """
        更新EWC的Fisher信息矩阵

        在任务切换或学习新策略前调用此方法，以更新参数重要性

        Args:
            dataloader: 用于计算Fisher信息矩阵的数据加载器
        """
        if not self.use_ewc or self.ewc is None:
            logger.warning("EWC未启用或未初始化，无法更新参数重要性")
            return

        if dataloader is None:
            logger.warning("未提供数据加载器，无法更新参数重要性")
            return

        try:
            # 更新EWC模型
            self.ewc.update_model(self.model)
            logger.info("EWC参数重要性更新成功")

            # 如果提供了状态路径，保存EWC状态
            if self.ewc_state_path:
                os.makedirs(os.path.dirname(self.ewc_state_path), exist_ok=True)
                self.ewc.save_state(self.ewc_state_path)
                logger.info(f"已保存EWC状态: {self.ewc_state_path}")
        except Exception as e:
            logger.error(f"更新EWC参数重要性失败: {e}")

    def train(self, batch: Dict[str, Any], human_feedback_batch: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        训练模型（支持重要性加权和EfficientZero特有的一致性损失）

        Args:
            batch: 从回放缓冲区采样的批次数据
            human_feedback_batch: 人类反馈数据（可选）

        Returns:
            Dict[str, float]: 包含各种损失值的字典
        """
        # 初始化优化器
        self.optimizer.zero_grad()

        # 提取批次数据 - 修复：处理键名不匹配问题
        # sample_batch返回的是'states'，但train方法期望'observations'
        if 'observations' in batch:
            observations = batch['observations']
        elif 'states' in batch:
            observations = batch['states']  # 使用states作为observations
        else:
            raise KeyError("批次数据中既没有'observations'也没有'states'键")

        actions = batch['actions']

        # 处理next_observations/next_states的键名不匹配
        if 'next_observations' in batch:
            next_observations = batch['next_observations']
        elif 'next_states' in batch:
            next_observations = batch['next_states']  # 使用next_states作为next_observations
        else:
            raise KeyError("批次数据中既没有'next_observations'也没有'next_states'键")

        # 计算样本重要性权重（基于关键决策点检测）
        if self.use_importance_weighting and self.key_moment_detector is not None:
            sample_weights = self.calculate_importance_weights(observations)
            # 添加到损失字典中，用于记录
            importance_weight_mean = sample_weights.mean().item()
            importance_weight_max = sample_weights.max().item()
            importance_weight_min = sample_weights.min().item()
        else:
            sample_weights = torch.ones(observations.shape[0], device=self.device)
            importance_weight_mean = 1.0
            importance_weight_max = 1.0
            importance_weight_min = 1.0

        # 获取当前状态和下一个状态的表示
        current_states = self.model.representation(observations)
        next_states_target = self.model.representation(next_observations)

        # 计算自监督表示学习损失
        self_supervised_loss = self.model.self_supervised_loss(current_states, next_states_target)

        # 如果启用了重要性加权，应用权重
        if self.use_importance_weighting:
            self_supervised_loss = (self_supervised_loss * sample_weights).mean()
        else:
            self_supervised_loss = self_supervised_loss.mean()

        # 初始化损失字典
        losses = {
            'value_loss': 0.0,
            'policy_loss': 0.0,
            'reward_loss': 0.0,
            'consistency_loss': 0.0,
            'self_supervised_loss': self_supervised_loss.item(),
            'rlhf_loss': 0.0,
            'gto_loss': 0.0,  # 新增：GTO正则化损失
            'importance_weight_mean': importance_weight_mean,
            'importance_weight_max': importance_weight_max,
            'importance_weight_min': importance_weight_min,
            'total_loss': 0.0
        }

        # 初始化损失缩放因子
        gradient_scale = 1.0 / self.num_unroll_steps

        # 展开训练 (通过动态模型预测未来状态和奖励)
        current_states_pred = current_states
        for step in range(self.num_unroll_steps):
            # 如果是第一步，使用真实动作；否则使用模型预测的动作
            if step == 0:
                # 使用真实轨迹中的动作
                current_actions = actions
            else:
                # 使用上一步预测的策略中概率最高的动作
                # 注意：实际实现时需要根据您的策略表示方式调整
                current_actions = torch.argmax(policy_logits_pred, dim=1)

            # 使用动态网络预测下一个状态和值前缀
            next_states_pred, _ = self.model.dynamics(current_states_pred, current_actions)

            # 使用预测网络预测策略和值
            policy_logits_pred, _ = self.model.predict(next_states_pred)

            # 计算一致性损失 (EfficientZero改进)
            if self.consistency_loss_weight > 0:
                with torch.no_grad():
                    # 使用目标网络预测下一个状态
                    target_next_states, _ = self.target_model.dynamics(current_states_pred.detach(), current_actions)

                # 计算预测状态与目标状态之间的一致性损失
                step_consistency_loss = self.model.consistency_loss(next_states_pred, target_next_states)

                # 如果启用了重要性加权，应用权重
                if self.use_importance_weighting:
                    weighted_consistency_loss = (step_consistency_loss * sample_weights).mean()
                    losses['consistency_loss'] += weighted_consistency_loss.item() * gradient_scale

                    # 将加权一致性损失添加到总损失中
                    losses['total_loss'] += self.consistency_loss_weight * weighted_consistency_loss * gradient_scale
                else:
                    losses['consistency_loss'] += step_consistency_loss.item() * gradient_scale

                    # 将一致性损失添加到总损失中
                    losses['total_loss'] += self.consistency_loss_weight * step_consistency_loss * gradient_scale

            # 更新当前状态为预测的下一个状态
            current_states_pred = next_states_pred

        # EfficientZero不调用父类的update方法，因为dynamics_network输出格式不同
        # 父类期望(next_state, reward)，但EfficientZero输出(next_state, value_prefix)
        # 我们需要自己实现MuZero的核心损失计算，但适配EfficientZero的输出格式
        parent_losses = self._compute_muzero_losses(batch)

        # 将父类损失添加到当前损失中
        # 如果启用了重要性加权，我们需要对这些损失应用权重
        if self.use_importance_weighting:
            # 对于value_loss和policy_loss，我们需要重新计算并应用权重
            # 这里假设父类返回的是平均损失，我们需要重新计算加权损失
            # 注意：这是一个简化处理，实际上可能需要修改父类的实现以支持重要性加权
            for key in ['value_loss', 'policy_loss', 'reward_loss']:
                if key in parent_losses:
                    # 这里我们简单地将平均损失乘以样本数，然后应用权重，再取平均
                    # 这是一个近似处理，实际上可能需要更精确的实现
                    losses[key] += parent_losses[key]

            # 对于total_loss，我们不直接使用父类的值，而是在最后重新计算
        else:
            # 如果不使用重要性加权，直接使用父类的损失
            for key in ['value_loss', 'policy_loss', 'reward_loss', 'total_loss']:
                if key in parent_losses:
                    losses[key] += parent_losses[key]

        # 将自监督损失添加到总损失中
        # 注意：self_supervised_loss已经在前面应用了权重
        losses['total_loss'] += self.self_supervised_loss_weight * self_supervised_loss

        # 计算GTO正则化损失
        if self.use_gto_regularization and self.gto_regularizer is not None:
            try:
                # 获取当前状态和合法动作
                states = batch.get('states', batch.get('observations'))
                legal_actions_list = batch.get('legal_actions', [None] * len(states))

                # 获取当前策略
                hidden_states = self.model.representation(states)
                policy_logits, _ = self.model.predict(hidden_states)

                # 计算GTO正则化损失
                gto_loss = self.gto_regularizer.compute_loss(
                    states=states,
                    legal_actions_list=legal_actions_list,
                    predicted_policies=policy_logits
                )

                # 添加到损失字典
                losses['gto_loss'] = gto_loss.item()

                # 添加到总损失
                losses['total_loss'] += gto_loss

                # 计算策略与GTO的距离（用于监控）
                if self.train_steps % 100 == 0:  # 每100步记录一次
                    mean_dist, max_dist, min_dist = self.gto_regularizer.compute_policy_distance(
                        states=states,
                        legal_actions_list=legal_actions_list,
                        predicted_policies=policy_logits
                    )
                    logger.info(f"策略与GTO距离 - 平均: {mean_dist:.4f}, 最大: {max_dist:.4f}, 最小: {min_dist:.4f}, "
                               f"方法: {self.gto_regularization_method}, 当前权重: {self.gto_regularizer.get_current_weight():.4f}")

                # 更新自适应权重（如果启用）
                if self.gto_adaptive_weight:
                    # 使用策略损失作为性能指标来调整权重
                    self.gto_regularizer.update_weight(losses['policy_loss'])
            except Exception as e:
                logger.warning(f"计算GTO正则化损失时出错: {e}")
                losses['gto_loss'] = 0.0

        # 添加正则化损失（如EWC或L2正则化）
        if self.regularization_loss.item() > 0:
            losses['regularization_loss'] = self.regularization_loss.item()
            losses['total_loss'] += self.regularization_loss
        else:
            losses['regularization_loss'] = 0.0

        # 计算EWC损失（防止灾难性遗忘）
        if self.use_ewc and self.ewc is not None:
            try:
                # 计算EWC惩罚项
                ewc_loss = self.ewc.penalty(self.model)

                # 添加到损失字典
                losses['ewc_loss'] = ewc_loss.item()

                # 添加到总损失
                losses['total_loss'] += ewc_loss

                if self.train_steps % 100 == 0:  # 每100步记录一次
                    logger.info(f"EWC损失: {ewc_loss.item():.4f}")
            except Exception as e:
                logger.warning(f"计算EWC损失时出错: {e}")
                losses['ewc_loss'] = 0.0
        else:
            losses['ewc_loss'] = 0.0

        # 处理人类反馈数据（RLHF）
        if human_feedback_batch is not None and len(human_feedback_batch) > 0:
            # 计算RLHF损失
            rlhf_loss = self.calculate_rlhf_loss(batch, human_feedback_batch)

            # 如果启用了重要性加权，应用权重
            if self.use_importance_weighting:
                # 此时rlhf_loss应该是每个样本的损失
                # 应用样本权重
                weighted_rlhf_loss = (rlhf_loss * sample_weights).mean()
                losses['rlhf_loss'] = weighted_rlhf_loss.item()

                # 将加权RLHF损失添加到总损失中
                rlhf_weight = getattr(self, 'rlhf_loss_weight', 1.0)
                losses['total_loss'] += rlhf_weight * weighted_rlhf_loss
            else:
                # 如果不使用重要性加权，rlhf_loss应该是平均损失
                losses['rlhf_loss'] = rlhf_loss.item()

                # 将RLHF损失添加到总损失中
                rlhf_weight = getattr(self, 'rlhf_loss_weight', 1.0)
                losses['total_loss'] += rlhf_weight * rlhf_loss

        # 反向传播和优化
        losses['total_loss'].backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), 10)

        # 更新参数
        self.optimizer.step()

        # 更新目标网络
        self._update_target_network()

        # 更新学习率
        if self.scheduler is not None:
            self.scheduler.step()

        # 增加训练步数
        self.train_steps += 1

        # 重置正则化损失，避免重复使用
        self.regularization_loss = torch.tensor(0.0, device=self.device)

        # 更新对手分布切换器
        if self.use_opponent_distribution_switcher and self.opponent_switcher is not None:
            # 使用训练指标作为性能指标
            metrics = losses.get('value_loss', 0.0) + losses.get('policy_loss', 0.0)
            self.opponent_switcher.update(metrics)

            # 记录对手分布切换器统计信息
            if self.train_steps % 100 == 0:  # 每100步记录一次
                stats = self.opponent_switcher.get_stats()
                logger.info(f"对手分布切换器统计信息: {stats}")

        return losses

    def _compute_muzero_losses(self, batch: Dict[str, Any]) -> Dict[str, float]:
        """
        计算MuZero风格的损失，但适配EfficientZero的dynamics_network输出格式

        Args:
            batch: 批次数据

        Returns:
            Dict[str, float]: 损失字典
        """
        # 提取批次数据
        states = batch['states']
        actions = batch['actions']
        rewards = batch['rewards']
        next_states = batch['next_states']
        dones = batch['dones']

        batch_size = states.shape[0]

        # 获取初始隐藏状态
        current_states = self.model.represent(states)

        # 计算目标值和策略
        with torch.no_grad():
            target_values = self._compute_target_values(rewards, dones, next_states)
            target_policies = self._compute_target_policies(states)

        # 初始化损失
        value_loss = torch.tensor(0.0, device=self.device)
        policy_loss = torch.tensor(0.0, device=self.device)
        reward_loss = torch.tensor(0.0, device=self.device)
        consistency_loss = torch.tensor(0.0, device=self.device)

        # 展开步骤
        for step in range(self.num_unroll_steps):
            # 获取当前动作
            if step == 0:
                current_actions = actions
            else:
                # 使用目标策略中概率最高的动作
                current_actions = torch.argmax(target_policies[:, step - 1], dim=1)

            # 使用动态网络预测下一个状态和值前缀
            next_states_pred, value_prefix_pred = self.model.dynamics_network(current_states, current_actions)

            # 使用预测网络预测策略和价值
            policy_logits_pred, values_pred = self.model.prediction_network(next_states_pred)

            # 计算奖励损失 - 使用值前缀的第一个元素作为即时奖励预测
            if step == 0:
                # 第一步使用真实奖励，值前缀的第一个元素应该预测即时奖励
                immediate_reward_pred = value_prefix_pred[:, 0]  # 取值前缀的第一个元素
                reward_loss += F.mse_loss(immediate_reward_pred, rewards, reduction='mean')

            # 计算策略损失 (交叉熵)
            policy_loss += F.cross_entropy(
                policy_logits_pred,
                torch.argmax(target_policies[:, step], dim=1),
                reduction='mean'
            )

            # 计算价值损失 (MSE)
            value_loss += F.mse_loss(values_pred, target_values[:, step], reduction='mean')

            # 计算一致性损失 (EfficientZero改进)
            if self.consistency_loss_weight > 0:
                with torch.no_grad():
                    target_next_states, _ = self.target_model.dynamics_network(current_states, current_actions)

                consistency_loss += F.mse_loss(
                    next_states_pred,
                    target_next_states,
                    reduction='mean'
                )

            # 更新当前状态为预测的下一个状态
            current_states = next_states_pred

        # 返回损失字典
        return {
            'value_loss': value_loss.item(),
            'policy_loss': policy_loss.item(),
            'reward_loss': reward_loss.item(),
            'consistency_loss': consistency_loss.item(),
            'total_loss': (value_loss + policy_loss + reward_loss + consistency_loss).item()
        }

    def _update_target_network(self, tau=0.005):
        """
        软更新目标网络

        Args:
            tau (float, optional): 软更新参数. Defaults to 0.005.
        """
        for target_param, param in zip(self.target_model.parameters(), self.model.parameters()):
            target_param.data.copy_(tau * param.data + (1 - tau) * target_param.data)

    def set_key_moment_detector(self, detector):
        """
        设置关键时刻检测器

        Args:
            detector: 关键时刻检测器实例
        """
        self.key_moment_detector = detector
        self.use_key_moment_detector = (detector is not None)

        # 如果存在动态预算分配器，也更新它
        if self.dynamic_budget_allocator is not None:
            self.dynamic_budget_allocator.set_key_moment_detector(detector)

        # 如果存在MCTS搜索器，也更新它
        if hasattr(self, 'mcts') and self.mcts is not None:
            self.mcts.key_moment_detector = detector
            self.mcts.use_key_moment_detector = (detector is not None)

        logger.info(f"设置关键时刻检测器: {detector.name if detector else 'None'}")

    def set_dynamic_budget_allocator(self, allocator):
        """
        设置动态预算分配器

        Args:
            allocator: 动态预算分配器实例
        """
        self.dynamic_budget_allocator = allocator
        self.use_dynamic_budget = (allocator is not None)
        logger.info(f"设置动态预算分配器: {allocator.name if allocator else 'None'}")

    def set_opponent_switcher(self, opponent_switcher: 'OpponentDistributionSwitcher'):
        """
        设置对手分布切换器

        Args:
            opponent_switcher: OpponentDistributionSwitcher实例
        """
        self.opponent_switcher = opponent_switcher
        self.use_opponent_distribution_switcher = True

        # 更新切换器的设置
        if hasattr(opponent_switcher, 'switch_strategy'):
            self.switch_strategy = opponent_switcher.switch_strategy
        if hasattr(opponent_switcher, 'switch_interval'):
            self.switch_interval = opponent_switcher.switch_interval
        if hasattr(opponent_switcher, 'distribution_weights'):
            self.distribution_weights = opponent_switcher.distribution_weights

        logger.info(f"已设置对手分布切换器，当前分布: {opponent_switcher.current_distribution}")

    def get_opponent(self, state=None, legal_actions=None):
        """
        获取当前对手

        如果启用了对手分布切换器，则使用切换器获取对手；
        否则返回None，表示使用默认对手。

        Args:
            state: 当前状态（可选）
            legal_actions: 合法动作列表（可选）

        Returns:
            对手策略或None
        """
        if self.use_opponent_distribution_switcher and self.opponent_switcher is not None:
            return self.opponent_switcher.get_opponent(state, legal_actions)
        return None

    def add_regularization_loss(self, loss: torch.Tensor):
        """
        添加正则化损失

        用于添加外部正则化损失，如EWC或L2正则化惩罚项。
        这个损失将在下一次调用train方法时被添加到总损失中。

        Args:
            loss: 正则化损失
        """
        if isinstance(loss, torch.Tensor):
            self.regularization_loss = loss
        else:
            # 如果不是张量，尝试转换
            try:
                self.regularization_loss = torch.tensor(loss, device=self.device)
            except:
                logger.warning(f"无法将正则化损失转换为张量: {loss}")
                self.regularization_loss = torch.tensor(0.0, device=self.device)

    def calculate_importance_weights(self, observations: torch.Tensor) -> torch.Tensor:
        """
        计算样本的重要性权重

        基于关键决策点检测器的评分，对样本进行加权。
        关键决策点的样本将获得更高的权重。

        Args:
            observations: 观察数据，形状为[batch_size, ...]

        Returns:
            torch.Tensor: 样本权重，形状为[batch_size]
        """
        # 如果未启用重要性加权或没有关键决策点检测器，返回均匀权重
        use_importance_weighting = getattr(self, 'use_importance_weighting', False)
        key_moment_detector = getattr(self, 'key_moment_detector', None)

        if not use_importance_weighting or key_moment_detector is None:
            return torch.ones(observations.shape[0], device=self.device)

        # 使用关键决策点检测器计算关键程度评分
        with torch.no_grad():
            # 确保检测器处于评估模式
            key_moment_detector.eval()

            # 计算关键程度评分
            criticality_scores = key_moment_detector(observations)

            # 确保评分是一维张量
            if criticality_scores.dim() > 1:
                criticality_scores = criticality_scores.squeeze()

            # 应用缩放因子，增强重要样本的权重
            importance_weight_scale = getattr(self, 'importance_weight_scale', 2.0)
            weights = 1.0 + (importance_weight_scale - 1.0) * criticality_scores

            # 归一化权重，使其平均值为1
            weights = weights * (observations.shape[0] / weights.sum())

        return weights

    def _create_mcts(self, **kwargs):
        """
        创建MCTS搜索器

        Returns:
            MCTS: MCTS搜索器实例
        """
        # 从父类继承默认设置
        mcts_kwargs = {
            'num_simulations': self.num_simulations,
            'discount': self.discount,
            'dirichlet_alpha': self.dirichlet_alpha,
            'exploration_fraction': self.exploration_fraction,
            'pb_c_base': self.pb_c_base,
            'pb_c_init': self.pb_c_init,
            'use_belief_state': getattr(self, 'use_belief_state', False),
            'risk_alpha': getattr(self, 'risk_alpha', 0.05),
            'risk_beta': getattr(self, 'risk_beta', 0.1)
        }

        # 添加关键时刻检测器相关参数
        if hasattr(self, 'use_key_moment_detector') and self.use_key_moment_detector:
            mcts_kwargs.update({
                'use_key_moment_detector': True,
                'key_moment_detector': getattr(self, 'key_moment_detector', None),
                'key_moment_factor': 2.0  # 默认关键时刻系数
            })

        # 更新自定义参数
        mcts_kwargs.update(kwargs)

        # 创建MCTS实例
        from cardgame_ai.algorithms.mcts import MCTS
        return MCTS(**mcts_kwargs)

    def _compute_distributional_value_loss(self, predicted_logits: torch.Tensor, target_values: torch.Tensor) -> torch.Tensor:
        """
        计算分布式价值损失

        将目标值转换为分布，然后计算与预测分布的交叉熵损失。

        Args:
            predicted_logits (torch.Tensor): 预测的价值分布logits
            target_values (torch.Tensor): 目标价值（标量）

        Returns:
            torch.Tensor: 分布式价值损失
        """
        if not self.use_distributional_value:
            # 如果未启用分布式价值头，则使用标准MSE损失
            return F.mse_loss(predicted_logits, target_values)

        # 使用模型的分布式价值头计算损失
        return self.model.distributional_value_head.compute_distributional_loss(
            predicted_logits=predicted_logits,
            target_values=target_values
        )

    def _create_replay_buffer(self, capacity):
        """
        创建回放缓冲区

        Args:
            capacity: 回放缓冲区容量

        Returns:
            ReplayBuffer: 回放缓冲区实例
        """
        if getattr(self, 'use_importance_weighting', False):
            from cardgame_ai.algorithms.replay_buffer import PrioritizedReplayBuffer
            return PrioritizedReplayBuffer(
                capacity=capacity,
                alpha=0.6,  # 优先级指数
                beta=0.4,   # 重要性采样指数
                beta_increment=0.001  # beta增量
            )
        else:
            from cardgame_ai.algorithms.replay_buffer import ReplayBuffer
            return ReplayBuffer(capacity)

    def store_experience(self, state: Union[State, np.ndarray], action: Union[int, Action],
                         reward: float, next_state: Union[State, np.ndarray], done: bool) -> None:
        """
        存储单步经验到回放缓冲区

        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一个状态
            done: 是否终止
        """
        # 创建Experience对象
        from cardgame_ai.core.base import Experience

        # 根据需要转换状态和动作
        if isinstance(state, State):
            # 如果是State对象，提取成numpy数组
            state_array = state.to_array()
        else:
            # 已经是numpy数组
            state_array = state

        if isinstance(next_state, State):
            next_state_array = next_state.to_array()
        else:
            next_state_array = next_state

        if isinstance(action, Action):
            action_index = action.to_index()
        else:
            action_index = action

        # 构建经验记录
        experience = Experience(
            state=state_array,
            action=action_index,
            reward=reward,
            next_state=next_state_array,
            done=done
        )

        # 存储到回放缓冲区
        self.replay_buffer.add(experience)

    def sample_batch(self, batch_size: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        从经验回放缓冲区采样批次数据

        Args:
            batch_size: 批量大小，如果为None则使用默认批量大小

        Returns:
            Dict[str, Any]: 批次数据字典，包含states, actions, rewards, next_states, dones
                           如果回放缓冲区为空或数据不足，返回None
        """
        if batch_size is None:
            batch_size = self.batch_size

        # 检查缓冲区是否有足够的样本
        if len(self.replay_buffer) < batch_size:
            return None

        # 从回放缓冲区采样
        if getattr(self, 'use_importance_weighting', False):
            batch, indices, weights = self.replay_buffer.sample(batch_size, return_indices_and_weights=True)

            # 将numpy数组转换为PyTorch张量
            states = torch.tensor(np.array([exp.state for exp in batch]), dtype=torch.float32).to(self.device)
            actions = torch.tensor(np.array([exp.action for exp in batch]), dtype=torch.int64).to(self.device)
            rewards = torch.tensor(np.array([exp.reward for exp in batch]), dtype=torch.float32).to(self.device)
            next_states = torch.tensor(np.array([exp.next_state for exp in batch]), dtype=torch.float32).to(self.device)
            dones = torch.tensor(np.array([exp.done for exp in batch]), dtype=torch.bool).to(self.device)
            importance_weights = torch.tensor(weights, dtype=torch.float32).to(self.device)

            return {
                'states': states,
                'actions': actions,
                'rewards': rewards,
                'next_states': next_states,
                'dones': dones,
                'indices': indices,
                'weights': importance_weights
            }
        else:
            batch = self.replay_buffer.sample(batch_size)

            # 将numpy数组转换为PyTorch张量
            states = torch.tensor(np.array([exp.state for exp in batch]), dtype=torch.float32).to(self.device)
            actions = torch.tensor(np.array([exp.action for exp in batch]), dtype=torch.int64).to(self.device)
            rewards = torch.tensor(np.array([exp.reward for exp in batch]), dtype=torch.float32).to(self.device)
            next_states = torch.tensor(np.array([exp.next_state for exp in batch]), dtype=torch.float32).to(self.device)
            dones = torch.tensor(np.array([exp.done for exp in batch]), dtype=torch.bool).to(self.device)

            return {
                'states': states,
                'actions': actions,
                'rewards': rewards,
                'next_states': next_states,
                'dones': dones
            }

    def calculate_rlhf_loss(self, batch: Dict[str, Any], human_feedback_batch: Dict[str, Any]) -> torch.Tensor:
        """
        计算基于人类反馈的强化学习损失

        Args:
            batch: 从回放缓冲区采样的批次数据
            human_feedback_batch: 人类反馈数据批次

        Returns:
            torch.Tensor: RLHF损失，如果use_importance_weighting为True，则返回每个样本的损失；
                         否则返回平均损失
        """
        # 如果未启用RLHF，直接返回零损失
        if not self.use_rlhf:
            return torch.tensor(0.0, device=self.device)

        # 初始化总损失
        total_rlhf_loss = torch.tensor(0.0, device=self.device)
        per_sample_losses = None

        # 提取人类反馈数据
        # 假设human_feedback_batch包含以下字段：
        # - states: 游戏状态
        # - ai_suggestions: AI建议的动作
        # - human_actions: 人类实际执行的动作
        # - feedback_scores: 人类对AI建议的评分（可选）
        # - preferences: 人类偏好（可选，用于偏好学习）

        # 1. 基于人类偏好的损失计算（偏好学习）
        if 'preferences' in human_feedback_batch:
            preference_loss, per_sample_preference_loss = self._calculate_preference_loss(
                human_feedback_batch, return_per_sample=self.use_importance_weighting
            )

            if self.use_importance_weighting and per_sample_preference_loss is not None:
                if per_sample_losses is None:
                    per_sample_losses = self.rlhf_preference_weight * per_sample_preference_loss
                else:
                    per_sample_losses += self.rlhf_preference_weight * per_sample_preference_loss

            total_rlhf_loss += self.rlhf_preference_weight * preference_loss

        # 2. 基于人类反馈评分的损失计算
        if 'feedback_scores' in human_feedback_batch:
            feedback_loss, per_sample_feedback_loss = self._calculate_feedback_score_loss(
                human_feedback_batch, return_per_sample=self.use_importance_weighting
            )

            if self.use_importance_weighting and per_sample_feedback_loss is not None:
                if per_sample_losses is None:
                    per_sample_losses = self.rlhf_feedback_weight * per_sample_feedback_loss
                else:
                    per_sample_losses += self.rlhf_feedback_weight * per_sample_feedback_loss

            total_rlhf_loss += self.rlhf_feedback_weight * feedback_loss

        # 3. 基于人类动作模仿的损失计算（行为克隆）
        if 'human_actions' in human_feedback_batch:
            imitation_loss, per_sample_imitation_loss = self._calculate_imitation_loss(
                human_feedback_batch, return_per_sample=self.use_importance_weighting
            )

            if self.use_importance_weighting and per_sample_imitation_loss is not None:
                if per_sample_losses is None:
                    per_sample_losses = self.rlhf_imitation_weight * per_sample_imitation_loss
                else:
                    per_sample_losses += self.rlhf_imitation_weight * per_sample_imitation_loss

            total_rlhf_loss += self.rlhf_imitation_weight * imitation_loss

        # 如果启用了重要性加权并且有每个样本的损失，返回每个样本的损失
        if self.use_importance_weighting and per_sample_losses is not None:
            return per_sample_losses

        # 否则返回平均损失
        return total_rlhf_loss

    def _calculate_preference_loss(self, human_feedback_batch: Dict[str, Any], return_per_sample: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        计算基于人类偏好的损失（偏好学习）

        Args:
            human_feedback_batch: 人类反馈数据批次
            return_per_sample: 是否返回每个样本的损失

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果return_per_sample为False，返回平均损失；
                否则返回(平均损失, 每个样本的损失)
        """
        # 提取偏好数据
        preferred_states = human_feedback_batch.get('preferred_states')
        rejected_states = human_feedback_batch.get('rejected_states')

        if preferred_states is None or rejected_states is None:
            if return_per_sample:
                return torch.tensor(0.0, device=self.device), None
            else:
                return torch.tensor(0.0, device=self.device)

        # 将状态转换为张量
        preferred_states = torch.FloatTensor(preferred_states).to(self.device)
        rejected_states = torch.FloatTensor(rejected_states).to(self.device)

        # 获取状态表示
        preferred_hidden = self.model.representation(preferred_states)
        rejected_hidden = self.model.representation(rejected_states)

        # 预测价值
        if self.use_distributional_value:
            # 如果使用分布式价值头，获取风险敏感价值
            preferred_value_logits = self.model.distributional_value_head(preferred_hidden)
            rejected_value_logits = self.model.distributional_value_head(rejected_hidden)

            preferred_values = self.model.distributional_value_head.compute_risk_sensitive_value(
                preferred_value_logits,
                alpha=self.risk_alpha,
                beta=self.risk_beta
            )

            rejected_values = self.model.distributional_value_head.compute_risk_sensitive_value(
                rejected_value_logits,
                alpha=self.risk_alpha,
                beta=self.risk_beta
            )
        else:
            # 使用标准预测网络
            _, preferred_values = self.model.predict(preferred_hidden)
            _, rejected_values = self.model.predict(rejected_hidden)

        # 计算偏好损失（使用Bradley-Terry模型）
        # 目标：使preferred_values > rejected_values
        per_sample_loss = F.softplus(rejected_values - preferred_values)
        preference_loss = per_sample_loss.mean()

        if return_per_sample:
            return preference_loss, per_sample_loss
        else:
            return preference_loss

    def _calculate_feedback_score_loss(self, human_feedback_batch: Dict[str, Any], return_per_sample: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        计算基于人类反馈评分的损失

        Args:
            human_feedback_batch: 人类反馈数据批次
            return_per_sample: 是否返回每个样本的损失

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果return_per_sample为False，返回平均损失；
                否则返回(平均损失, 每个样本的损失)
        """
        # 提取反馈评分数据
        states = human_feedback_batch.get('states')
        feedback_scores = human_feedback_batch.get('feedback_scores')

        if states is None or feedback_scores is None:
            if return_per_sample:
                return torch.tensor(0.0, device=self.device), None
            else:
                return torch.tensor(0.0, device=self.device)

        # 将数据转换为张量
        states = torch.FloatTensor(states).to(self.device)
        feedback_scores = torch.FloatTensor(feedback_scores).to(self.device)

        # 获取状态表示
        hidden_states = self.model.representation(states)

        # 预测价值
        if self.use_distributional_value:
            # 如果使用分布式价值头，获取风险敏感价值
            value_logits = self.model.distributional_value_head(hidden_states)

            predicted_values = self.model.distributional_value_head.compute_risk_sensitive_value(
                value_logits,
                alpha=self.risk_alpha,
                beta=self.risk_beta
            )
        else:
            # 使用标准预测网络
            _, predicted_values = self.model.predict(hidden_states)

        # 计算每个样本的MSE损失
        predicted_values = predicted_values.squeeze()
        per_sample_loss = (predicted_values - feedback_scores) ** 2

        # 计算平均损失
        feedback_loss = per_sample_loss.mean()

        if return_per_sample:
            return feedback_loss, per_sample_loss
        else:
            return feedback_loss

    def _calculate_imitation_loss(self, human_feedback_batch: Dict[str, Any], return_per_sample: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        计算基于人类动作模仿的损失（行为克隆）

        Args:
            human_feedback_batch: 人类反馈数据批次
            return_per_sample: 是否返回每个样本的损失

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
                如果return_per_sample为False，返回平均损失；
                否则返回(平均损失, 每个样本的损失)
        """
        # 提取模仿数据
        states = human_feedback_batch.get('states')
        human_actions = human_feedback_batch.get('human_actions')

        if states is None or human_actions is None:
            if return_per_sample:
                return torch.tensor(0.0, device=self.device), None
            else:
                return torch.tensor(0.0, device=self.device)

        # 将数据转换为张量
        states = torch.FloatTensor(states).to(self.device)
        human_actions = torch.LongTensor(human_actions).to(self.device)

        # 获取状态表示
        hidden_states = self.model.representation(states)

        # 预测策略
        policy_logits, _ = self.model.predict(hidden_states)

        # 计算每个样本的交叉熵损失
        if return_per_sample:
            # 使用F.cross_entropy的reduction='none'选项获取每个样本的损失
            per_sample_loss = F.cross_entropy(policy_logits, human_actions, reduction='none')
            imitation_loss = per_sample_loss.mean()
            return imitation_loss, per_sample_loss
        else:
            # 计算平均交叉熵损失
            imitation_loss = F.cross_entropy(policy_logits, human_actions)
            return imitation_loss

    def _create_scheduler(self, lr_scheduler):
        """
        创建学习率调度器

        Args:
            lr_scheduler (str): 学习率调度器类型

        Returns:
            torch.optim.lr_scheduler: 学习率调度器实例
        """
        if lr_scheduler == 'step':
            return torch.optim.lr_scheduler.StepLR(self.optimizer, step_size=10000, gamma=0.1)
        elif lr_scheduler == 'cosine':
            return torch.optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=100000)
        else:
            return None

    def set_belief_state_usage(self, use_belief_state: bool, belief_dim: int = 54,
                             use_belief_attention: bool = True, belief_attention_heads: int = 4,
                             use_residual_belief: bool = True, use_gating_mechanism: bool = True):
        """
        设置是否使用信念状态及其相关配置

        Args:
            use_belief_state (bool): 是否使用信念状态
            belief_dim (int, optional): 信念状态维度. Defaults to 54.
            use_belief_attention (bool, optional): 是否使用注意力机制. Defaults to True.
            belief_attention_heads (int, optional): 注意力头数. Defaults to 4.
            use_residual_belief (bool, optional): 是否使用残差连接. Defaults to True.
            use_gating_mechanism (bool, optional): 是否使用门控机制. Defaults to True.
        """
        self.use_belief_state = use_belief_state

        # 更新模型的信念状态设置
        if hasattr(self.model, 'use_belief_state'):
            self.model.use_belief_state = use_belief_state
            self.model.residual_belief = use_residual_belief

            # 如果模型没有信念处理网络，但现在需要使用信念状态，则创建信念处理网络
            if use_belief_state and (not hasattr(self.model, 'belief_processor') or
                                     (use_belief_attention and not hasattr(self.model, 'belief_attention'))):
                # 获取模型参数
                hidden_dim = getattr(self.model, 'hidden_dim', 256)
                state_dim = getattr(self.model, 'state_dim', 64)
                action_shape = getattr(self.model, 'action_shape', (1,))

                # 创建信念处理网络
                self.model.belief_dim = belief_dim

                if use_belief_attention:
                    # 使用注意力机制
                    # 计算注意力参数
                    head_dim = hidden_dim // belief_attention_heads

                    # 创建投影层
                    self.model.belief_query_proj = nn.Linear(state_dim, head_dim * belief_attention_heads).to(self.device)
                    self.model.belief_key_proj = nn.Linear(belief_dim, head_dim * belief_attention_heads).to(self.device)
                    self.model.belief_value_proj = nn.Linear(belief_dim, head_dim * belief_attention_heads).to(self.device)

                    # 创建多头注意力层
                    self.model.belief_attention = nn.MultiheadAttention(
                        embed_dim=head_dim * belief_attention_heads,
                        num_heads=belief_attention_heads,
                        batch_first=True
                    ).to(self.device)

                    belief_output_dim = head_dim * belief_attention_heads
                else:
                    # 使用标准信念处理器
                    self.model.belief_processor = nn.Sequential(
                        nn.Linear(belief_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(0.1),
                        nn.Linear(hidden_dim, hidden_dim // 2),
                        nn.ReLU(),
                        nn.Linear(hidden_dim // 2, hidden_dim // 4)
                    ).to(self.device)

                    belief_output_dim = hidden_dim // 4

                # 如果使用门控机制
                if use_gating_mechanism:
                    # 创建融合门控
                    self.model.fusion_gate = nn.Linear(state_dim + belief_output_dim, belief_output_dim).to(self.device)

                # 组合处理器
                self.model.combined_processor = nn.Sequential(
                    nn.Linear(state_dim + belief_output_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(hidden_dim, state_dim)
                ).to(self.device)

                # 修改预测网络以支持信念状态
                if self.use_distributional_value:
                    # 如果同时使用信念状态和分布式价值头
                    self.model.prediction_network_with_belief_policy = nn.Sequential(
                        nn.Linear(state_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(0.1),
                        nn.Linear(hidden_dim, action_shape[0])
                    ).to(self.device)

                    # 分布式价值头
                    self.model.prediction_network_with_belief_value = DistributionalValueHead(
                        input_dim=state_dim,
                        hidden_dim=hidden_dim,
                        value_support_size=self.value_support_size,
                        value_min=self.value_min,
                        value_max=self.value_max,
                        device=self.device
                    ).to(self.device)
                else:
                    # 使用标准预测网络
                    self.model.prediction_network_with_belief = nn.Sequential(
                        nn.Linear(state_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(0.1),
                        nn.Linear(hidden_dim, action_shape[0] + 1)  # 输出动作概率和价值
                    ).to(self.device)

            # 同样更新目标模型
            if hasattr(self.target_model, 'use_belief_state'):
                self.target_model.use_belief_state = use_belief_state
                self.target_model.residual_belief = use_residual_belief

                # 如果目标模型需要信念处理网络，则复制主模型的网络
                if use_belief_state:
                    self.target_model.belief_dim = self.model.belief_dim

                    # 复制注意力相关组件
                    if hasattr(self.model, 'belief_attention'):
                        self.target_model.belief_query_proj = copy.deepcopy(self.model.belief_query_proj).to(self.device)
                        self.target_model.belief_key_proj = copy.deepcopy(self.model.belief_key_proj).to(self.device)
                        self.target_model.belief_value_proj = copy.deepcopy(self.model.belief_value_proj).to(self.device)
                        self.target_model.belief_attention = copy.deepcopy(self.model.belief_attention).to(self.device)

                    # 复制标准处理器
                    if hasattr(self.model, 'belief_processor'):
                        self.target_model.belief_processor = nn.Sequential(
                            *[copy.deepcopy(module) for module in self.model.belief_processor]
                        ).to(self.device)

                    # 复制门控机制
                    if hasattr(self.model, 'fusion_gate'):
                        self.target_model.fusion_gate = copy.deepcopy(self.model.fusion_gate).to(self.device)

                    # 复制组合处理器
                    if hasattr(self.model, 'combined_processor'):
                        self.target_model.combined_processor = nn.Sequential(
                            *[copy.deepcopy(module) for module in self.model.combined_processor]
                        ).to(self.device)

                    # 复制预测网络
                    if hasattr(self.model, 'prediction_network_with_belief'):
                        self.target_model.prediction_network_with_belief = nn.Sequential(
                            *[copy.deepcopy(module) for module in self.model.prediction_network_with_belief]
                        ).to(self.device)

                    # 复制分布式价值头
                    if hasattr(self.model, 'prediction_network_with_belief_policy'):
                        self.target_model.prediction_network_with_belief_policy = nn.Sequential(
                            *[copy.deepcopy(module) for module in self.model.prediction_network_with_belief_policy]
                        ).to(self.device)

                    if hasattr(self.model, 'prediction_network_with_belief_value'):
                        self.target_model.prediction_network_with_belief_value = copy.deepcopy(
                            self.model.prediction_network_with_belief_value
                        ).to(self.device)

        # 重新创建MCTS搜索器，使用更新后的设置
        self.mcts = self._create_mcts(
            num_simulations=self.num_simulations,
            discount=self.discount,
            dirichlet_alpha=self.dirichlet_alpha,
            exploration_fraction=self.exploration_fraction,
            pb_c_base=self.pb_c_base,
            pb_c_init=self.pb_c_init,
            use_belief_state=self.use_belief_state
        )

    def act(self, state: Union[State, np.ndarray], temperature: float = 1.0,
            belief_trackers: Optional[Dict[str, Any]] = None,
            opponent_model_priors: Optional[Dict[str, Dict[int, float]]] = None,
            dynamic_budget: Optional[Dict[str, Any]] = None,
            explain: bool = False,
            force_exploration: bool = False) -> Union[Tuple[int, Dict[int, float]], Tuple[int, Dict[int, float], Dict[str, Any]]]:
        """
        使用MCTS选择动作

        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察
            temperature (float, optional): 温度参数，控制探索. Defaults to 1.0.
            belief_trackers (Optional[Dict[str, Any]], optional): 信念追踪器字典，键为玩家ID. Defaults to None.
            opponent_model_priors (Optional[Dict[str, Dict[int, float]]], optional): 对手模型先验，键为玩家ID，值为动作到概率的映射. Defaults to None.
            dynamic_budget (Optional[Dict[str, Any]], optional): 动态计算预算配置，包含num_simulations、max_time_ms等. Defaults to None.
            explain (bool, optional): 是否返回解释信息. Defaults to False.

        Returns:
            Union[Tuple[int, Dict[int, float]], Tuple[int, Dict[int, float], Dict[str, Any]]]:
                如果explain=False，返回(选择的动作ID, 动作概率分布)
                如果explain=True，返回(选择的动作ID, 动作概率分布, 解释数据)
        """
        # 保存当前训练状态
        representation_training = self.model.representation_network.training
        dynamics_training = self.model.dynamics_network.training
        prediction_training = self.model.prediction_network.training

        # 切换到评估模式
        self.model.representation_network.eval()
        self.model.dynamics_network.eval()
        self.model.prediction_network.eval()

        explanation_data = {} if explain else None

        try:
            # 获取状态
            if isinstance(state, State):
                observation = state.get_observation()
                legal_actions = state.get_legal_actions()
            else:
                observation = state
                legal_actions = None

            # 创建动作掩码
            action_mask = None
            if legal_actions is not None:
                action_mask = [1 if i in legal_actions else 0 for i in range(self.action_shape[0])]

            # 构建上下文信息
            context = {}
            if isinstance(state, State):
                # 提取游戏阶段
                if hasattr(state, 'get_game_stage'):
                    context['game_stage'] = state.get_game_stage()
                elif hasattr(state, 'get_phase'):
                    game_phase = state.get_phase()
                    if game_phase in ['bidding', 'card_play_init']:
                        context['game_stage'] = 'early'
                    elif game_phase == 'card_play':
                        # 根据剩余手牌数来判断中后期
                        if hasattr(state, 'get_hand_count'):
                            hand_count = state.get_hand_count()
                            if hand_count <= 5:
                                context['game_stage'] = 'late'
                            else:
                                context['game_stage'] = 'mid'
                        else:
                            context['game_stage'] = 'mid'

                # 提取玩家角色
                if hasattr(state, 'get_player_role'):
                    context['player_role'] = state.get_player_role()
                elif hasattr(state, 'landlord') and hasattr(state, 'current_player'):
                    context['player_role'] = 'landlord' if state.current_player == state.landlord else 'farmer'

                # 提取手牌数量
                if hasattr(state, 'get_hand_count'):
                    context['cards_left'] = state.get_hand_count()
                elif hasattr(state, 'hands') and hasattr(state, 'current_player'):
                    if isinstance(state.hands, list) and len(state.hands) > state.current_player:
                        context['cards_left'] = len(state.hands[state.current_player])

            # 使用动态预算分配器（如果启用）
            if self.use_dynamic_budget and self.dynamic_budget_allocator is not None:
                if explain:
                    budget_info, budget_explanation = self.dynamic_budget_allocator.allocate_budget(
                        state, context, explain=True
                    )
                    explanation_data['budget_allocation'] = budget_explanation
                else:
                    budget_info = self.dynamic_budget_allocator.allocate_budget(state, context)

                # 更新动态预算配置
                if dynamic_budget is None:
                    dynamic_budget = {}
                dynamic_budget.update(budget_info)

            # 计算动态风险系数并重新创建MCTS搜索器
            dynamic_risk_beta = self.risk_beta
            if dynamic_budget is not None and 'game_stage' in dynamic_budget:
                stage = dynamic_budget['game_stage']
                if stage == 'early':
                    dynamic_risk_beta = self.risk_beta * 0.8
                elif stage == 'mid':
                    dynamic_risk_beta = self.risk_beta
                elif stage == 'late':
                    dynamic_risk_beta = self.risk_beta * 1.2
            elif 'game_stage' in context:
                stage = context['game_stage']
                if stage == 'early':
                    dynamic_risk_beta = self.risk_beta * 0.8
                elif stage == 'mid':
                    dynamic_risk_beta = self.risk_beta
                elif stage == 'late':
                    dynamic_risk_beta = self.risk_beta * 1.2

            if self.use_opponent_distribution_switcher and self.opponent_switcher is not None and self.distribution_weights:
                opponent = self.get_opponent(state, legal_actions)
                weight = self.distribution_weights.get(opponent, 1.0) if opponent is not None else 1.0
                dynamic_risk_beta *= weight

            self.mcts = self._create_mcts(
                num_simulations=self.num_simulations,
                discount=self.discount,
                dirichlet_alpha=self.dirichlet_alpha,
                exploration_fraction=self.exploration_fraction,
                pb_c_base=self.pb_c_base,
                pb_c_init=self.pb_c_init,
                use_belief_state=self.use_belief_state,
                risk_beta=dynamic_risk_beta,
                use_key_moment_detector=self.use_key_moment_detector,
                key_moment_detector=self.key_moment_detector
            )

            # 使用MCTS进行预测，融合信念状态和对手模型先验
            if explain:
                visit_counts, action_probs, mcts_explanation = self.mcts.run(
                    observation,
                    self.model,
                    temperature=temperature,
                    actions_mask=action_mask,
                    belief_trackers=belief_trackers if self.use_belief_state else None,
                    opponent_model_priors=opponent_model_priors,
                    dynamic_budget=dynamic_budget,
                    explain=True,
                    force_exploration=force_exploration
                )
                explanation_data['mcts'] = mcts_explanation
            else:
                visit_counts, action_probs = self.mcts.run(
                    observation,
                    self.model,
                    temperature=temperature,
                    actions_mask=action_mask,
                    belief_trackers=belief_trackers if self.use_belief_state else None,
                    opponent_model_priors=opponent_model_priors,
                    dynamic_budget=dynamic_budget,
                    explain=False,
                    force_exploration=force_exploration
                )

            # 选择动作
            if temperature == 0:
                # 确定性选择：选择访问次数最多的动作
                action = max(visit_counts.items(), key=lambda x: x[1])[0]
            else:
                # 随机选择：根据概率分布采样
                actions = list(action_probs.keys())
                probs = list(action_probs.values())
                action = np.random.choice(actions, p=probs)

            # 返回结果
            if explain:
                return action, action_probs, explanation_data
            else:
                return action, action_probs
        finally:
            # 恢复原始训练状态
            self.model.representation_network.train(representation_training)
            self.model.dynamics_network.train(dynamics_training)
            self.model.prediction_network.train(prediction_training)


class EfficientZeroAMP(EfficientZero):
    """
    支持混合精度训练的EfficientZero算法

    扩展EfficientZero算法，添加混合精度训练支持，提高训练速度和减少内存使用。
    """

    def __init__(
        self,
        state_shape: Tuple[int, ...],
        action_shape: Tuple[int, ...],
        hidden_dim: int = 256,
        state_dim: int = 64,
        use_resnet: bool = True,
        projection_dim: int = 256,
        prediction_dim: int = 128,
        value_prefix_length: int = 5,
        num_simulations: int = 50,
        discount: float = 0.997,
        dirichlet_alpha: float = 0.25,
        exploration_fraction: float = 0.25,
        pb_c_base: int = 19652,
        pb_c_init: float = 1.25,
        replay_buffer_size: int = 100000,
        batch_size: int = 128,
        num_unroll_steps: int = 5,
        td_steps: int = 10,
        value_loss_weight: float = 1.0,
        policy_loss_weight: float = 1.0,
        consistency_loss_weight: float = 1.0,
        self_supervised_loss_weight: float = 1.0,
        # 添加缺失的EWC参数
        use_ewc: bool = False,  # 是否使用EWC算法
        ewc_lambda: float = 100.0,  # EWC正则化系数
        ewc_state_path: Optional[str] = None,  # EWC状态保存路径
        learning_rate: float = 0.001,
        weight_decay: float = 1e-4,
        lr_scheduler: str = 'step',
        device: str = None,
        use_mixed_precision: bool = True,
        dynamic_loss_scaling: bool = True
    ):
        """
        初始化支持混合精度训练的EfficientZero算法

        Args:
            state_shape (Tuple[int, ...]): 状态空间形状
            action_shape (Tuple[int, ...]): 动作空间形状
            hidden_dim (int, optional): 隐藏层维度. Defaults to 256.
            state_dim (int, optional): 隐藏状态维度. Defaults to 64.
            use_resnet (bool, optional): 是否使用残差网络架构. Defaults to True.
            projection_dim (int, optional): 投影维度. Defaults to 256.
            prediction_dim (int, optional): 预测网络隐藏层维度. Defaults to 128.
            value_prefix_length (int, optional): 值前缀长度. Defaults to 5.
            num_simulations (int, optional): MCTS模拟次数. Defaults to 50.
            discount (float, optional): 折扣因子. Defaults to 0.997.
            dirichlet_alpha (float, optional): Dirichlet噪声参数. Defaults to 0.25.
            exploration_fraction (float, optional): 探索比例. Defaults to 0.25.
            pb_c_base (int, optional): PUCT算法基数. Defaults to 19652.
            pb_c_init (float, optional): PUCT算法初始值. Defaults to 1.25.
            replay_buffer_size (int, optional): 回放缓冲区大小. Defaults to 100000.
            batch_size (int, optional): 批次大小. Defaults to 128.
            num_unroll_steps (int, optional): 展开步数. Defaults to 5.
            td_steps (int, optional): 时差学习步数. Defaults to 10.
            value_loss_weight (float, optional): 值函数损失权重. Defaults to 1.0.
            policy_loss_weight (float, optional): 策略损失权重. Defaults to 1.0.
            consistency_loss_weight (float, optional): 一致性损失权重. Defaults to 1.0.
            self_supervised_loss_weight (float, optional): 自监督损失权重. Defaults to 1.0.
            use_ewc (bool, optional): 是否使用EWC算法. Defaults to False.
            ewc_lambda (float, optional): EWC正则化系数. Defaults to 100.0.
            ewc_state_path (Optional[str], optional): EWC状态保存路径. Defaults to None.
            learning_rate (float, optional): 学习率. Defaults to 0.001.
            weight_decay (float, optional): 权重衰减. Defaults to 1e-4.
            lr_scheduler (str, optional): 学习率调度器. Defaults to 'step'.
            device (str, optional): 计算设备. Defaults to None.
            use_mixed_precision (bool, optional): 是否使用混合精度训练. Defaults to True.
            dynamic_loss_scaling (bool, optional): 是否使用动态损失缩放. Defaults to True.
        """
        # 调用父类初始化
        super().__init__(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=hidden_dim,
            state_dim=state_dim,
            use_resnet=use_resnet,
            projection_dim=projection_dim,
            prediction_dim=prediction_dim,
            value_prefix_length=value_prefix_length,
            num_simulations=num_simulations,
            discount=discount,
            dirichlet_alpha=dirichlet_alpha,
            exploration_fraction=exploration_fraction,
            pb_c_base=pb_c_base,
            pb_c_init=pb_c_init,
            replay_buffer_size=replay_buffer_size,
            batch_size=batch_size,
            num_unroll_steps=num_unroll_steps,
            td_steps=td_steps,
            value_loss_weight=value_loss_weight,
            policy_loss_weight=policy_loss_weight,
            consistency_loss_weight=consistency_loss_weight,
            self_supervised_loss_weight=self_supervised_loss_weight,
            use_ewc=use_ewc,  # 传递EWC参数
            ewc_lambda=ewc_lambda,  # 传递EWC参数
            ewc_state_path=ewc_state_path,  # 传递EWC参数
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            lr_scheduler=lr_scheduler,
            device=device,
            amp_enabled=True  # 强制启用AMP
        )

        # 混合精度训练设置
        self.use_mixed_precision = use_mixed_precision and torch.cuda.is_available()
        self.dynamic_loss_scaling = dynamic_loss_scaling

        # 创建梯度缩放器（用于混合精度训练）
        self.scaler = GradScaler(device='cuda', enabled=self.use_mixed_precision)

        # 记录混合精度训练状态
        self.amp_enabled = self.use_mixed_precision

    def update(self, experience: Union[Experience, Batch]) -> Dict[str, float]:
        """
        使用混合精度训练更新模型

        Args:
            experience (Union[Experience, Batch]): 单个经验或经验批次

        Returns:
            Dict[str, float]: 更新指标，如损失值等
        """
        # 如果是单个经验，先存入回放缓冲区
        if isinstance(experience, Experience):
            self.replay_buffer.add(experience)

            # 如果回放缓冲区样本不足，则跳过更新
            if len(self.replay_buffer) < self.batch_size:
                return {}

            # 从回放缓冲区采样批次数据
            batch = self.replay_buffer.sample(self.batch_size)
        else:
            batch = experience

        # 将数据移动到设备上
        states = torch.FloatTensor(np.array([exp.state for exp in batch])).to(self.device)
        actions = torch.LongTensor(np.array([exp.action.action_id for exp in batch])).to(self.device)
        rewards = torch.FloatTensor(np.array([exp.reward for exp in batch])).to(self.device)
        next_states = torch.FloatTensor(np.array([exp.next_state for exp in batch])).to(self.device)
        dones = torch.FloatTensor(np.array([float(exp.done) for exp in batch])).to(self.device)

        # 清零梯度
        self.optimizer.zero_grad()

        # 使用混合精度训练
        with autocast(device_type='cuda', enabled=self.amp_enabled):
            # 训练MuZero模型
            self.model.representation_network.train()
            self.model.dynamics_network.train()
            self.model.prediction_network.train()

            # 获取初始隐藏状态
            hidden_states = self.model.representation_network(states)

            # 预测策略和价值
            if self.use_distributional_value:
                # 如果使用分布式价值头，分别获取策略和价值
                policy_logits = self.model.policy_head(hidden_states)
                value_logits = self.model.distributional_value_head(hidden_states)

                # 计算风险敏感价值（用于记录）
                values = self.model.distributional_value_head.compute_risk_sensitive_value(
                    value_logits,
                    alpha=self.risk_alpha,
                    beta=self.risk_beta
                )
            else:
                # 使用标准预测网络
                policy_logits, values = self.model.prediction_network(hidden_states)

            # 计算目标值 (n步回报)
            target_values = self._compute_target_values(rewards, dones, next_states)

            # 计算策略目标 (MCTS访问计数)
            target_policies = self._compute_target_policies(states)

            # 初始化损失
            value_loss = 0.0
            policy_loss = 0.0
            reward_loss = 0.0
            consistency_loss = 0.0
            self_supervised_loss = 0.0

            # 初始化损失缩放因子
            gradient_scale = 1.0 / self.num_unroll_steps

            # 计算初始状态的策略损失
            policy_loss += F.cross_entropy(policy_logits, target_policies)

            # 计算初始状态的值损失
            if self.use_distributional_value:
                # 使用分布式价值头计算损失
                value_loss += self._compute_distributional_value_loss(value_logits, target_values)
            else:
                # 使用标准MSE损失
                value_loss += F.mse_loss(values, target_values)

            # 展开模型动力学
            current_hidden_states = hidden_states
            for step_idx in range(self.num_unroll_steps):
                # 选择动作
                action_batch = actions[:, step_idx] if actions.shape[1] > step_idx else actions[:, -1]

                # 预测下一个状态和奖励
                next_hidden_states, predicted_rewards = self.model.dynamics_network(current_hidden_states, action_batch)

                # 预测策略和价值
                if self.use_distributional_value:
                    # 如果使用分布式价值头，分别获取策略和价值
                    next_policy_logits = self.model.policy_head(next_hidden_states)
                    next_value_logits = self.model.distributional_value_head(next_hidden_states)

                    # 计算风险敏感价值（用于记录）
                    next_values = self.model.distributional_value_head.compute_risk_sensitive_value(
                        next_value_logits,
                        alpha=self.risk_alpha,
                        beta=self.risk_beta
                    )
                else:
                    # 使用标准预测网络
                    next_policy_logits, next_values = self.model.prediction_network(next_hidden_states)

                # 计算目标值和策略
                step_target_values = target_values[:, step_idx:step_idx+1] if step_idx < target_values.shape[1] else target_values[:, -1:]
                step_target_policies = target_policies[:, step_idx:step_idx+1] if step_idx < target_policies.shape[1] else target_policies[:, -1:]
                step_rewards = rewards[:, step_idx:step_idx+1] if step_idx < rewards.shape[1] else rewards[:, -1:]

                # 计算策略损失
                policy_loss += gradient_scale * F.cross_entropy(next_policy_logits, step_target_policies)

                # 计算值损失
                if self.use_distributional_value:
                    # 使用分布式价值头计算损失
                    value_loss += gradient_scale * self._compute_distributional_value_loss(next_value_logits, step_target_values)
                else:
                    # 使用标准MSE损失
                    value_loss += gradient_scale * F.mse_loss(next_values, step_target_values)

                # 计算奖励损失
                reward_loss += gradient_scale * F.mse_loss(predicted_rewards, step_rewards)

                # 计算一致性损失（使用目标网络）
                if self.consistency_loss_weight > 0:
                    with torch.no_grad():
                        target_hidden_states = self.target_model.representation_network(next_states)
                    consistency_loss += gradient_scale * F.mse_loss(next_hidden_states, target_hidden_states)

                # 计算自监督损失（如果有自监督模块）
                if hasattr(self.model, 'self_supervised_module') and self.self_supervised_loss_weight > 0:
                    p1, p2, z1, z2 = self.model.self_supervised_module(current_hidden_states, next_hidden_states)
                    step_self_supervised_loss = self.model.self_supervised_module.compute_loss(p1, p2, z1, z2)
                    self_supervised_loss += gradient_scale * step_self_supervised_loss

                # 更新当前状态
                current_hidden_states = next_hidden_states

            # 组合所有损失
            total_loss = (
                self.value_loss_weight * value_loss +
                self.policy_loss_weight * policy_loss +
                reward_loss +
                self.consistency_loss_weight * consistency_loss +
                self.self_supervised_loss_weight * self_supervised_loss
            )

        # 使用梯度缩放器计算梯度
        self.scaler.scale(total_loss).backward()

        # 使用梯度缩放器更新参数
        self.scaler.step(self.optimizer)

        # 更新梯度缩放因子
        self.scaler.update()

        # 更新学习率
        if self.scheduler is not None:
            self.scheduler.step()

        # 定期更新目标网络
        self._update_target_network()

        # 返回损失指标
        return {
            'value_loss': value_loss.item(),
            'policy_loss': policy_loss.item(),
            'reward_loss': reward_loss.item(),
            'consistency_loss': consistency_loss.item(),
            'self_supervised_loss': self_supervised_loss.item(),
            'total_loss': total_loss.item(),
            'learning_rate': self.optimizer.param_groups[0]['lr']
        }

    def predict(self, state: Union[State, np.ndarray]) -> Tuple[List[float], float]:
        """
        使用混合精度推理预测状态的动作概率分布和价值

        Args:
            state (Union[State, np.ndarray]): 游戏状态或观察

        Returns:
            Tuple[List[float], float]: 动作概率分布、状态价值
        """
        # 将状态转换为张量
        if isinstance(state, State):
            state_tensor = torch.FloatTensor(state.vectorize()).unsqueeze(0).to(self.device)
        else:
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        # 使用混合精度推理
        with torch.no_grad():
            with autocast(device_type='cuda', enabled=self.amp_enabled):
                # 获取隐藏状态
                hidden_state = self.model.representation_network(state_tensor)

                # 预测策略和价值
                if self.use_distributional_value:
                    # 如果使用分布式价值头，分别获取策略和价值
                    policy_logits = self.model.policy_head(hidden_state)
                    value_logits = self.model.distributional_value_head(hidden_state)

                    # 计算风险敏感价值
                    value = self.model.distributional_value_head.compute_risk_sensitive_value(
                        value_logits,
                        alpha=self.risk_alpha,
                        beta=self.risk_beta
                    ).item()
                else:
                    # 使用标准预测网络
                    policy_logits, value = self.model.prediction_network(hidden_state)
                    value = value.item()

                # 计算动作概率
                policy = F.softmax(policy_logits, dim=1).squeeze(0).cpu().numpy().tolist()

        return policy, value

    def predict_next_state(self, state: Union[State, np.ndarray], action: Action) -> Tuple[Union[State, np.ndarray], float]:
        """
        使用混合精度推理预测下一个状态和奖励

        Args:
            state (Union[State, np.ndarray]): 当前状态
            action (Action): 动作

        Returns:
            Tuple[Union[State, np.ndarray], float]: 下一个状态和奖励
        """
        # 将状态和动作转换为张量
        if isinstance(state, State):
            state_tensor = torch.FloatTensor(state.vectorize()).unsqueeze(0).to(self.device)
        else:
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        action_tensor = torch.LongTensor([action.action_id]).to(self.device)

        # 使用混合精度推理
        with torch.no_grad():
            with autocast(device_type='cuda', enabled=self.amp_enabled):
                # 获取隐藏状态
                hidden_state = self.model.representation_network(state_tensor)

                # 预测下一个状态和奖励
                next_hidden_state, reward = self.model.dynamics_network(hidden_state, action_tensor)

                # 将隐藏状态转换为观察状态（如果需要）
                # 注意：这里的实现可能需要根据具体环境进行调整
                next_state = next_hidden_state.squeeze(0).cpu().numpy()
                reward = reward.item()

        # 如果原始状态是State对象，则尝试返回State对象
        if isinstance(state, State):
            try:
                next_state_obj = state.clone()
                next_state_obj.update(next_state)
                return next_state_obj, reward
            except:
                # 如果无法创建State对象，则返回原始数组
                return next_state, reward

        return next_state, reward

    def save(self, path: str) -> None:
        """
        保存模型

        Args:
            path (str): 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存模型状态
        state_dict = {
            'model': self.model.state_dict(),
            'target_model': self.target_model.state_dict(),
            'optimizer': self.optimizer.state_dict(),
            'scheduler': self.scheduler.state_dict() if self.scheduler is not None else None,
            'use_ewc': self.use_ewc,
            'ewc_lambda': self.ewc_lambda,
            'scaler': getattr(self, 'scaler', None),
            'amp_enabled': getattr(self, 'amp_enabled', False),
            'use_mixed_precision': getattr(self, 'use_mixed_precision', False),
            'dynamic_loss_scaling': getattr(self, 'dynamic_loss_scaling', False)
        }

        # 保存到文件
        torch.save(state_dict, path)

        # 如果启用了EWC并且实例已初始化，保存EWC状态
        if self.use_ewc and self.ewc is not None:
            ewc_path = f"{path}_ewc"
            try:
                self.ewc.save_state(ewc_path)
                logger.info(f"已保存EWC状态: {ewc_path}")
            except Exception as e:
                logger.warning(f"保存EWC状态时出错: {e}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path (str): 加载路径
        """
        # 检查文件是否存在
        if not os.path.exists(path):
            raise FileNotFoundError(f"Model file not found: {path}")

        # 加载模型状态
        state_dict = torch.load(path, map_location=self.device)

        # 加载模型参数
        self.model.load_state_dict(state_dict['model'])
        self.target_model.load_state_dict(state_dict['target_model'])
        self.optimizer.load_state_dict(state_dict['optimizer'])

        # 加载学习率调度器（如果有）
        if state_dict['scheduler'] is not None and self.scheduler is not None:
            self.scheduler.load_state_dict(state_dict['scheduler'])

        # 加载EWC参数（如果有）
        if 'use_ewc' in state_dict:
            self.use_ewc = state_dict['use_ewc']
        if 'ewc_lambda' in state_dict:
            self.ewc_lambda = state_dict['ewc_lambda']

        # 加载EWC状态（如果启用了EWC）
        if self.use_ewc:
            ewc_path = f"{path}_ewc"
            if os.path.exists(f"{ewc_path}_fisher.pt"):
                # 如果EWC实例尚未创建，先创建一个空实例
                if self.ewc is None:
                    self.ewc = EWC(
                        model=self.model,
                        dataloader=[],  # 空数据集，仅用于初始化
                        fisher_importance=self.ewc_lambda,
                        device=self.device
                    )

                # 加载EWC状态
                try:
                    success = self.ewc.load_state(ewc_path)
                    if success:
                        logger.info(f"已加载EWC状态: {ewc_path}")
                    else:
                        logger.warning(f"加载EWC状态失败: {ewc_path}")
                except Exception as e:
                    logger.warning(f"加载EWC状态时出错: {e}")

        # 加载梯度缩放器状态（如果有）
        if 'scaler' in state_dict and state_dict['scaler'] is not None and hasattr(self, 'scaler'):
            self.scaler.load_state_dict(state_dict['scaler'])

        # 加载混合精度训练设置（如果有）
        if 'amp_enabled' in state_dict and hasattr(self, 'amp_enabled'):
            self.amp_enabled = state_dict['amp_enabled']
        if 'use_mixed_precision' in state_dict and hasattr(self, 'use_mixed_precision'):
            self.use_mixed_precision = state_dict['use_mixed_precision']
        if 'dynamic_loss_scaling' in state_dict and hasattr(self, 'dynamic_loss_scaling'):
            self.dynamic_loss_scaling = state_dict['dynamic_loss_scaling']


def test_importance_weighted_training():
    """
    测试重要性加权训练功能

    这个函数用于测试EfficientZero的重要性加权训练功能。
    它会创建一个关键决策点检测器和一个EfficientZero模型，
    然后使用关键决策点检测器对样本进行加权训练。
    """
    import time
    import torch
    import numpy as np
    from cardgame_ai.games.doudizhu import DouDizhuEnvironment
    from cardgame_ai.core.base import Action, Experience
    from cardgame_ai.algorithms.components.key_moment_detector import KeyMomentDetector

    # 创建环境
    env = DouDizhuEnvironment()

    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)

    # 创建关键决策点检测器
    key_moment_detector = KeyMomentDetector(
        state_dim=observation_shape[0],
        hidden_dims=[128, 64, 32],
        output_dim=1,
        use_attention=True,
        use_history=True
    )

    # 创建两个模型，一个使用重要性加权训练，另一个不使用
    model_weighted = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=64,
        state_dim=32,
        use_resnet=False,
        batch_size=32,
        num_simulations=10,
        use_importance_weighting=True,
        importance_weight_scale=2.0
    )

    model_no_weighted = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=64,
        state_dim=32,
        use_resnet=False,
        batch_size=32,
        num_simulations=10,
        use_importance_weighting=False
    )

    # 设置关键决策点检测器
    model_weighted.set_key_moment_detector(key_moment_detector)

    # 生成一些测试数据
    experiences = []
    state = env.reset()
    for _ in range(100):
        # 随机选择动作
        legal_actions = env.get_legal_actions(state)
        action = np.random.choice(legal_actions)
        action_obj = Action(action_id=action)

        # 执行动作
        next_state, reward, done, _ = env.step(action)

        # 创建经验
        exp = Experience(state, action_obj, reward, next_state, done)
        experiences.append(exp)

        # 更新状态
        state = next_state

        if done:
            state = env.reset()

    # 测试训练
    print("\n\n=== 测试重要性加权训练 ===")

    # 创建批次数据
    batch = {
        'observations': torch.FloatTensor(np.array([exp.state for exp in experiences[:32]])),
        'actions': torch.LongTensor(np.array([exp.action.action_id for exp in experiences[:32]])),
        'rewards': torch.FloatTensor(np.array([exp.reward for exp in experiences[:32]])),
        'next_observations': torch.FloatTensor(np.array([exp.next_state for exp in experiences[:32]])),
        'dones': torch.FloatTensor(np.array([float(exp.done) for exp in experiences[:32]]))
    }

    # 使用重要性加权训练
    losses_weighted = model_weighted.train(batch)
    print("\n使用重要性加权训练的损失:")
    for key, value in losses_weighted.items():
        print(f"  {key}: {value:.4f}")

    # 不使用重要性加权训练
    losses_no_weighted = model_no_weighted.train(batch)
    print("\n不使用重要性加权训练的损失:")
    for key, value in losses_no_weighted.items():
        print(f"  {key}: {value:.4f}")

    return {
        'weighted_losses': losses_weighted,
        'no_weighted_losses': losses_no_weighted
    }


def test_efficient_zero_amp():
    """
    测试EfficientZeroAMP的性能

    这个函数用于测试EfficientZeroAMP的性能，包括训练速度和内存使用。
    它会创建两个模型，一个使用混合精度训练，另一个不使用，
    然后比较它们的训练速度和内存使用。
    """
    import time
    import torch
    import numpy as np
    from cardgame_ai.games.doudizhu import DouDizhuEnvironment
    from cardgame_ai.core.base import Action, Experience

    # 创建环境
    env = DouDizhuEnvironment()

    # 获取观察和动作空间
    observation_shape = env.observation_space.shape
    action_shape = (env.action_space.n,)

    # 创建两个模型，一个使用混合精度训练，另一个不使用
    model_amp = EfficientZeroAMP(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=64,  # 小一点，加快测试速度
        state_dim=32,
        use_resnet=False,
        batch_size=32,
        num_simulations=10,
        use_mixed_precision=True
    )

    model_no_amp = EfficientZero(
        state_shape=observation_shape,
        action_shape=action_shape,
        hidden_dim=64,
        state_dim=32,
        use_resnet=False,
        batch_size=32,
        num_simulations=10
    )

    # 生成一些测试数据
    experiences = []
    state = env.reset()
    for _ in range(100):
        # 随机选择动作
        legal_actions = env.get_legal_actions(state)
        action = np.random.choice(legal_actions)
        action_obj = Action(action_id=action)

        # 执行动作
        next_state, reward, done, _ = env.step(action)

        # 创建经验
        exp = Experience(state, action_obj, reward, next_state, done)
        experiences.append(exp)

        # 更新状态
        state = next_state

        if done:
            state = env.reset()

    # 测试训练速度
    print("\n\n=== 测试训练速度 ===")

    # 测试使用混合精度训练的模型
    start_time = time.time()
    for _ in range(10):
        for exp in experiences:
            model_amp.update(exp)
    amp_time = time.time() - start_time
    print(f"\u4f7f用混合精度训练时间: {amp_time:.4f} 秒")

    # 测试不使用混合精度训练的模型
    start_time = time.time()
    for _ in range(10):
        for exp in experiences:
            model_no_amp.update(exp)
    no_amp_time = time.time() - start_time
    print(f"不使用混合精度训练时间: {no_amp_time:.4f} 秒")

    # 计算加速比
    speedup = no_amp_time / amp_time if amp_time > 0 else 0
    print(f"加速比: {speedup:.2f}x")

    # 测试内存使用
    print("\n=== 测试内存使用 ===")

    # 测试使用混合精度训练的模型
    torch.cuda.reset_peak_memory_stats()
    for _ in range(5):
        for exp in experiences:
            model_amp.update(exp)
    amp_memory = torch.cuda.max_memory_allocated() / (1024 ** 2)  # 转换为MB
    print(f"使用混合精度内存使用: {amp_memory:.2f} MB")

    # 测试不使用混合精度训练的模型
    torch.cuda.reset_peak_memory_stats()
    for _ in range(5):
        for exp in experiences:
            model_no_amp.update(exp)
    no_amp_memory = torch.cuda.max_memory_allocated() / (1024 ** 2)  # 转换为MB
    print(f"不使用混合精度内存使用: {no_amp_memory:.2f} MB")

    # 计算内存节省比例
    memory_saving = (no_amp_memory - amp_memory) / no_amp_memory * 100 if no_amp_memory > 0 else 0
    print(f"内存节省: {memory_saving:.2f}%")

    # 测试推理速度
    print("\n=== 测试推理速度 ===")

    # 生成测试状态
    test_states = [env.reset() for _ in range(100)]

    # 测试使用混合精度推理的模型
    start_time = time.time()
    for state in test_states:
        model_amp.predict(state)
    amp_inference_time = time.time() - start_time
    print(f"使用混合精度推理时间: {amp_inference_time:.4f} 秒")

    # 测试不使用混合精度推理的模型
    start_time = time.time()
    for state in test_states:
        model_no_amp.predict(state)
    no_amp_inference_time = time.time() - start_time
    print(f"不使用混合精度推理时间: {no_amp_inference_time:.4f} 秒")

    # 计算推理加速比
    inference_speedup = no_amp_inference_time / amp_inference_time if amp_inference_time > 0 else 0
    print(f"推理加速比: {inference_speedup:.2f}x")

    return {
        'training_speedup': speedup,
        'memory_saving': memory_saving,
        'inference_speedup': inference_speedup
    }


def train_efficient_zero(game: str, config: Dict[str, Any]) -> int:
    """
    训练EfficientZero模型的主函数

    Args:
        game (str): 游戏名称，例如"doudizhu"
        config (Dict[str, Any]): 训练配置，包含所有必要的超参数

    Returns:
        int: 返回状态码 (0: 成功, 非0: 错误)
    """
    try:
        # 导入必要模块
        import torch
        import os
        import glob
        import time
        import numpy as np
        from torch.utils.data import DataLoader

        # 设置日志
        logger = logging.getLogger(__name__)
        logger.info(f"开始训练 {game} 游戏的 EfficientZero 模型")

        # 提取 num_workers 参数，优先从 training 子配置获取，其次从根配置获取，最后使用默认值 4
        training_config = config.get('training', {})
        num_workers = training_config.get('num_workers', config.get('num_workers', 4))
        logger.info(f"train_efficient_zero: 使用 num_workers = {num_workers} 创建 DataLoader")

        # 提取配置参数
        model_config = config.get('model', {})
        mcts_config = config.get('mcts', {})
        training_config = config.get('training', {})
        base_config = config.get('base', {})
        environment_config = config.get('environment', {})
        opponent_modeling_config = config.get('opponent_modeling', {})
        continual_learning_config = config.get('continual_learning', {})
        rlhf_config = config.get('rlhf', {})

        # 设备配置
        device = config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {device}")

        # 加载游戏环境
        if game == 'doudizhu':
            from cardgame_ai.games.doudizhu import DouDizhuEnvironment
            game_env = DouDizhuEnvironment()
        else:
            logger.error(f"不支持的游戏类型: {game}")
            return 1

        # 获取状态和动作空间
        state_shape = game_env.state_shape
        action_shape = game_env.action_shape

        logger.info(f"状态空间形状: {state_shape}")
        logger.info(f"动作空间形状: {action_shape}")

        # 创建EfficientZero算法实例
        model = EfficientZero(
            state_shape=state_shape,
            action_shape=action_shape,
            hidden_dim=model_config.get('hidden_dim', 256),
            state_dim=model_config.get('state_dim', 64),
            use_resnet=model_config.get('use_resnet', True),
            projection_dim=model_config.get('projection_dim', 256),
            prediction_dim=model_config.get('prediction_dim', 128),
            value_prefix_length=model_config.get('value_prefix_length', 5),
            num_simulations=mcts_config.get('num_simulations', 50),
            discount=mcts_config.get('discount', 0.997),
            dirichlet_alpha=mcts_config.get('dirichlet_alpha', 0.25),
            exploration_fraction=mcts_config.get('exploration_fraction', 0.25),
            pb_c_base=mcts_config.get('pb_c_base', 19652),
            pb_c_init=mcts_config.get('pb_c_init', 1.25),
            replay_buffer_size=training_config.get('replay_buffer_size', 100000),
            batch_size=training_config.get('batch_size', 128),
            num_unroll_steps=training_config.get('num_unroll_steps', 5),
            td_steps=training_config.get('td_steps', 10),
            value_loss_weight=training_config.get('value_loss_weight', 1.0),
            policy_loss_weight=training_config.get('policy_loss_weight', 1.0),
            consistency_loss_weight=training_config.get('consistency_loss_weight', 1.0),
            self_supervised_loss_weight=training_config.get('self_supervised_loss_weight', 1.0),
            learning_rate=float(training_config.get('learning_rate', 0.001)),
            weight_decay=float(training_config.get('weight_decay', 1e-4)),
            lr_scheduler=training_config.get('lr_scheduler', 'step'),
            device=device,
            use_ewc=continual_learning_config.get('use_ewc', False),
            ewc_lambda=continual_learning_config.get('ewc_lambda', 100.0),
            amp_enabled=environment_config.get('amp_enabled', False),
            use_distributional_value=model_config.get('use_distributional_value', True)
        )

        # 输出模型结构
        logger.info(f"模型创建成功，使用 {'自动混合精度' if environment_config.get('amp_enabled', False) else '全精度'} 训练")

        # 加载预训练模型（如果有）
        pretrained_model_path = training_config.get('pretrained_model_path', None)
        if pretrained_model_path:
            if os.path.exists(pretrained_model_path):
                logger.info(f"加载预训练模型: {pretrained_model_path}")
                model.load(pretrained_model_path)
            else:
                logger.warning(f"预训练模型路径不存在: {pretrained_model_path}")

        # 设置训练参数
        num_epochs = training_config.get('num_epochs', 1000)
        episodes_per_epoch = training_config.get('episodes_per_epoch', 10)
        updates_per_epoch = training_config.get('updates_per_epoch', 100)
        save_interval = training_config.get('save_interval', 10)
        eval_interval = training_config.get('eval_interval', 10)
        log_interval = training_config.get('log_interval', 10)  # 添加日志间隔参数
        save_dir = training_config.get('save_dir', 'models/muzero_doudizhu')

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # Checkpoint 相关配置
        checkpoint_dir = config.get('checkpoint_dir', save_dir)
        checkpoint_interval = config.get('checkpoint_save_interval', training_config.get('save_interval', 1))
        os.makedirs(checkpoint_dir, exist_ok=True)
        if config.get('resume', False):
            ckpt_files = sorted(glob.glob(os.path.join(checkpoint_dir, '*.pth')))
            if ckpt_files:
                latest_ckpt = ckpt_files[-1]
                logger.info(f"恢复训练: 加载最新 checkpoint {latest_ckpt}")
                model.load(latest_ckpt)

        # 加载 RLHF 数据（如果配置启用了 RLHF）
        human_feedback_loader = None
        human_feedback_iterator = None

        if rlhf_config.get('use_rlhf', False) or model.use_rlhf:
            rlhf_data_path = rlhf_config.get('data_path')
            if rlhf_data_path:
                try:
                    from cardgame_ai.data.human_feedback_loader import load_human_preference_data
                    rlhf_batch_size = rlhf_config.get('batch_size', training_config.get('batch_size', 32))
                    # 使用提取的 num_workers 参数创建数据加载器
                    human_feedback_loader, _ = load_human_preference_data(
                        rlhf_data_path,
                        batch_size=rlhf_batch_size,
                        shuffle=True,
                        num_workers=num_workers,  # 传递提取到的 num_workers 参数
                        train_ratio=rlhf_config.get('train_ratio', 0.8)
                    )
                    human_feedback_iterator = iter(human_feedback_loader)
                    logger.info(f"RLHF 数据加载成功，使用 num_workers={num_workers}")
                except Exception as e:
                    logger.error(f"加载 RLHF 数据时出错: {e}")
                    logger.exception(e)
                    logger.warning("将继续训练，但不使用 RLHF")
            else:
                logger.warning("启用了 RLHF，但未指定数据路径，将继续训练但不使用 RLHF")

        # 初始化 EWC (Elastic Weight Consolidation)
        if continual_learning_config.get('use_ewc', False) and model.use_ewc:
            logger.info("初始化 EWC (Elastic Weight Consolidation)...")

            # 创建 EWC 数据集和加载器
            try:
                # 获取多个批次的样本
                ewc_batch_size = continual_learning_config.get('ewc_batch_size', training_config.get('batch_size', 128))
                ewc_num_batches = continual_learning_config.get('ewc_samples', 10)

                if len(model.replay_buffer) >= ewc_batch_size * ewc_num_batches:
                    from torch.utils.data import Dataset, DataLoader

                    # 创建一个包装回放缓冲区的数据集
                    class ReplayBufferDataset(Dataset):
                        def __init__(self, replay_buffer, num_samples, batch_size):
                            self.replay_buffer = replay_buffer
                            self.num_samples = num_samples
                            self.batch_size = batch_size

                        def __len__(self):
                            return self.num_samples

                        def __getitem__(self, idx):
                            # 获取一个批次，并返回其中的状态
                            batch = self.replay_buffer.sample(self.batch_size)
                            # 注意：这里需要根据实际的批次结构提取状态
                            # 假设批次的第一个元素是状态
                            states = batch[0] if isinstance(batch, (list, tuple)) else batch
                            return states

                    # 创建数据集和加载器
                    ewc_dataset = ReplayBufferDataset(model.replay_buffer, ewc_num_batches, ewc_batch_size)

                    # 创建数据加载器，使用提取的 num_workers 参数
                    ewc_dataloader = DataLoader(
                        ewc_dataset,
                        batch_size=1,  # 每次返回一个批次
                        shuffle=True,
                        num_workers=num_workers  # 使用提取的 num_workers 参数
                    )

                    # 初始化 EWC
                    model.initialize_ewc(dataloader=ewc_dataloader)
                    logger.info(f"EWC 初始化成功，使用 num_workers={num_workers}")
                else:
                    logger.warning(f"回放缓冲区样本数量不足，跳过 EWC 初始化")
            except Exception as e:
                logger.error(f"初始化 EWC 时出错: {e}")
                logger.exception(e)

        logger.info(f"训练设置: {num_epochs}轮，每轮{episodes_per_epoch}局游戏，每轮{updates_per_epoch}次更新")

        # 创建环境和收集经验
        logger.info("开始训练循环")

        # 定义训练阶段权重，用于计算总体进度
        episode_collection_weight = 0.4  # 游戏收集占轮次的40%
        update_weight = 0.6  # 模型更新占轮次的60%

        # 定义卡牌字符映射的辅助函数 (更健壮的方式是放在环境类中)
        def _get_card_char_representation(card_obj, game_env_ref):
            """
            获取卡牌的字符表示: '3'-'9','T','J','Q','K','A','2','X'(小王),'D'(大王)
            """
            try:
                # 优先使用 CardRank.to_char 方法
                rank = card_obj.rank
                if hasattr(rank, 'to_char'):
                    rank_char = rank.to_char()
                    # 将 '10' 映射为 'T'
                    if rank_char == '10':
                        rank_char = 'T'
                    return rank_char
            except Exception:
                pass
            # 备用：根据字符串表示处理
            s = str(card_obj)
            if s == '小王':
                return 'X'
            if s == '大王':
                return 'D'
            # 其他情况取最后一个字符作为点数
            if s:
                last_char = s[-1]
                if last_char == '0':  # 处理 '10'
                    return 'T'
                return last_char.upper()
            return '?'

        def format_hand_to_dx_string(hand_cards, game_env_ref):
            if not hand_cards:
                return ""

            card_chars = [_get_card_char_representation(card, game_env_ref) for card in hand_cards]

            # 定义斗地主牌的排序顺序
            # D > X > 2 > A > K > Q > J > T > 9 > 8 > 7 > 6 > 5 > 4 > 3
            order = {'D': 15, 'X': 14, '2': 13, 'A': 12, 'K': 11, 'Q': 10, 'J': 9, 'T': 8,
                     '9': 7, '8': 6, '7': 5, '6': 4, '5': 3, '4': 2, '3': 1, '?': 0}

            # 根据字符表示的牌面大小排序，从大到小
            try:
                # 过滤掉无法识别的 '?' 牌，除非它是唯一的牌
                valid_card_chars = [c for c in card_chars if c != '?']
                if not valid_card_chars and '?' in card_chars: # 如果全是问号，就显示问号
                    sorted_chars = card_chars
                else:
                    # 优先使用有效字符排序
                    sorted_chars = sorted(valid_card_chars, key=lambda c: order.get(c, 0), reverse=True)
                    # 如果有问号，附在最后
                    sorted_chars.extend([c for c in card_chars if c == '?'])

            except Exception as e_sort:
                logger.debug(f"排序卡牌字符时出错: {e_sort}. 使用原始顺序。")
                sorted_chars = card_chars # 出错则不排序

            return "".join(sorted_chars)

        for epoch in range(num_epochs):
            epoch_start_time = time.time()
            logger.info(f"开始第 {epoch+1}/{num_epochs} 轮训练")

            # Checkpoint 保存
            if (epoch+1) % checkpoint_interval == 0:
                ckpt_path = os.path.join(checkpoint_dir, f'ckpt_epoch{epoch+1}.pth')
                model.save(ckpt_path)
                logger.info(f"已保存 checkpoint: {ckpt_path}")

            # 收集经验
            logger.info(f"收集经验: {episodes_per_epoch}局游戏")
            for episode in range(episodes_per_epoch):
                # 重置游戏环境
                try:
                    state = game_env.reset()
                    # 将状态转换为模型可用的格式
                    state_array = game_env.get_observation(state)
                except Exception as e:
                    logger.error(f"游戏环境重置或状态转换时出错 [轮次 {epoch+1}/{num_epochs}, 局 {episode+1}/{episodes_per_epoch}]: {str(e)}")
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    logger.warning(f"跳过当前游戏局，继续下一局")
                    continue

                # 移除原来的初始手牌日志块

                done = False
                total_reward = 0
                step = 0  # 初始化当前游戏的步数计数器，用于更细粒度日志
                landlord_info_logged_this_episode = False # 新增标志位，确保地主信息只记录一次

                # 初始化信念追踪器和对手模型先验（如果游戏和配置需要）
                # belief_trackers = None
                # opponent_model_priors = None
                # if model.use_belief_state and hasattr(game_env, 'get_belief_trackers'):
                #     belief_trackers = game_env.get_belief_trackers()
                # if model.use_opponent_modeling and hasattr(game_env, 'get_opponent_model_priors'):
                # opponent_model_priors = game_env.get_opponent_model_priors()

                while not done:
                    step += 1  # 累加步数

                    acting_player_id = state.current_player
                    previous_game_phase_for_log = state.game_phase if hasattr(state, 'game_phase') else None
                    # state_array 在循环开始或上一轮末尾已基于当时的 state (current_player是acting_player_id) 更新
                    # game_env.get_observation(state) 不需要显式传入 player_id, 它默认使用 state.current_player

                    act_start_time = time.time()
                    act_output = model.act(
                        state_array,
                        temperature=1.0,
                        explain=True
                    )
                    action_idx = act_output[0]
                    action_probs_for_log = act_output[1]
                    mcts_data_for_log = act_output[2] if len(act_output) > 2 and act_output[2] else {}
                    act_duration = time.time() - act_start_time
                    logger.debug(f"    [局 {episode+1}/{episodes_per_epoch}] 步 {step}: P{acting_player_id} model.act 耗时: {act_duration:.4f}s")

                    legal_actions = game_env.get_legal_actions(state)
                    if not legal_actions:
                        logger.error(f"    [局 {episode+1}/{episodes_per_epoch}] 步 {step}: P{acting_player_id} 没有合法动作！状态: {state}")
                        break

                    if action_idx >= len(legal_actions):
                        logger.warning(f"    [局 {episode+1}/{episodes_per_epoch}] 步 {step}: P{acting_player_id} 模型选择非法动作 {action_idx} (合法动作数量: {len(legal_actions)})，随机选择替代。")
                        action_idx = np.random.choice(len(legal_actions))
                    action = legal_actions[action_idx]

                    # 执行动作 (环境的 step 方法会使用其内部的 current_player，即 acting_player_id)
                    try:
                        next_state, reward, done, info = game_env.step(action) # next_state 是执行action后的新状态
                    except Exception as e:
                        logger.error(f"执行动作时出错 [轮次 {epoch+1}/{num_epochs}, 局 {episode+1}/{episodes_per_epoch}, 步 {step}]: {str(e)}")
                        logger.error(f"动作信息: 玩家 {acting_player_id}, 动作索引 {action_idx}, 动作 {action}")
                        logger.error(f"错误详情: {traceback.format_exc()}")

                        # 尝试恢复 - 如果是非致命错误，可以尝试跳过这个动作，随机选择另一个动作
                        try:
                            logger.warning(f"尝试恢复 - 随机选择另一个动作")
                            if legal_actions and len(legal_actions) > 1:  # 确保有多个合法动作可选
                                # 排除当前导致错误的动作
                                alternative_actions = [a for i, a in enumerate(legal_actions) if i != action_idx]
                                if alternative_actions:
                                    recovery_action = random.choice(alternative_actions)
                                    logger.info(f"选择替代动作: {recovery_action}")
                                    next_state, reward, done, info = game_env.step(recovery_action)
                                    logger.info(f"恢复成功，继续游戏")
                                    continue

                            logger.warning(f"恢复失败，无法找到合适的替代动作")
                        except Exception as recovery_error:
                            logger.error(f"恢复尝试也失败: {str(recovery_error)}")
                            logger.error(f"错误详情: {traceback.format_exc()}")

                        logger.warning(f"中断当前游戏局，继续下一局")
                        break

                    # === 日志记录 P{acting_player_id} 执行的动作 ===
                    try:
                        acting_player_hand_after_action_str = "未知手牌"
                        # 手牌应该显示该玩家执行动作 *之后* 的手牌 (从 next_state 中获取)
                        if hasattr(next_state, 'hands') and next_state.hands and len(next_state.hands) > acting_player_id:
                            cards_to_format = next_state.hands[acting_player_id]
                            if not cards_to_format and done:
                                acting_player_hand_after_action_str = "已出完"
                            else:
                                acting_player_hand_after_action_str = format_hand_to_dx_string(cards_to_format, game_env)

                        action_to_log_str = str(action)
                        if hasattr(action, 'cards') and action.cards:
                            action_to_log_str = format_hand_to_dx_string(action.cards, game_env)
                        elif hasattr(action, 'name'):
                            action_to_log_str = action.name
                        elif not hasattr(action, 'cards') or not action.cards :
                             action_to_log_str = "PASS"

                        phase_str_for_log = ''
                        if previous_game_phase_for_log:
                            if previous_game_phase_for_log == GamePhase.BIDDING: phase_str_for_log = '叫分'
                            elif previous_game_phase_for_log == GamePhase.GRABBING: phase_str_for_log = '抢地主'
                            elif previous_game_phase_for_log == GamePhase.PLAYING: phase_str_for_log = '出牌'
                        else:
                            if isinstance(action, BidAction): phase_str_for_log = '叫分'
                            elif isinstance(action, GrabAction): phase_str_for_log = '抢地主'
                            elif hasattr(action, 'cards'): phase_str_for_log = '出牌'

                        current_step_for_log = step # 记录当前算法的迭代步数

                        # 游戏状态的文本描述，基于动作执行后的状态 next_state
                        game_status_for_log = "进行中"
                        if done: # done 是基于 next_state 判断的
                            game_status_for_log = f"结束 (P{acting_player_id}行动导致)"
                            payoffs = next_state.get_payoffs()
                            winner_log_info = ""
                            if payoffs[acting_player_id] > 0 :
                                winner_log_info = f"P{acting_player_id} 获胜"
                            elif next_state.landlord is not None:
                                if payoffs[next_state.landlord] > 0:
                                     winner_log_info = f"地主 P{next_state.landlord} 获胜"
                                else:
                                    farmer_winners = [p for p, pay in enumerate(payoffs) if p != next_state.landlord and pay > 0]
                                    if farmer_winners:
                                        winner_log_info = f"农民 P{farmer_winners} 获胜"
                            if winner_log_info:
                                game_status_for_log += f" - {winner_log_info}"

                        # 获取合法动作数量
                        legal_actions_count = len(legal_actions) if legal_actions else 0

                        logger.info(f"    [局 {episode+1}/{episodes_per_epoch}] 步 {current_step_for_log} ({phase_str_for_log}阶段): P{acting_player_id} 动作 {action_idx} ({action_to_log_str}), 奖励 {reward:.2f}, 合法动作数: {legal_actions_count}, 剩余手牌: [{acting_player_hand_after_action_str}], 状态 {game_status_for_log}")

                    except Exception as e_log:
                        logger.error(f"记录游戏步骤日志时发生错误: {e_log}", exc_info=True)

                    # === 新增/修改：地主确定和阶段转换的日志 ===
                    # 检查是否刚从叫分/抢地主阶段转换到出牌阶段，并且地主信息尚未记录
                    just_transitioned_to_play = (previous_game_phase_for_log in [GamePhase.BIDDING, GamePhase.GRABBING] and
                                                next_state.game_phase == GamePhase.PLAYING)

                    if not landlord_info_logged_this_episode and next_state.landlord is not None:
                        landlord_id_log = next_state.landlord
                        landlord_cards_val_log = next_state.landlord_cards
                        landlord_cards_str_log = format_hand_to_dx_string(landlord_cards_val_log, game_env)

                        transition_message = ""
                        if just_transitioned_to_play:
                            transition_message = f" (P{acting_player_id}的动作导致转换). "

                        logger.info(f"    [局 {episode+1}/{episodes_per_epoch}] 地主确定: 玩家 {landlord_id_log}，底牌: [{landlord_cards_str_log}].{transition_message}阶段转为出牌，当前由 P{next_state.current_player} 行动.")
                        landlord_info_logged_this_episode = True
                    # === 日志修改结束 ===

                    next_state_array = game_env.get_observation(next_state)
                    total_reward += reward
                    model.store_experience(state_array, action_idx, reward, next_state_array, done)

                    state = next_state
                    state_array = next_state_array

                # 局结束后的详细日志
                # 计算每个玩家的奖励统计（基于最终游戏状态）
                player_rewards = [0.0, 0.0, 0.0]  # P0, P1, P2的累计奖励
                player_actions = [0, 0, 0]  # 每个玩家的动作次数（暂时无法精确统计）

                # 从最终状态获取每个玩家的奖励
                if hasattr(next_state, 'get_payoffs'):
                    payoffs = next_state.get_payoffs()
                    for i in range(min(3, len(payoffs))):
                        player_rewards[i] = payoffs[i]

                # 简单估算动作数（基于总步数平均分配）
                avg_actions = step // 3 if step > 0 else 0
                for i in range(3):
                    player_actions[i] = avg_actions

                # 获取游戏结果信息
                winner_info = ""
                landlord_info = ""
                if hasattr(next_state, 'landlord') and next_state.landlord is not None:
                    landlord_info = f"地主: P{next_state.landlord}"

                    # 确定获胜者
                    payoffs = next_state.get_payoffs()
                    for i, payoff in enumerate(payoffs):
                        if payoff > 0:
                            role = "地主" if i == next_state.landlord else "农民"
                            winner_info = f"获胜: P{i}({role})"
                            break

                # 输出详细的局结束日志
                logger.info(f"  完成局 {episode+1}/{episodes_per_epoch}，共 {step} 步")
                logger.info(f"    {landlord_info}, {winner_info}")

                # 输出每个玩家的详细信息
                for i in range(3):
                    role = ""
                    if hasattr(next_state, 'landlord') and next_state.landlord is not None:
                        role = "地主" if i == next_state.landlord else "农民"

                    logger.info(f"    P{i}({role}): 奖励 {player_rewards[i]:+.2f}, 动作数 {player_actions[i]}")

                logger.info(f"    总奖励: {total_reward:.2f}")

                # 添加标准格式的进度信息，每隔10局游戏输出一次进度
                if (episode + 1) % 10 == 0 or episode == episodes_per_epoch - 1:
                    # 计算当前轮次内的进度
                    episode_progress = (episode + 1) / episodes_per_epoch * 100
                    # 计算总体训练进度
                    total_progress = ((epoch * episodes_per_epoch + episode + 1) /
                                    (num_epochs * episodes_per_epoch) * 100)
                    logger.info(f"回合 {episode+1}/{episodes_per_epoch}")
                    logger.info(f"进度: {total_progress:.1f}%")
                    logger.info(f"阶段进度: {episode_progress:.1f}%")
                    logger.info(f"奖励: {total_reward:.2f}")

            # 更新模型
            logger.info(f"更新模型: {updates_per_epoch}次")
            total_loss = 0
            for update in range(updates_per_epoch):
                # 从经验回放缓冲区中采样批次
                try:
                    batch = model.sample_batch()

                    # 如果批次为空，跳过此次更新
                    if batch is None:
                        logger.warning("批次为空，跳过更新")
                        continue
                except Exception as e:
                    import traceback  # 在使用前导入traceback模块
                    logger.error(f"从经验回放缓冲区采样批次时出错 [轮次 {epoch+1}/{num_epochs}, 更新 {update+1}/{updates_per_epoch}]: {str(e)}")
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    logger.warning("跳过当前更新，继续下一次更新")
                    continue

                # 如果有 RLHF 数据，获取一个批次用于训练
                if human_feedback_loader is not None:
                    try:
                        # 尝试从迭代器获取下一个批次
                        try:
                            human_feedback_batch = next(human_feedback_iterator)
                        except StopIteration:
                            # 如果迭代器已耗尽，重新创建
                            human_feedback_iterator = iter(human_feedback_loader)
                            human_feedback_batch = next(human_feedback_iterator)

                        # 使用 RLHF 数据更新模型
                        try:
                            metrics = model.train(batch, human_feedback_batch=human_feedback_batch)
                        except Exception as e:
                            import traceback  # 在使用前导入traceback模块
                            logger.error(f"使用RLHF数据训练模型时出错 [轮次 {epoch+1}/{num_epochs}, 更新 {update+1}/{updates_per_epoch}]: {str(e)}")
                            logger.error(f"错误详情: {traceback.format_exc()}")
                            logger.warning("尝试不使用RLHF数据进行训练")
                            try:
                                metrics = model.train(batch)
                            except Exception as e2:
                                import traceback  # 在使用前导入traceback模块
                                logger.error(f"退回到不使用RLHF的训练也失败 [轮次 {epoch+1}/{num_epochs}, 更新 {update+1}/{updates_per_epoch}]: {str(e2)}")
                                logger.error(f"错误详情: {traceback.format_exc()}")
                                logger.warning("跳过当前更新，继续下一次更新")
                                # 创建一个空的指标字典，以便后续代码能够继续执行
                                metrics = {'total_loss': 0.0, 'value_loss': 0.0, 'policy_loss': 0.0}
                                continue
                    except Exception as e:
                        logger.warning(f"获取或使用 RLHF 数据时出错: {str(e)}")
                        # 如果出错，退回到不使用 RLHF 的训练
                        try:
                            metrics = model.train(batch)
                        except Exception as e2:
                            import traceback  # 在使用前导入traceback模块
                            logger.error(f"退回到不使用RLHF的训练也失败 [轮次 {epoch+1}/{num_epochs}, 更新 {update+1}/{updates_per_epoch}]: {str(e2)}")
                            logger.error(f"错误详情: {traceback.format_exc()}")
                            logger.warning("跳过当前更新，继续下一次更新")
                            # 创建一个空的指标字典，以便后续代码能够继续执行
                            metrics = {'total_loss': 0.0, 'value_loss': 0.0, 'policy_loss': 0.0}
                            continue
                else:
                    # 没有 RLHF 数据，正常训练
                    try:
                        metrics = model.train(batch)
                    except Exception as e:
                        import traceback  # 在使用前导入traceback模块
                        logger.error(f"模型训练时出错 [轮次 {epoch+1}/{num_epochs}, 更新 {update+1}/{updates_per_epoch}]: {str(e)}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        logger.warning("跳过当前更新，继续下一次更新")
                        # 创建一个空的指标字典，以便后续代码能够继续执行
                        metrics = {'total_loss': 0.0, 'value_loss': 0.0, 'policy_loss': 0.0}
                        continue

                # 累计损失
                total_loss += metrics.get('total_loss', 0)

                # 每10次更新输出一次日志
                if (update + 1) % 10 == 0:
                    metrics_str = ', '.join([f"{k}: {v:.4f}" for k, v in metrics.items()])
                    logger.info(f"更新 {update+1}/{updates_per_epoch}, {metrics_str}")

                    # 添加标准格式的进度信息
                    # 计算当前更新进度
                    update_progress = (update + 1) / updates_per_epoch * 100
                    # 计算总体训练进度 (考虑轮次、游戏和更新)
                    # 为简化计算，我们假设每个轮次的权重为: 游戏收集占40%，模型更新占60%
                    epoch_progress = ((epoch + (episode_collection_weight * 1.0) +
                                     (update_weight * (update + 1) / updates_per_epoch)) /
                                    num_epochs * 100)
                    logger.info(f"更新 {update+1}/{updates_per_epoch}")
                    logger.info(f"进度: {epoch_progress:.1f}%")
                    logger.info(f"更新进度: {update_progress:.1f}%")
                    if 'total_loss' in metrics:
                        logger.info(f"损失: {metrics['total_loss']:.6f}")

            # 计算平均损失
            avg_loss = total_loss / updates_per_epoch if updates_per_epoch > 0 else 0

            # 记录轮次信息
            epoch_time = time.time() - epoch_start_time
            logger.info(f"第 {epoch+1}/{num_epochs} 轮完成, 平均损失: {avg_loss:.4f}, 用时: {epoch_time:.2f}秒")

            # 定期保存模型
            if (epoch + 1) % save_interval == 0:
                save_path = os.path.join(save_dir, f"model_epoch_{epoch+1}.pt")
                logger.info(f"保存模型到: {save_path}")
                model.save(save_path)

            # 进度
            progress = (epoch + 1) / num_epochs * 100
            logger.info(f"训练进度: {progress:.1f}%")

            # 添加标准格式的进度信息，确保能被外部脚本的正则表达式捕获
            logger.info(f"第 {epoch+1}/{num_epochs} 轮训练完成")
            logger.info(f"进度: {progress:.1f}%")
            logger.info(f"损失: {avg_loss:.6f}")
            if isinstance(metrics, dict) and 'value_loss' in metrics:
                logger.info(f"价值损失: {metrics['value_loss']:.6f}")
            if isinstance(metrics, dict) and 'policy_loss' in metrics:
                logger.info(f"策略损失: {metrics['policy_loss']:.6f}")

            # 如果有GPU监控，添加显存使用信息
            try:
                if torch.cuda.is_available():
                    gpu_memory = torch.cuda.max_memory_allocated() / (1024**3)  # 转换为GB
                    logger.info(f"显存使用: {gpu_memory:.2f}GB")
                    torch.cuda.reset_peak_memory_stats()  # 重置峰值统计
            except Exception as e:
                logger.debug(f"无法获取GPU内存使用信息: {e}")

        # 保存最终模型
        try:
            final_save_path = os.path.join(save_dir, "model_final.pt")
            logger.info(f"保存最终模型到: {final_save_path}")
            model.save(final_save_path)
            logger.info("最终模型保存成功")
        except Exception as e:
            import traceback  # 在使用前导入traceback模块
            logger.error(f"保存最终模型时出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            logger.warning("无法保存最终模型，但训练过程已完成")

        logger.info("训练完成!")
        return 0

    except Exception as e:
        logger.error(f"训练过程中出现严重错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        # 尝试保存当前模型状态，以便后续恢复
        try:
            emergency_save_path = os.path.join(checkpoint_dir, f'emergency_save_{int(time.time())}.pth')
            logger.warning(f"尝试紧急保存模型状态到: {emergency_save_path}")
            model.save(emergency_save_path)
            logger.info(f"紧急保存成功，可以使用此文件恢复训练: {emergency_save_path}")
        except Exception as save_error:
            logger.error(f"紧急保存模型失败: {str(save_error)}")
            logger.error(traceback.format_exc())

        # 记录当前训练状态信息，便于后续恢复
        logger.info(f"训练中断时的状态: 轮次 {epoch+1}/{num_epochs}")

        return 1


if __name__ == "__main__":
    # 如果直接运行这个文件，则执行测试
    print("=== 测试EfficientZeroAMP性能 ===")
    test_efficient_zero_amp()

    print("\n\n=== 测试重要性加权训练功能 ===")
    test_importance_weighted_training()